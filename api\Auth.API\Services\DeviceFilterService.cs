using Auth.API.DBContext;
using Auth.API.Repository.Models;
using Auth.API.Types;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Auth.API.Services
{
    public interface IDeviceFilterService
    {
        Task<List<RepoData>> FilterDataByUserDeviceConfigurationsAsync(List<RepoData> datas, Guid userId);
        Task<List<RealTimeData>> FilterDataByUserDeviceConfigurationsAsync(List<RealTimeData> datas, Guid userId);
    }

    public class DeviceFilterService : IDeviceFilterService
    {
        private readonly AppDbContext _context;
        private readonly ILogger<DeviceFilterService> _logger;

        public DeviceFilterService(AppDbContext context, ILogger<DeviceFilterService> logger)
        {
            _context = context;
            _logger = logger;
        }

        /// <summary>
        /// Filters RepoData based on user's device configurations
        /// Returns data for devices that either:
        /// 1. Have no device configuration (not configured by user)
        /// 2. Have device configuration with DisplayInCharts = true
        /// </summary>
        public async Task<List<RepoData>> FilterDataByUserDeviceConfigurationsAsync(List<RepoData> datas, Guid userId)
        {
            if (datas == null || !datas.Any())
                return new List<RepoData>();

            var filteredAllData = new List<RealTimeData>();

            foreach (RepoData data in datas)
            {
                var repoData = JsonConvert.DeserializeObject<HistoricTimeData>(data.Data);
                // Get all device IDs from the data
                var deviceIds = repoData.Data.Select(d => d.name).Distinct().ToList();

                // Get user's device configurations for these devices that have DisplayInCharts = false

                var hiddenDeviceIds = await _context.DeviceConfigurations
                    .Include(dc => dc.StationConfiguration)
                    .Where(dc => deviceIds.Contains(dc.SerialNumber) &&
                               dc.StationConfiguration.UserId == userId &&
                               dc.DisplayInCharts == false)
                    .Select(dc => dc.SerialNumber)
                    .ToListAsync();

                // Filter data to exclude devices that are explicitly hidden (DisplayInCharts = false)
                // This means we include:
                // 1. Devices with no configuration (not in DeviceConfigurations table)
                // 2. Devices with DisplayInCharts = true (not in hiddenDeviceIds list)
                var filteredData = repoData.Data.Where(d => !hiddenDeviceIds.Contains(d.name)).ToList();


                
                var userDeviceConfigs = await _context.DeviceConfigurations
                    .Include(dc => dc.StationConfiguration)
                    .Where(dc => deviceIds.Contains(dc.SerialNumber) &&
                                 dc.StationConfiguration.UserId == userId)
                    .ToListAsync();

                
                // Use composite key (SerialNumber + Id) to ensure uniqueness
                var deviceConfigDict = userDeviceConfigs
                        .ToDictionary(dc => $"{dc.SerialNumber}_{dc.Id}", dc => dc.SKeyeName);

                // Also create a lookup by SerialNumber only for backwards compatibility
                var deviceConfigBySerialNumber = userDeviceConfigs
                        .GroupBy(dc => dc.SerialNumber)
                        .ToDictionary(g => g.Key, g => g.First().SKeyeName); // Take first if multiple


                filteredData.ForEach(fdata => {
                    // Try to find SKeyeName by SerialNumber (backwards compatibility)
                    if (deviceConfigBySerialNumber.TryGetValue(fdata.name, out var skeyeName) &&
                        !string.IsNullOrWhiteSpace(skeyeName))
                    {
                        fdata.name = skeyeName;
                    }


                });

                _logger.LogInformation($"Filtered {datas.Count} RepoData entries to {filteredData.Count} based on user device configurations for user {userId}. Hidden devices: {hiddenDeviceIds.Count}");
                
                repoData.Data = filteredData;
                repoData.Sum = filteredData.Sum(s => s.activePower);

                data.Data =  JsonConvert.SerializeObject(repoData);

            }
            return datas;
        }


        /// <summary>
        /// Filters RealTimeData based on user's device configurations
        /// Returns data for devices that either:
        /// 1. Have no device configuration (not configured by user)
        /// 2. Have device configuration with DisplayInCharts = true
        /// </summary>
        public async Task<List<RealTimeData>> FilterDataByUserDeviceConfigurationsAsync(List<RealTimeData> datas, Guid userId)
        {
            if (datas == null || !datas.Any())
                return new List<RealTimeData>();

            var filteredAllData = new List<RealTimeData>();

            foreach (RealTimeData data in datas)
            {
                // Get all device IDs from the data
                var deviceIds = datas.Select(d => d.name).Distinct().ToList();

                // Get user's device configurations for these devices that have DisplayInCharts = false

                var hiddenDeviceIds = await _context.DeviceConfigurations
                    .Include(dc => dc.StationConfiguration)
                    .Where(dc => deviceIds.Contains(dc.SerialNumber) &&
                               dc.StationConfiguration.UserId == userId &&
                               dc.DisplayInCharts == false)
                    .Select(dc => dc.SerialNumber)
                    .ToListAsync();

                // Filter data to exclude devices that are explicitly hidden (DisplayInCharts = false)
                // This means we include:
                // 1. Devices with no configuration (not in DeviceConfigurations table)
                // 2. Devices with DisplayInCharts = true (not in hiddenDeviceIds list)
                var filteredData = datas.Where(d => !hiddenDeviceIds.Contains(d.name)).ToList();



                var userDeviceConfigs = await _context.DeviceConfigurations
                    .Include(dc => dc.StationConfiguration)
                    .Where(dc => deviceIds.Contains(dc.SerialNumber) &&
                                 dc.StationConfiguration.UserId == userId)
                    .ToListAsync();


                // Use composite key (SerialNumber + Id) to ensure uniqueness
                var deviceConfigDict = userDeviceConfigs
                        .ToDictionary(dc => $"{dc.SerialNumber}_{dc.Id}", dc => dc.SKeyeName);

                // Also create a lookup by SerialNumber only for backwards compatibility
                var deviceConfigBySerialNumber = userDeviceConfigs
                        .GroupBy(dc => dc.SerialNumber)
                        .ToDictionary(g => g.Key, g => g.First().SKeyeName); // Take first if multiple


                filteredData.ForEach(fdata => {
                    // Try to find SKeyeName by SerialNumber (backwards compatibility)
                    if (deviceConfigBySerialNumber.TryGetValue(fdata.name, out var skeyeName) &&
                        !string.IsNullOrWhiteSpace(skeyeName))
                    {
                        fdata.name = skeyeName;
                    }


                });

                _logger.LogInformation($"Filtered {datas.Count} RepoData entries to {filteredData.Count} based on user device configurations for user {userId}. Hidden devices: {hiddenDeviceIds.Count}");

                datas = filteredData;                

            }
            return datas;
        }
    }
}
