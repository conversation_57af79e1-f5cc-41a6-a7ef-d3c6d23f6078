import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Product } from '../api/product';
import { Station } from '../api/station';
import { InvertPower } from '../api/invertpower';
import { GetHistoricDataResponse, GetProvidersResponse, GetStationDevicesResponse, GetStationsResponse, GetStationSumDataResponse, GetUserProvidersResponse, IProvider, IUserProvider, IUserProviderConfiguration, SaveUserProvidersResponse } from '../api/responses';
import { Device } from '../api/device';
import { GetHistoricDataRequest, GetStationDevicesRequest, GetStationSumDataRequest, SaveUserProvidersRequest } from '../api/requests';
import { environment } from '../../../environments/environment';


@Injectable()
export class ProvidersService {

    

    constructor(private http: HttpClient) { }

    
    getProviders(): Promise<IProvider[]> {
        return this.http.get<GetProvidersResponse>(environment.solarApi + 'api/providers')
            .toPromise()
            .then(res => res.providers) // Προσαρμογή στη δομή της απόκρισης
            .catch(error => {
                console.error('Error fetching providers:', error);
                return [];
            });
    }

    getUserProviders(): Promise<IUserProviderConfiguration[]> {
        return this.http.get<GetUserProvidersResponse>(environment.solarApi + 'api/user/providers')
            .toPromise()
            .then(res => res.providers) // Προσαρμογή στη δομή της απόκρισης
            .catch(error => {
                console.error('Error fetching user providers:', error);
                return [];
            });
    }

    saveUserProviders(request: SaveUserProvidersRequest): Promise<SaveUserProvidersResponse> {
        return this.http.post<SaveUserProvidersResponse>(
                environment.solarApi + 'api/user/providers',
                request
            )
            .toPromise()
            .catch(error => {
                console.error('Error saving user providers:', error);
                throw error; // Ή επιστρέφεις ένα default αντικείμενο αν χρειάζεται
            });
    }

    // Update individual user provider (delete + add since no PUT endpoint exists)
    async updateUserProvider(providerId: number, configuration: string): Promise<any> {
        try {
            // First delete the existing provider
            await this.deleteUserProvider(providerId);

            // Then add it back with new configuration
            return await this.http.post(
                `${environment.solarApi}api/UserProviders/${this.getCurrentUserId()}/${providerId}`,
                configuration,
                {
                    headers: { 'Content-Type': 'application/json' }
                }
            ).toPromise();
        } catch (error) {
            console.error('Error updating user provider:', error);
            throw error;
        }
    }

    // Delete individual user provider
    deleteUserProvider(providerId: number): Promise<any> {
        return this.http.delete(
                `${environment.solarApi}api/UserProviders/${this.getCurrentUserId()}/${providerId}`
            )
            .toPromise()
            .catch(error => {
                console.error('Error deleting user provider:', error);
                throw error;
            });
    }

    // Helper method to get current user ID from JWT token
    private getCurrentUserId(): string {
        // Since the backend uses JWT authentication and gets userId from claims,
        // we don't need to pass userId explicitly - the backend will extract it from the token
        // But the API expects userId in the URL, so we'll use a placeholder that the backend ignores
        return 'me'; // The backend will use the actual userId from JWT claims
    }
    
}
