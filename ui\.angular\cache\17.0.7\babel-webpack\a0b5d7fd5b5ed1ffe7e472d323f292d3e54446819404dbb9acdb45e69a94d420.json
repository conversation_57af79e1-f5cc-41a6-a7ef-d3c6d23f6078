{"ast": null, "code": "import { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"../../service/cache.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"primeng/button\";\nimport * as i9 from \"primeng/breadcrumb\";\nimport * as i10 from \"primeng/tooltip\";\nimport * as i11 from \"@fortawesome/angular-fontawesome\";\nimport * as i12 from \"primeng/card\";\nimport * as i13 from \"primeng/inputtext\";\nimport * as i14 from \"primeng/dropdown\";\nimport * as i15 from \"primeng/password\";\nimport * as i16 from \"primeng/badge\";\nimport * as i17 from \"primeng/tag\";\nimport * as i18 from \"primeng/message\";\nfunction ProvidersComponent_ng_template_5_p_badge_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 9);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r4.userProviders.length);\n  }\n}\nfunction ProvidersComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"fa-icon\", 6);\n    i0.ɵɵelementStart(2, \"h2\", 7);\n    i0.ɵɵtext(3, \"Your Providers\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ProvidersComponent_ng_template_5_p_badge_4_Template, 1, 1, \"p-badge\", 8);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.userProviders.length > 0);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_0_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵelement(1, \"fa-icon\", 17);\n    i0.ɵɵelementStart(2, \"span\");\n    i0.ɵɵtext(3, \"You are not registered to any provider. You can start adding providers now!\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12);\n    i0.ɵɵelement(1, \"fa-icon\", 13);\n    i0.ɵɵelementStart(2, \"p-message\", 14);\n    i0.ɵɵtemplate(3, ProvidersComponent_ng_template_6_div_0_ng_template_3_Template, 4, 0, \"ng-template\", 15);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"closable\", false);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23);\n    i0.ɵɵelement(2, \"p-tag\", 24)(3, \"p-badge\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const provider_r9 = i0.ɵɵnextContext().$implicit;\n    const ctx_r11 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", ctx_r11.getSelectedProviderName(provider_r9.providerId));\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r9 = i0.ɵɵnextContext(3).$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", provider_r9.configuration.Stations[0].StationName, \" \");\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"label\", 28);\n    i0.ɵɵelement(2, \"fa-icon\", 33);\n    i0.ɵɵtext(3, \" Station \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_div_4_Template, 2, 1, \"div\", 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r9 = i0.ɵɵnextContext(2).$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", provider_r9.configuration.Stations.length > 0);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"label\", 28);\n    i0.ɵɵelement(3, \"fa-icon\", 29);\n    i0.ɵɵtext(4, \" Username \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 30);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 27)(8, \"label\", 28);\n    i0.ɵɵelement(9, \"fa-icon\", 31);\n    i0.ɵɵtext(10, \" Password \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 30);\n    i0.ɵɵtext(12, \"\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\\u2022\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(13, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_Template, 5, 1, \"div\", 32);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(provider_r9.configuration.Username);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", provider_r9.configuration.Stations);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"p-button\", 36);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template_p_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const i_r10 = i0.ɵɵnextContext().index;\n      const ctx_r20 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r20.editProvider(i_r10));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"p-button\", 37);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template_p_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r22);\n      const i_r10 = i0.ɵɵnextContext().index;\n      const ctx_r23 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r23.removeProvider(i_r10));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"disabled\", true);\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"p-card\", 20);\n    i0.ɵɵtemplate(2, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_2_Template, 4, 1, \"ng-template\", 3)(3, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_Template, 14, 2, \"ng-template\", 4)(4, ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template, 3, 2, \"ng-template\", 21);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProvidersComponent_ng_template_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0);\n    i0.ɵɵtemplate(1, ProvidersComponent_ng_template_6_div_1_div_1_Template, 5, 0, \"div\", 18);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.userProviders);\n  }\n}\nfunction ProvidersComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ProvidersComponent_ng_template_6_div_0_Template, 4, 1, \"div\", 10)(1, ProvidersComponent_ng_template_6_div_1_Template, 2, 1, \"div\", 11);\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userProviders.length === 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userProviders.length > 0);\n  }\n}\nfunction ProvidersComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5);\n    i0.ɵɵelement(1, \"fa-icon\", 38);\n    i0.ɵɵelementStart(2, \"h2\", 7);\n    i0.ɵɵtext(3, \"Add New Providers\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 59)(1, \"div\", 16);\n    i0.ɵɵelement(2, \"fa-icon\", 60);\n    i0.ɵɵelementStart(3, \"span\", 61);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"p-button\", 62);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_10_div_35_ng_template_2_Template_p_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r34);\n      const i_r28 = i0.ɵɵnextContext().index;\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.removeProvider(i_r28));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r28 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Provider Configuration \", i_r28 + 1, \"\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"text\", true);\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_3_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 27)(2, \"label\", 64);\n    i0.ɵɵelement(3, \"fa-icon\", 72);\n    i0.ɵɵtext(4, \" Portfolio ID * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 73);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r28 = i0.ɵɵnextContext(2).index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"portfolio-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"portfolio-\", i_r28, \"\");\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_3_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 74)(1, \"div\", 27)(2, \"label\", 64);\n    i0.ɵɵelement(3, \"fa-icon\", 75);\n    i0.ɵɵtext(4, \" FTP URL * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"input\", 76);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r28 = i0.ɵɵnextContext(2).index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"ftp-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"ftp-\", i_r28, \"\");\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_3_div_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 27)(2, \"label\", 64);\n    i0.ɵɵelement(3, \"fa-icon\", 33);\n    i0.ɵɵtext(4, \" Station * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"p-dropdown\", 78);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(2);\n    const i_r28 = ctx_r41.index;\n    const provider_r27 = ctx_r41.$implicit;\n    let tmp_2_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"station-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"station-\", i_r28, \"\");\n    i0.ɵɵproperty(\"options\", (tmp_2_0 = provider_r27.get(\"stations\")) == null ? null : tmp_2_0.value)(\"showClear\", true);\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 63)(2, \"div\", 27)(3, \"label\", 64);\n    i0.ɵɵelement(4, \"fa-icon\", 65);\n    i0.ɵɵtext(5, \" Provider * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(6, \"p-dropdown\", 66);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 63)(8, \"div\", 27)(9, \"label\", 64);\n    i0.ɵɵelement(10, \"fa-icon\", 29);\n    i0.ɵɵtext(11, \" Username * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 67);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 63)(14, \"div\", 27)(15, \"label\", 64);\n    i0.ɵɵelement(16, \"fa-icon\", 31);\n    i0.ɵɵtext(17, \" Password * \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"p-password\", 68);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(19, ProvidersComponent_ng_template_10_div_35_ng_template_3_div_19_Template, 6, 2, \"div\", 69)(20, ProvidersComponent_ng_template_10_div_35_ng_template_3_div_20_Template, 6, 2, \"div\", 70)(21, ProvidersComponent_ng_template_10_div_35_ng_template_3_div_21_Template, 6, 4, \"div\", 71);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r42 = i0.ɵɵnextContext();\n    const i_r28 = ctx_r42.index;\n    const provider_r27 = ctx_r42.$implicit;\n    const ctx_r30 = i0.ɵɵnextContext(2);\n    let tmp_10_0;\n    let tmp_11_0;\n    let tmp_12_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"provider-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"provider-\", i_r28, \"\");\n    i0.ɵɵproperty(\"options\", ctx_r30.availableProviders)(\"showClear\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"username-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"username-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"for\", \"password-\", i_r28, \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵpropertyInterpolate1(\"id\", \"password-\", i_r28, \"\");\n    i0.ɵɵproperty(\"toggleMask\", true)(\"feedback\", false);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = provider_r27.get(\"providerId\")) == null ? null : tmp_10_0.value) == 4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = provider_r27.get(\"providerId\")) == null ? null : tmp_11_0.value) == 6);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_12_0 = provider_r27.get(\"stations\")) == null ? null : tmp_12_0.value.length) > 0);\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_4_p_button_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r46 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"p-button\", 80);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_10_div_35_ng_template_4_p_button_1_Template_p_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r46);\n      const i_r28 = i0.ɵɵnextContext(2).index;\n      const ctx_r44 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r44.getStations(i_r28));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, ProvidersComponent_ng_template_10_div_35_ng_template_4_p_button_1_Template, 1, 0, \"p-button\", 79);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const provider_r27 = i0.ɵɵnextContext().$implicit;\n    const ctx_r31 = i0.ɵɵnextContext(2);\n    let tmp_0_0;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_0_0 = provider_r27.get(\"providerId\")) == null ? null : tmp_0_0.value) && ctx_r31.userProvidersForm.valid);\n  }\n}\nfunction ProvidersComponent_ng_template_10_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"p-card\");\n    i0.ɵɵtemplate(2, ProvidersComponent_ng_template_10_div_35_ng_template_2_Template, 6, 2, \"ng-template\", 3)(3, ProvidersComponent_ng_template_10_div_35_ng_template_3_Template, 22, 13, \"ng-template\", 4)(4, ProvidersComponent_ng_template_10_div_35_ng_template_4_Template, 2, 1, \"ng-template\", 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const i_r28 = ctx.index;\n    i0.ɵɵproperty(\"formGroupName\", i_r28);\n  }\n}\nfunction ProvidersComponent_ng_template_10_p_button_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-button\", 81);\n  }\n}\nfunction ProvidersComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r49 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵelement(2, \"fa-icon\", 41);\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"h4\", 43);\n    i0.ɵɵtext(5, \"Form Instructions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"ul\", 44)(7, \"li\", 45);\n    i0.ɵɵelement(8, \"fa-icon\", 46);\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"Fill in all required fields marked with an asterisk (*).\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"li\", 45);\n    i0.ɵɵelement(12, \"fa-icon\", 47);\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14, \" If you select Provider \");\n    i0.ɵɵelementStart(15, \"strong\");\n    i0.ɵɵtext(16, \"Aurora\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \", the Portfolio ID field becomes required. You can find it in the Aurora Portal. \");\n    i0.ɵɵelement(18, \"fa-icon\", 48);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"li\", 45);\n    i0.ɵɵelement(20, \"fa-icon\", 49);\n    i0.ɵɵelementStart(21, \"span\");\n    i0.ɵɵtext(22, \" If you select Provider \");\n    i0.ɵɵelementStart(23, \"strong\");\n    i0.ɵɵtext(24, \"SMA\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(25, \", the FTP Url becomes required. It should be like \");\n    i0.ɵɵelementStart(26, \"em\");\n    i0.ɵɵtext(27, \"ftp://ftp.server.com/\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(28, \". Please use the full path where the stations folders are located. \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"li\", 50);\n    i0.ɵɵelement(30, \"fa-icon\", 51);\n    i0.ɵɵelementStart(31, \"span\");\n    i0.ɵɵtext(32, \"Make sure to select one station from the available list.\");\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(33, \"form\", 52);\n    i0.ɵɵlistener(\"ngSubmit\", function ProvidersComponent_ng_template_10_Template_form_ngSubmit_33_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r48 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r48.onSubmit());\n    });\n    i0.ɵɵelementStart(34, \"div\", 53);\n    i0.ɵɵtemplate(35, ProvidersComponent_ng_template_10_div_35_Template, 5, 1, \"div\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 55)(37, \"p-button\", 56);\n    i0.ɵɵlistener(\"click\", function ProvidersComponent_ng_template_10_Template_p_button_click_37_listener() {\n      i0.ɵɵrestoreView(_r49);\n      const ctx_r50 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r50.addProvider());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(38, ProvidersComponent_ng_template_10_p_button_38_Template, 1, 0, \"p-button\", 57);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(33);\n    i0.ɵɵproperty(\"formGroup\", ctx_r3.userProvidersForm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.providers.controls);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.userProvidersForm.valid);\n  }\n}\nconst _c0 = () => ({\n  label: \"Providers\"\n});\nconst _c1 = a0 => [a0];\nconst _c2 = () => ({\n  icon: \"pi pi-home\"\n});\nexport class ProvidersComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, cacheService) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.cacheService = cacheService;\n    this.availableProviders = [];\n    this.userProviders = [];\n    this.stations = [];\n    this.tooltipVisible = false;\n    this.editingProvider = null;\n    this.editForm = null;\n    this.userProvidersForm = this.fb.group({\n      providers: this.fb.array([])\n    });\n  }\n  ngOnInit() {\n    this.providersService.getProviders().then(data => {\n      this.availableProviders = data;\n    });\n    this.getUserProviders();\n    this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\n  }\n\n  addProvider() {\n    const providerGroup = this.fb.group({\n      providerId: ['', Validators.required],\n      username: ['', Validators.required],\n      password: ['', Validators.required],\n      portfolioId: [''],\n      ftpUrl: [''],\n      stations: [[]],\n      selectedStation: ['']\n    });\n    this.providers.push(providerGroup);\n    // Παρακολούθηση του providerId\n    providerGroup.get('providerId')?.valueChanges.subscribe(value => {\n      const portfolioIdControl = providerGroup.get('portfolioId');\n      if (value === '4') {\n        portfolioIdControl?.setValidators(Validators.required);\n      } else {\n        portfolioIdControl?.clearValidators();\n      }\n      portfolioIdControl?.updateValueAndValidity();\n    });\n  }\n  get providers() {\n    return this.userProvidersForm.get('providers');\n  }\n  getSelectedProviderName(id) {\n    return this.availableProviders.find(p => p.id == id).name;\n  }\n  removeProvider(index) {\n    this.providers.removeAt(index);\n  }\n  getStations(index) {\n    const providerId = this.providers.at(index).get('providerId')?.value;\n    if (!providerId) return;\n    let request = {\n      providerId: this.providers.at(index).get('providerId')?.value,\n      username: this.providers.at(index).get('username')?.value,\n      password: this.providers.at(index).get('password')?.value,\n      portfolioId: this.providers.at(index).get('portfolioId')?.value,\n      ftpUrl: this.providers.at(index).get('ftpUrl')?.value\n    };\n    console.log('Form Data:', request);\n    this.stationsService.getStations(request).then(data => {\n      this.providers.at(index).patchValue({\n        stations: data\n      });\n    });\n    const providerGroup = this.providers.at(index);\n    // Set validators based on provider type\n    if (providerId === 4) {\n      // Aurora provider - Portfolio ID is required\n      providerGroup.get('portfolioId')?.setValidators(Validators.required);\n      providerGroup.get('ftpUrl')?.clearValidators();\n      providerGroup.get('ftpUrl')?.setValue('');\n    } else if (providerId === 6) {\n      // SMA provider - FTP URL is required\n      providerGroup.get('ftpUrl')?.setValidators(Validators.required);\n      providerGroup.get('portfolioId')?.clearValidators();\n      providerGroup.get('portfolioId')?.setValue('');\n    } else {\n      // Other providers - clear both\n      providerGroup.get('portfolioId')?.clearValidators();\n      providerGroup.get('portfolioId')?.setValue('');\n      providerGroup.get('ftpUrl')?.clearValidators();\n      providerGroup.get('ftpUrl')?.setValue('');\n    }\n    providerGroup.get('portfolioId')?.updateValueAndValidity();\n    providerGroup.get('ftpUrl')?.updateValueAndValidity();\n  }\n  onSubmit() {\n    if (this.userProvidersForm.invalid) {\n      this.userProvidersForm.markAllAsTouched();\n      return;\n    }\n    if (this.userProvidersForm.valid) {\n      console.log('Form Data (raw):', this.userProvidersForm.value.providers);\n      // Convert providerId from number to string as expected by API\n      const transformedProviders = this.userProvidersForm.value.providers.map(formProvider => ({\n        ...formProvider,\n        providerId: formProvider.providerId.toString() // Convert number to string\n      }));\n\n      console.log('Form Data (transformed):', transformedProviders);\n      let request = {\n        providers: transformedProviders\n      };\n      this.providersService.saveUserProviders(request).then(data => {\n        console.log('Save response:', data);\n        this.getUserProviders();\n      }).catch(error => {\n        console.error('Error saving providers:', error);\n      });\n    }\n  }\n  needsPortfolio(index) {\n    return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;\n  }\n  getUserProviders() {\n    this.providersService.getUserProviders().then(data => {\n      this.userProviders = data;\n      console.log(this.userProviders);\n      this.userProviders.forEach(up => {\n        up.configuration = JSON.parse(up.configuration);\n      });\n      if (data.length > 0) {\n        this.stationsService.getUserStations().then(data => {\n          this.stations = data;\n          this.cacheService.setStations(this.stations);\n        });\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ProvidersComponent_Factory(t) {\n    return new (t || ProvidersComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.CacheService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProvidersComponent,\n    selectors: [[\"app-providers\"]],\n    decls: 11,\n    vars: 6,\n    consts: [[1, \"grid\"], [1, \"col-12\"], [3, \"model\", \"home\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"content\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"p-3\"], [\"icon\", \"server\", 1, \"text-primary\", \"text-xl\"], [1, \"text-2xl\", \"font-semibold\", \"m-0\"], [\"severity\", \"info\", 3, \"value\", 4, \"ngIf\"], [\"severity\", \"info\", 3, \"value\"], [\"class\", \"text-center py-6\", 4, \"ngIf\"], [\"class\", \"grid\", 4, \"ngIf\"], [1, \"text-center\", \"py-6\"], [\"icon\", \"inbox\", 1, \"text-6xl\", \"text-300\", \"mb-3\"], [\"severity\", \"info\", 1, \"w-full\", 3, \"closable\"], [\"pTemplate\", \"\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"icon\", \"info-circle\"], [\"class\", \"col-12 lg:col-6 xl:col-4\", 4, \"ngFor\", \"ngForOf\"], [1, \"col-12\", \"lg:col-6\", \"xl:col-4\"], [1, \"h-full\"], [\"pTemplate\", \"footer\"], [1, \"bg-primary-50\", \"p-3\", \"border-round-top\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\"], [\"severity\", \"success\", \"icon\", \"pi pi-server\", 3, \"value\"], [\"value\", \"Active\", \"severity\", \"success\"], [1, \"space-y-3\"], [1, \"field\"], [1, \"block\", \"text-sm\", \"font-medium\", \"text-600\", \"mb-1\"], [\"icon\", \"user\", 1, \"mr-1\"], [1, \"text-900\", \"font-medium\"], [\"icon\", \"lock\", 1, \"mr-1\"], [\"class\", \"field\", 4, \"ngIf\"], [\"icon\", \"solar-panel\", 1, \"mr-1\"], [\"class\", \"text-900 font-medium\", 4, \"ngIf\"], [1, \"flex\", \"gap-2\"], [\"label\", \"Edit\", \"icon\", \"pi pi-pencil\", \"severity\", \"warning\", \"size\", \"small\", \"pTooltip\", \"Edit functionality coming soon\", 3, \"disabled\", \"click\"], [\"label\", \"Remove\", \"icon\", \"pi pi-trash\", \"severity\", \"danger\", \"size\", \"small\", \"pTooltip\", \"Remove functionality coming soon\", 3, \"disabled\", \"click\"], [\"icon\", \"plus-circle\", 1, \"text-primary\", \"text-xl\"], [1, \"bg-blue-50\", \"border-l-4\", \"border-blue-400\", \"p-4\", \"border-round\", \"mb-4\"], [1, \"flex\", \"align-items-start\", \"gap-3\"], [\"icon\", \"info-circle\", 1, \"text-blue-600\", \"text-xl\", \"mt-1\"], [1, \"flex-1\"], [1, \"text-blue-800\", \"font-semibold\", \"mb-3\", \"mt-0\"], [1, \"list-none\", \"p-0\", \"m-0\", \"text-blue-700\"], [1, \"flex\", \"align-items-start\", \"gap-2\", \"mb-2\"], [\"icon\", \"asterisk\", 1, \"text-red-500\", \"text-xs\", \"mt-1\", \"flex-shrink-0\"], [\"icon\", \"lightbulb\", 1, \"text-yellow-600\", \"text-sm\", \"mt-1\", \"flex-shrink-0\"], [\"icon\", \"question-circle\", \"pTooltip\", \"Click to see Aurora Portal example\", \"tooltipPosition\", \"top\", 1, \"cursor-pointer\", \"text-blue-600\", \"ml-1\"], [\"icon\", \"globe\", 1, \"text-blue-600\", \"text-sm\", \"mt-1\", \"flex-shrink-0\"], [1, \"flex\", \"align-items-start\", \"gap-2\"], [\"icon\", \"check-circle\", 1, \"text-green-600\", \"text-sm\", \"mt-1\", \"flex-shrink-0\"], [3, \"formGroup\", \"ngSubmit\"], [\"formArrayName\", \"providers\"], [\"class\", \"mb-4\", 3, \"formGroupName\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"gap-3\", \"mt-4\"], [\"label\", \"Add Provider\", \"icon\", \"pi pi-plus\", \"severity\", \"secondary\", 3, \"click\"], [\"label\", \"Save All Providers\", \"icon\", \"pi pi-save\", \"severity\", \"success\", \"type\", \"submit\", 4, \"ngIf\"], [1, \"mb-4\", 3, \"formGroupName\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"p-3\", \"bg-primary-50\"], [\"icon\", \"cog\", 1, \"text-primary\"], [1, \"font-semibold\"], [\"icon\", \"pi pi-times\", \"severity\", \"danger\", \"size\", \"small\", \"pTooltip\", \"Remove this provider\", 3, \"text\", \"click\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"xl:col-2\"], [1, \"block\", \"font-medium\", \"mb-2\", \"text-sm\", 3, \"for\"], [\"icon\", \"server\", 1, \"mr-1\"], [\"formControlName\", \"providerId\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"placeholder\", \"Select provider\", 1, \"w-full\", 3, \"id\", \"options\", \"showClear\"], [\"pInputText\", \"\", \"formControlName\", \"username\", \"placeholder\", \"Username\", 1, \"w-full\", 3, \"id\"], [\"formControlName\", \"password\", \"placeholder\", \"Password\", 1, \"w-full\", 3, \"id\", \"toggleMask\", \"feedback\"], [\"class\", \"col-12 md:col-6 lg:col-3 xl:col-2\", 4, \"ngIf\"], [\"class\", \"col-12 md:col-6 lg:col-3 xl:col-3\", 4, \"ngIf\"], [\"class\", \"col-12 md:col-6 lg:col-3 xl:col-4\", 4, \"ngIf\"], [\"icon\", \"briefcase\", 1, \"mr-1\"], [\"pInputText\", \"\", \"formControlName\", \"portfolioId\", \"placeholder\", \"Portfolio ID\", 1, \"w-full\", 3, \"id\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"xl:col-3\"], [\"icon\", \"globe\", 1, \"mr-1\"], [\"pInputText\", \"\", \"formControlName\", \"ftpUrl\", \"placeholder\", \"ftp://ftp.server.com/\", 1, \"w-full\", 3, \"id\"], [1, \"col-12\", \"md:col-6\", \"lg:col-3\", \"xl:col-4\"], [\"formControlName\", \"selectedStation\", \"optionLabel\", \"name\", \"optionValue\", \"id\", \"placeholder\", \"Select station\", 1, \"w-full\", 3, \"id\", \"options\", \"showClear\"], [\"label\", \"Get Stations\", \"icon\", \"pi pi-download\", \"severity\", \"info\", \"size\", \"small\", 3, \"click\", 4, \"ngIf\"], [\"label\", \"Get Stations\", \"icon\", \"pi pi-download\", \"severity\", \"info\", \"size\", \"small\", 3, \"click\"], [\"label\", \"Save All Providers\", \"icon\", \"pi pi-save\", \"severity\", \"success\", \"type\", \"submit\"]],\n    template: function ProvidersComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1);\n        i0.ɵɵelement(2, \"p-breadcrumb\", 2);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"div\", 1)(4, \"p-card\");\n        i0.ɵɵtemplate(5, ProvidersComponent_ng_template_5_Template, 5, 1, \"ng-template\", 3)(6, ProvidersComponent_ng_template_6_Template, 2, 2, \"ng-template\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 1)(8, \"p-card\");\n        i0.ɵɵtemplate(9, ProvidersComponent_ng_template_9_Template, 4, 0, \"ng-template\", 3)(10, ProvidersComponent_ng_template_10_Template, 39, 3, \"ng-template\", 4);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"model\", i0.ɵɵpureFunction1(3, _c1, i0.ɵɵpureFunction0(2, _c0)))(\"home\", i0.ɵɵpureFunction0(5, _c2));\n      }\n    },\n    dependencies: [i7.NgForOf, i7.NgIf, i4.PrimeTemplate, i8.Button, i5.ɵNgNoValidate, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgControlStatusGroup, i5.FormGroupDirective, i5.FormControlName, i5.FormGroupName, i5.FormArrayName, i9.Breadcrumb, i10.Tooltip, i11.FaIconComponent, i12.Card, i13.InputText, i14.Dropdown, i15.Password, i16.Badge, i17.Tag, i18.UIMessage],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelement", "ɵɵproperty", "ctx_r4", "userProviders", "length", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "ProvidersComponent_ng_template_5_p_badge_4_Template", "ɵɵadvance", "ctx_r0", "ProvidersComponent_ng_template_6_div_0_ng_template_3_Template", "ctx_r11", "getSelectedProviderName", "provider_r9", "providerId", "ɵɵtextInterpolate1", "configuration", "Stations", "StationName", "ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_div_4_Template", "ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_div_13_Template", "ɵɵtextInterpolate", "Username", "ɵɵlistener", "ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template_p_button_click_1_listener", "ɵɵrestoreView", "_r22", "i_r10", "ɵɵnextContext", "index", "ctx_r20", "ɵɵresetView", "edit<PERSON><PERSON><PERSON>", "ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template_p_button_click_2_listener", "ctx_r23", "removeProvider", "ProvidersComponent_ng_template_6_div_1_div_1_ng_template_2_Template", "ProvidersComponent_ng_template_6_div_1_div_1_ng_template_3_Template", "ProvidersComponent_ng_template_6_div_1_div_1_ng_template_4_Template", "ProvidersComponent_ng_template_6_div_1_div_1_Template", "ctx_r6", "ProvidersComponent_ng_template_6_div_0_Template", "ProvidersComponent_ng_template_6_div_1_Template", "ctx_r1", "ProvidersComponent_ng_template_10_div_35_ng_template_2_Template_p_button_click_5_listener", "_r34", "i_r28", "ctx_r32", "ɵɵpropertyInterpolate1", "tmp_2_0", "provider_r27", "get", "value", "ProvidersComponent_ng_template_10_div_35_ng_template_3_div_19_Template", "ProvidersComponent_ng_template_10_div_35_ng_template_3_div_20_Template", "ProvidersComponent_ng_template_10_div_35_ng_template_3_div_21_Template", "ctx_r30", "availableProviders", "tmp_10_0", "tmp_11_0", "tmp_12_0", "ProvidersComponent_ng_template_10_div_35_ng_template_4_p_button_1_Template_p_button_click_0_listener", "_r46", "ctx_r44", "getStations", "ProvidersComponent_ng_template_10_div_35_ng_template_4_p_button_1_Template", "tmp_0_0", "ctx_r31", "userProvidersForm", "valid", "ProvidersComponent_ng_template_10_div_35_ng_template_2_Template", "ProvidersComponent_ng_template_10_div_35_ng_template_3_Template", "ProvidersComponent_ng_template_10_div_35_ng_template_4_Template", "ProvidersComponent_ng_template_10_Template_form_ngSubmit_33_listener", "_r49", "ctx_r48", "onSubmit", "ProvidersComponent_ng_template_10_div_35_Template", "ProvidersComponent_ng_template_10_Template_p_button_click_37_listener", "ctx_r50", "addProvider", "ProvidersComponent_ng_template_10_p_button_38_Template", "ctx_r3", "providers", "controls", "ProvidersComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "cacheService", "stations", "tooltipVisible", "editing<PERSON>rovider", "editForm", "group", "array", "ngOnInit", "getProviders", "then", "data", "getUserProviders", "providerGroup", "required", "username", "password", "portfolioId", "ftpUrl", "selectedStation", "push", "valueChanges", "subscribe", "portfolioIdControl", "setValidators", "clearValidators", "updateValueAndValidity", "id", "find", "p", "name", "removeAt", "at", "request", "console", "log", "patchValue", "setValue", "invalid", "mark<PERSON>llAsTouched", "transformedProviders", "map", "formProvider", "toString", "saveUserProviders", "catch", "error", "needsPortfolio", "for<PERSON>ach", "up", "JSON", "parse", "getUserStations", "setStations", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "StationsService", "i3", "ProvidersService", "i4", "MessageService", "i5", "FormBuilder", "i6", "CacheService", "_2", "selectors", "decls", "vars", "consts", "template", "ProvidersComponent_Template", "rf", "ctx", "ProvidersComponent_ng_template_5_Template", "ProvidersComponent_ng_template_6_Template", "ProvidersComponent_ng_template_9_Template", "ProvidersComponent_ng_template_10_Template", "ɵɵpureFunction1", "_c1", "ɵɵpureFunction0", "_c0", "_c2"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\providers\\providers.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\providers\\providers.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'primeng/api';\r\nimport { LayoutService } from '../../../layout/service/app.layout.service';\r\n\r\nimport { GetStationsRequest, SaveUserProvidersRequest } from '../../api/requests';\r\nimport { IProvider, IUserProvider, IUserProviderConfiguration } from '../../api/responses';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { FaIconLibrary, FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-providers',\r\n    templateUrl: './providers.component.html'\r\n})\r\nexport class ProvidersComponent {\r\n\r\n    availableProviders:IProvider[] = [];\r\n    userProviders:IUserProviderConfiguration[] = [];\r\n    userProvidersForm: FormGroup;\r\n    stations: Station[] =[];\r\n    tooltipVisible = false;\r\n    editingProvider: IUserProviderConfiguration | null = null;\r\n    editForm: FormGroup | null = null;\r\n\r\n    constructor(public layoutService: LayoutService, \r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private cacheService: CacheService) {\r\n        \r\n        this.userProvidersForm = this.fb.group({\r\n          providers: this.fb.array([])\r\n        });\r\n\r\n    }\r\n\r\n    ngOnInit(){\r\n        this.providersService.getProviders().then(data => {\r\n            this.availableProviders = data;\r\n          });\r\n\r\n          this.getUserProviders();\r\n\r\n          this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\r\n    }\r\n\r\n\r\n    addProvider() {\r\n        const providerGroup = this.fb.group({\r\n          providerId: ['', Validators.required],\r\n          username: ['', Validators.required],\r\n          password: ['', Validators.required],\r\n          portfolioId: [''],\r\n          ftpUrl: [''],\r\n          stations: [[]], // Αρχικά empty array για το multiSelect,\r\n          selectedStation:['']\r\n        });\r\n        this.providers.push(providerGroup);\r\n\r\n        // Παρακολούθηση του providerId\r\n        providerGroup.get('providerId')?.valueChanges.subscribe((value) => {\r\n          const portfolioIdControl = providerGroup.get('portfolioId');\r\n\r\n          if (value === '4') {\r\n            portfolioIdControl?.setValidators(Validators.required);\r\n          } else {\r\n            portfolioIdControl?.clearValidators();\r\n          }\r\n\r\n          portfolioIdControl?.updateValueAndValidity();\r\n        });\r\n      }\r\n  \r\n      get providers(): FormArray {\r\n        return this.userProvidersForm.get('providers') as FormArray;\r\n      }\r\n\r\n      getSelectedProviderName(id:number): string | undefined {\r\n        return this.availableProviders.find(p => p.id == id).name;\r\n      }\r\n\r\n      \r\n    \r\n  \r\n  \r\n      removeProvider(index: number) {\r\n        this.providers.removeAt(index);\r\n      }\r\n  \r\n      getStations(index: number) {\r\n        const providerId = this.providers.at(index).get('providerId')?.value;\r\n    \r\n        if (!providerId) return;\r\n\r\n        let request: GetStationsRequest = {\r\n          providerId : this.providers.at(index).get('providerId')?.value,\r\n          username: this.providers.at(index).get('username')?.value, \r\n          password: this.providers.at(index).get('password')?.value,\r\n          portfolioId: this.providers.at(index).get('portfolioId')?.value,\r\n          ftpUrl: this.providers.at(index).get('ftpUrl')?.value\r\n        }\r\n        console.log('Form Data:', request);\r\n  \r\n        this.stationsService.getStations(request).then(data => {\r\n          this.providers.at(index).patchValue({ stations: data });\r\n        });\r\n\r\n        const providerGroup = this.providers.at(index);\r\n\r\n        // Set validators based on provider type\r\n        if (providerId === 4) {\r\n          // Aurora provider - Portfolio ID is required\r\n          providerGroup.get('portfolioId')?.setValidators(Validators.required);\r\n          providerGroup.get('ftpUrl')?.clearValidators();\r\n          providerGroup.get('ftpUrl')?.setValue('');\r\n        } else if (providerId === 6) {\r\n          // SMA provider - FTP URL is required\r\n          providerGroup.get('ftpUrl')?.setValidators(Validators.required);\r\n          providerGroup.get('portfolioId')?.clearValidators();\r\n          providerGroup.get('portfolioId')?.setValue('');\r\n        } else {\r\n          // Other providers - clear both\r\n          providerGroup.get('portfolioId')?.clearValidators();\r\n          providerGroup.get('portfolioId')?.setValue('');\r\n          providerGroup.get('ftpUrl')?.clearValidators();\r\n          providerGroup.get('ftpUrl')?.setValue('');\r\n        }\r\n\r\n        providerGroup.get('portfolioId')?.updateValueAndValidity();\r\n        providerGroup.get('ftpUrl')?.updateValueAndValidity();\r\n\r\n      }\r\n    \r\n      onSubmit() {\r\n        if (this.userProvidersForm.invalid) {\r\n          this.userProvidersForm.markAllAsTouched();\r\n          return;\r\n        }\r\n        if (this.userProvidersForm.valid) {\r\n          console.log('Form Data (raw):', this.userProvidersForm.value.providers);\r\n\r\n          // Convert providerId from number to string as expected by API\r\n          const transformedProviders = this.userProvidersForm.value.providers.map((formProvider: any) => ({\r\n            ...formProvider,\r\n            providerId: formProvider.providerId.toString() // Convert number to string\r\n          }));\r\n\r\n          console.log('Form Data (transformed):', transformedProviders);\r\n\r\n          let request: SaveUserProvidersRequest = {\r\n            providers: transformedProviders\r\n          };\r\n\r\n          this.providersService.saveUserProviders(request).then(data => {\r\n            console.log('Save response:', data);\r\n            this.getUserProviders();\r\n          }).catch(error => {\r\n            console.error('Error saving providers:', error);\r\n          });\r\n        }\r\n      }\r\n\r\n      needsPortfolio(index:number): boolean{\r\n        return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;\r\n      }\r\n  \r\n      getUserProviders(){\r\n        this.providersService.getUserProviders().then(data => {\r\n          this.userProviders = data;\r\n          console.log(this.userProviders)\r\n          this.userProviders.forEach(up => {\r\n            up.configuration = JSON.parse(up.configuration);\r\n\r\n          })\r\n          if (data.length > 0){\r\n            this.stationsService.getUserStations().then(data => {\r\n              this.stations = data;\r\n              this.cacheService.setStations(this.stations);\r\n            });\r\n          }\r\n        });\r\n      }\r\n    \r\n}\r\n", "<div class=\"grid\">\r\n    <!-- Breadcrumb Section -->\r\n    <div class=\"col-12\">\r\n        <p-breadcrumb [model]=\"[{ label: 'Providers' }]\" [home]=\"{icon: 'pi pi-home'}\"></p-breadcrumb>\r\n    </div>\r\n\r\n    <!-- Your Providers Section -->\r\n    <div class=\"col-12\">\r\n        <p-card>\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"flex align-items-center gap-2 p-3\">\r\n                    <fa-icon icon=\"server\" class=\"text-primary text-xl\"></fa-icon>\r\n                    <h2 class=\"text-2xl font-semibold m-0\">Your Providers</h2>\r\n                    <p-badge *ngIf=\"userProviders.length > 0\" [value]=\"userProviders.length\" severity=\"info\"></p-badge>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <!-- Empty State -->\r\n                <div *ngIf=\"userProviders.length === 0\" class=\"text-center py-6\">\r\n                    <fa-icon icon=\"inbox\" class=\"text-6xl text-300 mb-3\"></fa-icon>\r\n                    <p-message severity=\"info\" [closable]=\"false\" class=\"w-full\">\r\n                        <ng-template pTemplate>\r\n                            <div class=\"flex align-items-center gap-2\">\r\n                                <fa-icon icon=\"info-circle\"></fa-icon>\r\n                                <span>You are not registered to any provider. You can start adding providers now!</span>\r\n                            </div>\r\n                        </ng-template>\r\n                    </p-message>\r\n                </div>\r\n\r\n                <!-- Providers List -->\r\n                <div *ngIf=\"userProviders.length > 0\" class=\"grid\">\r\n                    <div *ngFor=\"let provider of userProviders; let i = index\" class=\"col-12 lg:col-6 xl:col-4\">\r\n                        <p-card class=\"h-full\">\r\n                            <ng-template pTemplate=\"header\">\r\n                                <div class=\"bg-primary-50 p-3 border-round-top\">\r\n                                    <div class=\"flex align-items-center justify-content-between\">\r\n                                        <p-tag [value]=\"getSelectedProviderName(provider.providerId)\"\r\n                                               severity=\"success\"\r\n                                               icon=\"pi pi-server\">\r\n                                        </p-tag>\r\n                                        <p-badge value=\"Active\" severity=\"success\"></p-badge>\r\n                                    </div>\r\n                                </div>\r\n                            </ng-template>\r\n\r\n                            <ng-template pTemplate=\"content\">\r\n                                <div class=\"space-y-3\">\r\n                                    <!-- Username -->\r\n                                    <div class=\"field\">\r\n                                        <label class=\"block text-sm font-medium text-600 mb-1\">\r\n                                            <fa-icon icon=\"user\" class=\"mr-1\"></fa-icon>\r\n                                            Username\r\n                                        </label>\r\n                                        <div class=\"text-900 font-medium\">{{ provider.configuration.Username }}</div>\r\n                                    </div>\r\n\r\n                                    <!-- Password (masked) -->\r\n                                    <div class=\"field\">\r\n                                        <label class=\"block text-sm font-medium text-600 mb-1\">\r\n                                            <fa-icon icon=\"lock\" class=\"mr-1\"></fa-icon>\r\n                                            Password\r\n                                        </label>\r\n                                        <div class=\"text-900 font-medium\">••••••••</div>\r\n                                    </div>\r\n\r\n                                    <!-- Station -->\r\n                                    <div *ngIf=\"provider.configuration.Stations\" class=\"field\">\r\n                                        <label class=\"block text-sm font-medium text-600 mb-1\">\r\n                                            <fa-icon icon=\"solar-panel\" class=\"mr-1\"></fa-icon>\r\n                                            Station\r\n                                        </label>\r\n                                        <div *ngIf=\"provider.configuration.Stations.length > 0\" class=\"text-900 font-medium\">\r\n                                            {{ provider.configuration.Stations[0].StationName }}\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </ng-template>\r\n\r\n                            <ng-template pTemplate=\"footer\">\r\n                                <div class=\"flex gap-2\">\r\n                                    <p-button label=\"Edit\"\r\n                                              icon=\"pi pi-pencil\"\r\n                                              severity=\"warning\"\r\n                                              size=\"small\"\r\n                                              [disabled]=\"true\"\r\n                                              pTooltip=\"Edit functionality coming soon\"\r\n                                              (click)=\"editProvider(i)\">\r\n                                    </p-button>\r\n                                    <p-button label=\"Remove\"\r\n                                              icon=\"pi pi-trash\"\r\n                                              severity=\"danger\"\r\n                                              size=\"small\"\r\n                                              [disabled]=\"true\"\r\n                                              pTooltip=\"Remove functionality coming soon\"\r\n                                              (click)=\"removeProvider(i)\">\r\n                                    </p-button>\r\n                                </div>\r\n                            </ng-template>\r\n                        </p-card>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n\r\n    <!-- Add Providers Section -->\r\n    <div class=\"col-12\">\r\n        <p-card>\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"flex align-items-center gap-2 p-3\">\r\n                    <fa-icon icon=\"plus-circle\" class=\"text-primary text-xl\"></fa-icon>\r\n                    <h2 class=\"text-2xl font-semibold m-0\">Add New Providers</h2>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <!-- Instructions Panel -->\r\n                <div class=\"bg-blue-50 border-l-4 border-blue-400 p-4 border-round mb-4\">\r\n                    <div class=\"flex align-items-start gap-3\">\r\n                        <fa-icon icon=\"info-circle\" class=\"text-blue-600 text-xl mt-1\"></fa-icon>\r\n                        <div class=\"flex-1\">\r\n                            <h4 class=\"text-blue-800 font-semibold mb-3 mt-0\">Form Instructions</h4>\r\n                            <ul class=\"list-none p-0 m-0 text-blue-700\">\r\n                                <li class=\"flex align-items-start gap-2 mb-2\">\r\n                                    <fa-icon icon=\"asterisk\" class=\"text-red-500 text-xs mt-1 flex-shrink-0\"></fa-icon>\r\n                                    <span>Fill in all required fields marked with an asterisk (*).</span>\r\n                                </li>\r\n                                <li class=\"flex align-items-start gap-2 mb-2\">\r\n                                    <fa-icon icon=\"lightbulb\" class=\"text-yellow-600 text-sm mt-1 flex-shrink-0\"></fa-icon>\r\n                                    <span>\r\n                                        If you select Provider <strong>Aurora</strong>, the Portfolio ID field becomes required.\r\n                                        You can find it in the Aurora Portal.\r\n                                        <fa-icon icon=\"question-circle\"\r\n                                                 class=\"cursor-pointer text-blue-600 ml-1\"\r\n                                                 pTooltip=\"Click to see Aurora Portal example\"\r\n                                                 tooltipPosition=\"top\">\r\n                                        </fa-icon>\r\n                                    </span>\r\n                                </li>\r\n                                <li class=\"flex align-items-start gap-2 mb-2\">\r\n                                    <fa-icon icon=\"globe\" class=\"text-blue-600 text-sm mt-1 flex-shrink-0\"></fa-icon>\r\n                                    <span>\r\n                                        If you select Provider <strong>SMA</strong>, the FTP Url becomes required.\r\n                                        It should be like <em>ftp://ftp.server.com/</em>.\r\n                                        Please use the full path where the stations folders are located.\r\n                                    </span>\r\n                                </li>\r\n                                <li class=\"flex align-items-start gap-2\">\r\n                                    <fa-icon icon=\"check-circle\" class=\"text-green-600 text-sm mt-1 flex-shrink-0\"></fa-icon>\r\n                                    <span>Make sure to select one station from the available list.</span>\r\n                                </li>\r\n                            </ul>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n\r\n\r\n                <!-- Dynamic Form -->\r\n                <form [formGroup]=\"userProvidersForm\" (ngSubmit)=\"onSubmit()\">\r\n                    <div formArrayName=\"providers\">\r\n                        <div *ngFor=\"let provider of providers.controls; let i = index\" [formGroupName]=\"i\" class=\"mb-4\">\r\n                            <p-card>\r\n                                <ng-template pTemplate=\"header\">\r\n                                    <div class=\"flex align-items-center justify-content-between p-3 bg-primary-50\">\r\n                                        <div class=\"flex align-items-center gap-2\">\r\n                                            <fa-icon icon=\"cog\" class=\"text-primary\"></fa-icon>\r\n                                            <span class=\"font-semibold\">Provider Configuration {{ i + 1 }}</span>\r\n                                        </div>\r\n                                        <p-button icon=\"pi pi-times\"\r\n                                                  severity=\"danger\"\r\n                                                  size=\"small\"\r\n                                                  [text]=\"true\"\r\n                                                  pTooltip=\"Remove this provider\"\r\n                                                  (click)=\"removeProvider(i)\">\r\n                                        </p-button>\r\n                                    </div>\r\n                                </ng-template>\r\n\r\n                                <ng-template pTemplate=\"content\">\r\n                                    <!-- Main Fields Row (for large screens) -->\r\n                                    <div class=\"grid\">\r\n                                        <!-- Provider Selection -->\r\n                                        <div class=\"col-12 md:col-6 lg:col-3 xl:col-2\">\r\n                                            <div class=\"field\">\r\n                                                <label for=\"provider-{{i}}\" class=\"block font-medium mb-2 text-sm\">\r\n                                                    <fa-icon icon=\"server\" class=\"mr-1\"></fa-icon>\r\n                                                    Provider *\r\n                                                </label>\r\n                                                <p-dropdown id=\"provider-{{i}}\"\r\n                                                           formControlName=\"providerId\"\r\n                                                           [options]=\"availableProviders\"\r\n                                                           optionLabel=\"name\"\r\n                                                           optionValue=\"id\"\r\n                                                           placeholder=\"Select provider\"\r\n                                                           class=\"w-full\"\r\n                                                           [showClear]=\"true\">\r\n                                                </p-dropdown>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <!-- Username -->\r\n                                        <div class=\"col-12 md:col-6 lg:col-3 xl:col-2\">\r\n                                            <div class=\"field\">\r\n                                                <label for=\"username-{{i}}\" class=\"block font-medium mb-2 text-sm\">\r\n                                                    <fa-icon icon=\"user\" class=\"mr-1\"></fa-icon>\r\n                                                    Username *\r\n                                                </label>\r\n                                                <input pInputText\r\n                                                       id=\"username-{{i}}\"\r\n                                                       formControlName=\"username\"\r\n                                                       placeholder=\"Username\"\r\n                                                       class=\"w-full\">\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <!-- Password -->\r\n                                        <div class=\"col-12 md:col-6 lg:col-3 xl:col-2\">\r\n                                            <div class=\"field\">\r\n                                                <label for=\"password-{{i}}\" class=\"block font-medium mb-2 text-sm\">\r\n                                                    <fa-icon icon=\"lock\" class=\"mr-1\"></fa-icon>\r\n                                                    Password *\r\n                                                </label>\r\n                                                <p-password id=\"password-{{i}}\"\r\n                                                           formControlName=\"password\"\r\n                                                           placeholder=\"Password\"\r\n                                                           class=\"w-full\"\r\n                                                           [toggleMask]=\"true\"\r\n                                                           [feedback]=\"false\">\r\n                                                </p-password>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <!-- Portfolio ID (Aurora only) -->\r\n                                        <div *ngIf=\"provider.get('providerId')?.value == 4\" class=\"col-12 md:col-6 lg:col-3 xl:col-2\">\r\n                                            <div class=\"field\">\r\n                                                <label for=\"portfolio-{{i}}\" class=\"block font-medium mb-2 text-sm\">\r\n                                                    <fa-icon icon=\"briefcase\" class=\"mr-1\"></fa-icon>\r\n                                                    Portfolio ID *\r\n                                                </label>\r\n                                                <input pInputText\r\n                                                       id=\"portfolio-{{i}}\"\r\n                                                       formControlName=\"portfolioId\"\r\n                                                       placeholder=\"Portfolio ID\"\r\n                                                       class=\"w-full\">\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <!-- FTP URL (SMA only) -->\r\n                                        <div *ngIf=\"provider.get('providerId')?.value == 6\" class=\"col-12 md:col-6 lg:col-3 xl:col-3\">\r\n                                            <div class=\"field\">\r\n                                                <label for=\"ftp-{{i}}\" class=\"block font-medium mb-2 text-sm\">\r\n                                                    <fa-icon icon=\"globe\" class=\"mr-1\"></fa-icon>\r\n                                                    FTP URL *\r\n                                                </label>\r\n                                                <input pInputText\r\n                                                       id=\"ftp-{{i}}\"\r\n                                                       formControlName=\"ftpUrl\"\r\n                                                       placeholder=\"ftp://ftp.server.com/\"\r\n                                                       class=\"w-full\">\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <!-- Station Selection -->\r\n                                        <div *ngIf=\"provider.get('stations')?.value.length > 0\" class=\"col-12 md:col-6 lg:col-3 xl:col-4\">\r\n                                            <div class=\"field\">\r\n                                                <label for=\"station-{{i}}\" class=\"block font-medium mb-2 text-sm\">\r\n                                                    <fa-icon icon=\"solar-panel\" class=\"mr-1\"></fa-icon>\r\n                                                    Station *\r\n                                                </label>\r\n                                                <p-dropdown id=\"station-{{i}}\"\r\n                                                           formControlName=\"selectedStation\"\r\n                                                           [options]=\"provider.get('stations')?.value\"\r\n                                                           optionLabel=\"name\"\r\n                                                           optionValue=\"id\"\r\n                                                           placeholder=\"Select station\"\r\n                                                           class=\"w-full\"\r\n                                                           [showClear]=\"true\">\r\n                                                </p-dropdown>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </ng-template>\r\n\r\n                                <ng-template pTemplate=\"footer\">\r\n                                    <div class=\"flex gap-2\">\r\n                                        <p-button label=\"Get Stations\"\r\n                                                  icon=\"pi pi-download\"\r\n                                                  severity=\"info\"\r\n                                                  size=\"small\"\r\n                                                  *ngIf=\"provider.get('providerId')?.value && userProvidersForm.valid\"\r\n                                                  (click)=\"getStations(i)\">\r\n                                        </p-button>\r\n                                    </div>\r\n                                </ng-template>\r\n                            </p-card>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <!-- Action Buttons -->\r\n                    <div class=\"flex gap-3 mt-4\">\r\n                        <p-button label=\"Add Provider\"\r\n                                  icon=\"pi pi-plus\"\r\n                                  severity=\"secondary\"\r\n                                  (click)=\"addProvider()\">\r\n                        </p-button>\r\n\r\n                        <p-button label=\"Save All Providers\"\r\n                                  icon=\"pi pi-save\"\r\n                                  severity=\"success\"\r\n                                  type=\"submit\"\r\n                                  *ngIf=\"userProvidersForm.valid\">\r\n                        </p-button>\r\n                    </div>\r\n                </form>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n</div>"], "mappings": "AACA,SAA4CA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;ICY1DC,EAAA,CAAAC,SAAA,iBAAmG;;;;IAAzDD,EAAA,CAAAE,UAAA,UAAAC,MAAA,CAAAC,aAAA,CAAAC,MAAA,CAA8B;;;;;IAH5EL,EAAA,CAAAM,cAAA,aAA+C;IAC3CN,EAAA,CAAAC,SAAA,iBAA8D;IAC9DD,EAAA,CAAAM,cAAA,YAAuC;IAAAN,EAAA,CAAAO,MAAA,qBAAc;IAAAP,EAAA,CAAAQ,YAAA,EAAK;IAC1DR,EAAA,CAAAS,UAAA,IAAAC,mDAAA,qBAAmG;IACvGV,EAAA,CAAAQ,YAAA,EAAM;;;;IADQR,EAAA,CAAAW,SAAA,GAA8B;IAA9BX,EAAA,CAAAE,UAAA,SAAAU,MAAA,CAAAR,aAAA,CAAAC,MAAA,KAA8B;;;;;IAUhCL,EAAA,CAAAM,cAAA,cAA2C;IACvCN,EAAA,CAAAC,SAAA,kBAAsC;IACtCD,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAO,MAAA,kFAA2E;IAAAP,EAAA,CAAAQ,YAAA,EAAO;;;;;IANxGR,EAAA,CAAAM,cAAA,cAAiE;IAC7DN,EAAA,CAAAC,SAAA,kBAA+D;IAC/DD,EAAA,CAAAM,cAAA,oBAA6D;IACzDN,EAAA,CAAAS,UAAA,IAAAI,6DAAA,0BAKc;IAClBb,EAAA,CAAAQ,YAAA,EAAY;;;IAPeR,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAAE,UAAA,mBAAkB;;;;;IAejCF,EAAA,CAAAM,cAAA,cAAgD;IAExCN,EAAA,CAAAC,SAAA,gBAGQ;IAEZD,EAAA,CAAAQ,YAAA,EAAM;;;;;IALKR,EAAA,CAAAW,SAAA,GAAsD;IAAtDX,EAAA,CAAAE,UAAA,UAAAY,OAAA,CAAAC,uBAAA,CAAAC,WAAA,CAAAC,UAAA,EAAsD;;;;;IAmC7DjB,EAAA,CAAAM,cAAA,cAAqF;IACjFN,EAAA,CAAAO,MAAA,GACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAM;;;;IADFR,EAAA,CAAAW,SAAA,GACJ;IADIX,EAAA,CAAAkB,kBAAA,MAAAF,WAAA,CAAAG,aAAA,CAAAC,QAAA,IAAAC,WAAA,MACJ;;;;;IAPJrB,EAAA,CAAAM,cAAA,cAA2D;IAEnDN,EAAA,CAAAC,SAAA,kBAAmD;IACnDD,EAAA,CAAAO,MAAA,gBACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAQ;IACRR,EAAA,CAAAS,UAAA,IAAAa,gFAAA,kBAEM;IACVtB,EAAA,CAAAQ,YAAA,EAAM;;;;IAHIR,EAAA,CAAAW,SAAA,GAAgD;IAAhDX,EAAA,CAAAE,UAAA,SAAAc,WAAA,CAAAG,aAAA,CAAAC,QAAA,CAAAf,MAAA,KAAgD;;;;;IAzB9DL,EAAA,CAAAM,cAAA,cAAuB;IAIXN,EAAA,CAAAC,SAAA,kBAA4C;IAC5CD,EAAA,CAAAO,MAAA,iBACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAQ;IACRR,EAAA,CAAAM,cAAA,cAAkC;IAAAN,EAAA,CAAAO,MAAA,GAAqC;IAAAP,EAAA,CAAAQ,YAAA,EAAM;IAIjFR,EAAA,CAAAM,cAAA,cAAmB;IAEXN,EAAA,CAAAC,SAAA,kBAA4C;IAC5CD,EAAA,CAAAO,MAAA,kBACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAQ;IACRR,EAAA,CAAAM,cAAA,eAAkC;IAAAN,EAAA,CAAAO,MAAA,wDAAQ;IAAAP,EAAA,CAAAQ,YAAA,EAAM;IAIpDR,EAAA,CAAAS,UAAA,KAAAc,0EAAA,kBAQM;IACVvB,EAAA,CAAAQ,YAAA,EAAM;;;;IAtBoCR,EAAA,CAAAW,SAAA,GAAqC;IAArCX,EAAA,CAAAwB,iBAAA,CAAAR,WAAA,CAAAG,aAAA,CAAAM,QAAA,CAAqC;IAarEzB,EAAA,CAAAW,SAAA,GAAqC;IAArCX,EAAA,CAAAE,UAAA,SAAAc,WAAA,CAAAG,aAAA,CAAAC,QAAA,CAAqC;;;;;;IAa/CpB,EAAA,CAAAM,cAAA,cAAwB;IAOVN,EAAA,CAAA0B,UAAA,mBAAAC,8FAAA;MAAA3B,EAAA,CAAA4B,aAAA,CAAAC,IAAA;MAAA,MAAAC,KAAA,GAAA9B,EAAA,CAAA+B,aAAA,GAAAC,KAAA;MAAA,MAAAC,OAAA,GAAAjC,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAkC,WAAA,CAAAD,OAAA,CAAAE,YAAA,CAAAL,KAAA,CAAe;IAAA,EAAC;IACnC9B,EAAA,CAAAQ,YAAA,EAAW;IACXR,EAAA,CAAAM,cAAA,mBAMsC;IAA5BN,EAAA,CAAA0B,UAAA,mBAAAU,8FAAA;MAAApC,EAAA,CAAA4B,aAAA,CAAAC,IAAA;MAAA,MAAAC,KAAA,GAAA9B,EAAA,CAAA+B,aAAA,GAAAC,KAAA;MAAA,MAAAK,OAAA,GAAArC,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAkC,WAAA,CAAAG,OAAA,CAAAC,cAAA,CAAAR,KAAA,CAAiB;IAAA,EAAC;IACrC9B,EAAA,CAAAQ,YAAA,EAAW;;;IAXDR,EAAA,CAAAW,SAAA,GAAiB;IAAjBX,EAAA,CAAAE,UAAA,kBAAiB;IAQjBF,EAAA,CAAAW,SAAA,GAAiB;IAAjBX,EAAA,CAAAE,UAAA,kBAAiB;;;;;IA7D3CF,EAAA,CAAAM,cAAA,cAA4F;IAEpFN,EAAA,CAAAS,UAAA,IAAA8B,mEAAA,yBAUc,IAAAC,mEAAA,8BAAAC,mEAAA;IAuDlBzC,EAAA,CAAAQ,YAAA,EAAS;;;;;IApEjBR,EAAA,CAAAM,cAAA,aAAmD;IAC/CN,EAAA,CAAAS,UAAA,IAAAiC,qDAAA,kBAoEM;IACV1C,EAAA,CAAAQ,YAAA,EAAM;;;;IArEwBR,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAAE,UAAA,YAAAyC,MAAA,CAAAvC,aAAA,CAAkB;;;;;IAdhDJ,EAAA,CAAAS,UAAA,IAAAmC,+CAAA,kBAUM,IAAAC,+CAAA;;;;IAVA7C,EAAA,CAAAE,UAAA,SAAA4C,MAAA,CAAA1C,aAAA,CAAAC,MAAA,OAAgC;IAahCL,EAAA,CAAAW,SAAA,GAA8B;IAA9BX,EAAA,CAAAE,UAAA,SAAA4C,MAAA,CAAA1C,aAAA,CAAAC,MAAA,KAA8B;;;;;IA+EpCL,EAAA,CAAAM,cAAA,aAA+C;IAC3CN,EAAA,CAAAC,SAAA,kBAAmE;IACnED,EAAA,CAAAM,cAAA,YAAuC;IAAAN,EAAA,CAAAO,MAAA,wBAAiB;IAAAP,EAAA,CAAAQ,YAAA,EAAK;;;;;;IAqD7CR,EAAA,CAAAM,cAAA,cAA+E;IAEvEN,EAAA,CAAAC,SAAA,kBAAmD;IACnDD,EAAA,CAAAM,cAAA,eAA4B;IAAAN,EAAA,CAAAO,MAAA,GAAkC;IAAAP,EAAA,CAAAQ,YAAA,EAAO;IAEzER,EAAA,CAAAM,cAAA,mBAKsC;IAA5BN,EAAA,CAAA0B,UAAA,mBAAAqB,0FAAA;MAAA/C,EAAA,CAAA4B,aAAA,CAAAoB,IAAA;MAAA,MAAAC,KAAA,GAAAjD,EAAA,CAAA+B,aAAA,GAAAC,KAAA;MAAA,MAAAkB,OAAA,GAAAlD,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAkC,WAAA,CAAAgB,OAAA,CAAAZ,cAAA,CAAAW,KAAA,CAAiB;IAAA,EAAC;IACrCjD,EAAA,CAAAQ,YAAA,EAAW;;;;IARqBR,EAAA,CAAAW,SAAA,GAAkC;IAAlCX,EAAA,CAAAkB,kBAAA,4BAAA+B,KAAA,SAAkC;IAKxDjD,EAAA,CAAAW,SAAA,GAAa;IAAbX,EAAA,CAAAE,UAAA,cAAa;;;;;IA8DvBF,EAAA,CAAAM,cAAA,cAA8F;IAGlFN,EAAA,CAAAC,SAAA,kBAAiD;IACjDD,EAAA,CAAAO,MAAA,uBACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAQ;IACRR,EAAA,CAAAC,SAAA,gBAIsB;IAC1BD,EAAA,CAAAQ,YAAA,EAAM;;;;IATKR,EAAA,CAAAW,SAAA,GAAqB;IAArBX,EAAA,CAAAmD,sBAAA,sBAAAF,KAAA,KAAqB;IAKrBjD,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAmD,sBAAA,qBAAAF,KAAA,KAAoB;;;;;IAQnCjD,EAAA,CAAAM,cAAA,cAA8F;IAGlFN,EAAA,CAAAC,SAAA,kBAA6C;IAC7CD,EAAA,CAAAO,MAAA,kBACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAQ;IACRR,EAAA,CAAAC,SAAA,gBAIsB;IAC1BD,EAAA,CAAAQ,YAAA,EAAM;;;;IATKR,EAAA,CAAAW,SAAA,GAAe;IAAfX,EAAA,CAAAmD,sBAAA,gBAAAF,KAAA,KAAe;IAKfjD,EAAA,CAAAW,SAAA,GAAc;IAAdX,EAAA,CAAAmD,sBAAA,eAAAF,KAAA,KAAc;;;;;IAQ7BjD,EAAA,CAAAM,cAAA,cAAkG;IAGtFN,EAAA,CAAAC,SAAA,kBAAmD;IACnDD,EAAA,CAAAO,MAAA,kBACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAQ;IACRR,EAAA,CAAAC,SAAA,qBAQa;IACjBD,EAAA,CAAAQ,YAAA,EAAM;;;;;;;IAbKR,EAAA,CAAAW,SAAA,GAAmB;IAAnBX,EAAA,CAAAmD,sBAAA,oBAAAF,KAAA,KAAmB;IAIdjD,EAAA,CAAAW,SAAA,GAAkB;IAAlBX,EAAA,CAAAmD,sBAAA,mBAAAF,KAAA,KAAkB;IAEnBjD,EAAA,CAAAE,UAAA,aAAAkD,OAAA,GAAAC,YAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,KAAA,CAA2C;;;;;IA3FlEvD,EAAA,CAAAM,cAAA,aAAkB;IAKFN,EAAA,CAAAC,SAAA,kBAA8C;IAC9CD,EAAA,CAAAO,MAAA,mBACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAQ;IACRR,EAAA,CAAAC,SAAA,qBAQa;IACjBD,EAAA,CAAAQ,YAAA,EAAM;IAIVR,EAAA,CAAAM,cAAA,cAA+C;IAGnCN,EAAA,CAAAC,SAAA,mBAA4C;IAC5CD,EAAA,CAAAO,MAAA,oBACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAQ;IACRR,EAAA,CAAAC,SAAA,iBAIsB;IAC1BD,EAAA,CAAAQ,YAAA,EAAM;IAIVR,EAAA,CAAAM,cAAA,eAA+C;IAGnCN,EAAA,CAAAC,SAAA,mBAA4C;IAC5CD,EAAA,CAAAO,MAAA,oBACJ;IAAAP,EAAA,CAAAQ,YAAA,EAAQ;IACRR,EAAA,CAAAC,SAAA,sBAMa;IACjBD,EAAA,CAAAQ,YAAA,EAAM;IAIVR,EAAA,CAAAS,UAAA,KAAA+C,sEAAA,kBAYM,KAAAC,sEAAA,uBAAAC,sEAAA;IAmCV1D,EAAA,CAAAQ,YAAA,EAAM;;;;;;;;;;IAhGaR,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAmD,sBAAA,qBAAAF,KAAA,KAAoB;IAIfjD,EAAA,CAAAW,SAAA,GAAmB;IAAnBX,EAAA,CAAAmD,sBAAA,oBAAAF,KAAA,KAAmB;IAEpBjD,EAAA,CAAAE,UAAA,YAAAyD,OAAA,CAAAC,kBAAA,CAA8B;IAalC5D,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAmD,sBAAA,qBAAAF,KAAA,KAAoB;IAKpBjD,EAAA,CAAAW,SAAA,GAAmB;IAAnBX,EAAA,CAAAmD,sBAAA,oBAAAF,KAAA,KAAmB;IAUnBjD,EAAA,CAAAW,SAAA,GAAoB;IAApBX,EAAA,CAAAmD,sBAAA,qBAAAF,KAAA,KAAoB;IAIfjD,EAAA,CAAAW,SAAA,GAAmB;IAAnBX,EAAA,CAAAmD,sBAAA,oBAAAF,KAAA,KAAmB;IAIpBjD,EAAA,CAAAE,UAAA,oBAAmB;IAOhCF,EAAA,CAAAW,SAAA,GAA4C;IAA5CX,EAAA,CAAAE,UAAA,WAAA2D,QAAA,GAAAR,YAAA,CAAAC,GAAA,iCAAAO,QAAA,CAAAN,KAAA,OAA4C;IAe5CvD,EAAA,CAAAW,SAAA,GAA4C;IAA5CX,EAAA,CAAAE,UAAA,WAAA4D,QAAA,GAAAT,YAAA,CAAAC,GAAA,iCAAAQ,QAAA,CAAAP,KAAA,OAA4C;IAe5CvD,EAAA,CAAAW,SAAA,GAAgD;IAAhDX,EAAA,CAAAE,UAAA,WAAA6D,QAAA,GAAAV,YAAA,CAAAC,GAAA,+BAAAS,QAAA,CAAAR,KAAA,CAAAlD,MAAA,MAAgD;;;;;;IAsBtDL,EAAA,CAAAM,cAAA,mBAKmC;IAAzBN,EAAA,CAAA0B,UAAA,mBAAAsC,qGAAA;MAAAhE,EAAA,CAAA4B,aAAA,CAAAqC,IAAA;MAAA,MAAAhB,KAAA,GAAAjD,EAAA,CAAA+B,aAAA,IAAAC,KAAA;MAAA,MAAAkC,OAAA,GAAAlE,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAkC,WAAA,CAAAgC,OAAA,CAAAC,WAAA,CAAAlB,KAAA,CAAc;IAAA,EAAC;IAClCjD,EAAA,CAAAQ,YAAA,EAAW;;;;;IAPfR,EAAA,CAAAM,cAAA,cAAwB;IACpBN,EAAA,CAAAS,UAAA,IAAA2D,0EAAA,uBAMW;IACfpE,EAAA,CAAAQ,YAAA,EAAM;;;;;;IAHSR,EAAA,CAAAW,SAAA,GAAkE;IAAlEX,EAAA,CAAAE,UAAA,WAAAmE,OAAA,GAAAhB,YAAA,CAAAC,GAAA,iCAAAe,OAAA,CAAAd,KAAA,KAAAe,OAAA,CAAAC,iBAAA,CAAAC,KAAA,CAAkE;;;;;IAjI7FxE,EAAA,CAAAM,cAAA,cAAiG;IAEzFN,EAAA,CAAAS,UAAA,IAAAgE,+DAAA,yBAcc,IAAAC,+DAAA,+BAAAC,+DAAA;IAsHlB3E,EAAA,CAAAQ,YAAA,EAAS;;;;IAtImDR,EAAA,CAAAE,UAAA,kBAAA+C,KAAA,CAAmB;;;;;IAkJnFjD,EAAA,CAAAC,SAAA,mBAKW;;;;;;IAnMnBD,EAAA,CAAAM,cAAA,cAAyE;IAEjEN,EAAA,CAAAC,SAAA,kBAAyE;IACzED,EAAA,CAAAM,cAAA,cAAoB;IACkCN,EAAA,CAAAO,MAAA,wBAAiB;IAAAP,EAAA,CAAAQ,YAAA,EAAK;IACxER,EAAA,CAAAM,cAAA,aAA4C;IAEpCN,EAAA,CAAAC,SAAA,kBAAmF;IACnFD,EAAA,CAAAM,cAAA,WAAM;IAAAN,EAAA,CAAAO,MAAA,gEAAwD;IAAAP,EAAA,CAAAQ,YAAA,EAAO;IAEzER,EAAA,CAAAM,cAAA,cAA8C;IAC1CN,EAAA,CAAAC,SAAA,mBAAuF;IACvFD,EAAA,CAAAM,cAAA,YAAM;IACFN,EAAA,CAAAO,MAAA,gCAAuB;IAAAP,EAAA,CAAAM,cAAA,cAAQ;IAAAN,EAAA,CAAAO,MAAA,cAAM;IAAAP,EAAA,CAAAQ,YAAA,EAAS;IAAAR,EAAA,CAAAO,MAAA,yFAE9C;IAAAP,EAAA,CAAAC,SAAA,mBAIU;IACdD,EAAA,CAAAQ,YAAA,EAAO;IAEXR,EAAA,CAAAM,cAAA,cAA8C;IAC1CN,EAAA,CAAAC,SAAA,mBAAiF;IACjFD,EAAA,CAAAM,cAAA,YAAM;IACFN,EAAA,CAAAO,MAAA,gCAAuB;IAAAP,EAAA,CAAAM,cAAA,cAAQ;IAAAN,EAAA,CAAAO,MAAA,WAAG;IAAAP,EAAA,CAAAQ,YAAA,EAAS;IAAAR,EAAA,CAAAO,MAAA,0DACzB;IAAAP,EAAA,CAAAM,cAAA,UAAI;IAAAN,EAAA,CAAAO,MAAA,6BAAqB;IAAAP,EAAA,CAAAQ,YAAA,EAAK;IAAAR,EAAA,CAAAO,MAAA,2EAEpD;IAAAP,EAAA,CAAAQ,YAAA,EAAO;IAEXR,EAAA,CAAAM,cAAA,cAAyC;IACrCN,EAAA,CAAAC,SAAA,mBAAyF;IACzFD,EAAA,CAAAM,cAAA,YAAM;IAAAN,EAAA,CAAAO,MAAA,gEAAwD;IAAAP,EAAA,CAAAQ,YAAA,EAAO;IAUzFR,EAAA,CAAAM,cAAA,gBAA8D;IAAxBN,EAAA,CAAA0B,UAAA,sBAAAkD,qEAAA;MAAA5E,EAAA,CAAA4B,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAA9E,EAAA,CAAA+B,aAAA;MAAA,OAAY/B,EAAA,CAAAkC,WAAA,CAAA4C,OAAA,CAAAC,QAAA,EAAU;IAAA,EAAC;IACzD/E,EAAA,CAAAM,cAAA,eAA+B;IAC3BN,EAAA,CAAAS,UAAA,KAAAuE,iDAAA,kBAuIM;IACVhF,EAAA,CAAAQ,YAAA,EAAM;IAGNR,EAAA,CAAAM,cAAA,eAA6B;IAIfN,EAAA,CAAA0B,UAAA,mBAAAuD,sEAAA;MAAAjF,EAAA,CAAA4B,aAAA,CAAAiD,IAAA;MAAA,MAAAK,OAAA,GAAAlF,EAAA,CAAA+B,aAAA;MAAA,OAAS/B,EAAA,CAAAkC,WAAA,CAAAgD,OAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACjCnF,EAAA,CAAAQ,YAAA,EAAW;IAEXR,EAAA,CAAAS,UAAA,KAAA2E,sDAAA,uBAKW;IACfpF,EAAA,CAAAQ,YAAA,EAAM;;;;IA1JJR,EAAA,CAAAW,SAAA,IAA+B;IAA/BX,EAAA,CAAAE,UAAA,cAAAmF,MAAA,CAAAd,iBAAA,CAA+B;IAEHvE,EAAA,CAAAW,SAAA,GAAuB;IAAvBX,EAAA,CAAAE,UAAA,YAAAmF,MAAA,CAAAC,SAAA,CAAAC,QAAA,CAAuB;IAsJtCvF,EAAA,CAAAW,SAAA,GAA6B;IAA7BX,EAAA,CAAAE,UAAA,SAAAmF,MAAA,CAAAd,iBAAA,CAAAC,KAAA,CAA6B;;;;;;;;;;ADvShE,OAAM,MAAOgB,kBAAkB;EAU3BC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,YAA0B;IALnB,KAAAL,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,YAAY,GAAZA,YAAY;IAbxB,KAAAnC,kBAAkB,GAAe,EAAE;IACnC,KAAAxD,aAAa,GAAgC,EAAE;IAE/C,KAAA4F,QAAQ,GAAa,EAAE;IACvB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,eAAe,GAAsC,IAAI;IACzD,KAAAC,QAAQ,GAAqB,IAAI;IAS7B,IAAI,CAAC5B,iBAAiB,GAAG,IAAI,CAACuB,EAAE,CAACM,KAAK,CAAC;MACrCd,SAAS,EAAE,IAAI,CAACQ,EAAE,CAACO,KAAK,CAAC,EAAE;KAC5B,CAAC;EAEN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACV,gBAAgB,CAACW,YAAY,EAAE,CAACC,IAAI,CAACC,IAAI,IAAG;MAC7C,IAAI,CAAC7C,kBAAkB,GAAG6C,IAAI;IAChC,CAAC,CAAC;IAEF,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,CAACvB,WAAW,EAAE,CAAC,CAAC;EAC1B;;EAGAA,WAAWA,CAAA;IACP,MAAMwB,aAAa,GAAG,IAAI,CAACb,EAAE,CAACM,KAAK,CAAC;MAClCnF,UAAU,EAAE,CAAC,EAAE,EAAElB,UAAU,CAAC6G,QAAQ,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE9G,UAAU,CAAC6G,QAAQ,CAAC;MACnCE,QAAQ,EAAE,CAAC,EAAE,EAAE/G,UAAU,CAAC6G,QAAQ,CAAC;MACnCG,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZhB,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdiB,eAAe,EAAC,CAAC,EAAE;KACpB,CAAC;IACF,IAAI,CAAC3B,SAAS,CAAC4B,IAAI,CAACP,aAAa,CAAC;IAElC;IACAA,aAAa,CAACrD,GAAG,CAAC,YAAY,CAAC,EAAE6D,YAAY,CAACC,SAAS,CAAE7D,KAAK,IAAI;MAChE,MAAM8D,kBAAkB,GAAGV,aAAa,CAACrD,GAAG,CAAC,aAAa,CAAC;MAE3D,IAAIC,KAAK,KAAK,GAAG,EAAE;QACjB8D,kBAAkB,EAAEC,aAAa,CAACvH,UAAU,CAAC6G,QAAQ,CAAC;OACvD,MAAM;QACLS,kBAAkB,EAAEE,eAAe,EAAE;;MAGvCF,kBAAkB,EAAEG,sBAAsB,EAAE;IAC9C,CAAC,CAAC;EACJ;EAEA,IAAIlC,SAASA,CAAA;IACX,OAAO,IAAI,CAACf,iBAAiB,CAACjB,GAAG,CAAC,WAAW,CAAc;EAC7D;EAEAvC,uBAAuBA,CAAC0G,EAAS;IAC/B,OAAO,IAAI,CAAC7D,kBAAkB,CAAC8D,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,IAAIA,EAAE,CAAC,CAACG,IAAI;EAC3D;EAMAtF,cAAcA,CAACN,KAAa;IAC1B,IAAI,CAACsD,SAAS,CAACuC,QAAQ,CAAC7F,KAAK,CAAC;EAChC;EAEAmC,WAAWA,CAACnC,KAAa;IACvB,MAAMf,UAAU,GAAG,IAAI,CAACqE,SAAS,CAACwC,EAAE,CAAC9F,KAAK,CAAC,CAACsB,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;IAEpE,IAAI,CAACtC,UAAU,EAAE;IAEjB,IAAI8G,OAAO,GAAuB;MAChC9G,UAAU,EAAG,IAAI,CAACqE,SAAS,CAACwC,EAAE,CAAC9F,KAAK,CAAC,CAACsB,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK;MAC9DsD,QAAQ,EAAE,IAAI,CAACvB,SAAS,CAACwC,EAAE,CAAC9F,KAAK,CAAC,CAACsB,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK;MACzDuD,QAAQ,EAAE,IAAI,CAACxB,SAAS,CAACwC,EAAE,CAAC9F,KAAK,CAAC,CAACsB,GAAG,CAAC,UAAU,CAAC,EAAEC,KAAK;MACzDwD,WAAW,EAAE,IAAI,CAACzB,SAAS,CAACwC,EAAE,CAAC9F,KAAK,CAAC,CAACsB,GAAG,CAAC,aAAa,CAAC,EAAEC,KAAK;MAC/DyD,MAAM,EAAE,IAAI,CAAC1B,SAAS,CAACwC,EAAE,CAAC9F,KAAK,CAAC,CAACsB,GAAG,CAAC,QAAQ,CAAC,EAAEC;KACjD;IACDyE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,OAAO,CAAC;IAElC,IAAI,CAACpC,eAAe,CAACxB,WAAW,CAAC4D,OAAO,CAAC,CAACvB,IAAI,CAACC,IAAI,IAAG;MACpD,IAAI,CAACnB,SAAS,CAACwC,EAAE,CAAC9F,KAAK,CAAC,CAACkG,UAAU,CAAC;QAAElC,QAAQ,EAAES;MAAI,CAAE,CAAC;IACzD,CAAC,CAAC;IAEF,MAAME,aAAa,GAAG,IAAI,CAACrB,SAAS,CAACwC,EAAE,CAAC9F,KAAK,CAAC;IAE9C;IACA,IAAIf,UAAU,KAAK,CAAC,EAAE;MACpB;MACA0F,aAAa,CAACrD,GAAG,CAAC,aAAa,CAAC,EAAEgE,aAAa,CAACvH,UAAU,CAAC6G,QAAQ,CAAC;MACpED,aAAa,CAACrD,GAAG,CAAC,QAAQ,CAAC,EAAEiE,eAAe,EAAE;MAC9CZ,aAAa,CAACrD,GAAG,CAAC,QAAQ,CAAC,EAAE6E,QAAQ,CAAC,EAAE,CAAC;KAC1C,MAAM,IAAIlH,UAAU,KAAK,CAAC,EAAE;MAC3B;MACA0F,aAAa,CAACrD,GAAG,CAAC,QAAQ,CAAC,EAAEgE,aAAa,CAACvH,UAAU,CAAC6G,QAAQ,CAAC;MAC/DD,aAAa,CAACrD,GAAG,CAAC,aAAa,CAAC,EAAEiE,eAAe,EAAE;MACnDZ,aAAa,CAACrD,GAAG,CAAC,aAAa,CAAC,EAAE6E,QAAQ,CAAC,EAAE,CAAC;KAC/C,MAAM;MACL;MACAxB,aAAa,CAACrD,GAAG,CAAC,aAAa,CAAC,EAAEiE,eAAe,EAAE;MACnDZ,aAAa,CAACrD,GAAG,CAAC,aAAa,CAAC,EAAE6E,QAAQ,CAAC,EAAE,CAAC;MAC9CxB,aAAa,CAACrD,GAAG,CAAC,QAAQ,CAAC,EAAEiE,eAAe,EAAE;MAC9CZ,aAAa,CAACrD,GAAG,CAAC,QAAQ,CAAC,EAAE6E,QAAQ,CAAC,EAAE,CAAC;;IAG3CxB,aAAa,CAACrD,GAAG,CAAC,aAAa,CAAC,EAAEkE,sBAAsB,EAAE;IAC1Db,aAAa,CAACrD,GAAG,CAAC,QAAQ,CAAC,EAAEkE,sBAAsB,EAAE;EAEvD;EAEAzC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACR,iBAAiB,CAAC6D,OAAO,EAAE;MAClC,IAAI,CAAC7D,iBAAiB,CAAC8D,gBAAgB,EAAE;MACzC;;IAEF,IAAI,IAAI,CAAC9D,iBAAiB,CAACC,KAAK,EAAE;MAChCwD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAAC1D,iBAAiB,CAAChB,KAAK,CAAC+B,SAAS,CAAC;MAEvE;MACA,MAAMgD,oBAAoB,GAAG,IAAI,CAAC/D,iBAAiB,CAAChB,KAAK,CAAC+B,SAAS,CAACiD,GAAG,CAAEC,YAAiB,KAAM;QAC9F,GAAGA,YAAY;QACfvH,UAAU,EAAEuH,YAAY,CAACvH,UAAU,CAACwH,QAAQ,EAAE,CAAC;OAChD,CAAC,CAAC;;MAEHT,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEK,oBAAoB,CAAC;MAE7D,IAAIP,OAAO,GAA6B;QACtCzC,SAAS,EAAEgD;OACZ;MAED,IAAI,CAAC1C,gBAAgB,CAAC8C,iBAAiB,CAACX,OAAO,CAAC,CAACvB,IAAI,CAACC,IAAI,IAAG;QAC3DuB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAExB,IAAI,CAAC;QACnC,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC,CAAC,CAACiC,KAAK,CAACC,KAAK,IAAG;QACfZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,CAAC;;EAEN;EAEAC,cAAcA,CAAC7G,KAAY;IACzB,OAAO,IAAI,CAACsD,SAAS,IAAI,IAAI,CAACA,SAAS,CAACjF,MAAM,GAAG2B,KAAK,IAAI,IAAI,CAACsD,SAAS,CAACwC,EAAE,CAAC9F,KAAK,CAAC,CAACsB,GAAG,CAAC,YAAY,CAAC,EAAEC,KAAK,KAAK,CAAC;EACnH;EAEAmD,gBAAgBA,CAAA;IACd,IAAI,CAACd,gBAAgB,CAACc,gBAAgB,EAAE,CAACF,IAAI,CAACC,IAAI,IAAG;MACnD,IAAI,CAACrG,aAAa,GAAGqG,IAAI;MACzBuB,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC7H,aAAa,CAAC;MAC/B,IAAI,CAACA,aAAa,CAAC0I,OAAO,CAACC,EAAE,IAAG;QAC9BA,EAAE,CAAC5H,aAAa,GAAG6H,IAAI,CAACC,KAAK,CAACF,EAAE,CAAC5H,aAAa,CAAC;MAEjD,CAAC,CAAC;MACF,IAAIsF,IAAI,CAACpG,MAAM,GAAG,CAAC,EAAC;QAClB,IAAI,CAACsF,eAAe,CAACuD,eAAe,EAAE,CAAC1C,IAAI,CAACC,IAAI,IAAG;UACjD,IAAI,CAACT,QAAQ,GAAGS,IAAI;UACpB,IAAI,CAACV,YAAY,CAACoD,WAAW,CAAC,IAAI,CAACnD,QAAQ,CAAC;QAC9C,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAAC,QAAAoD,CAAA,G;qBAxKM5D,kBAAkB,EAAAxF,EAAA,CAAAqJ,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAAvJ,EAAA,CAAAqJ,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAzJ,EAAA,CAAAqJ,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAA3J,EAAA,CAAAqJ,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA7J,EAAA,CAAAqJ,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAA/J,EAAA,CAAAqJ,iBAAA,CAAAW,EAAA,CAAAC,YAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlB1E,kBAAkB;IAAA2E,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClB/BzK,EAAA,CAAAM,cAAA,aAAkB;QAGVN,EAAA,CAAAC,SAAA,sBAA8F;QAClGD,EAAA,CAAAQ,YAAA,EAAM;QAGNR,EAAA,CAAAM,cAAA,aAAoB;QAEZN,EAAA,CAAAS,UAAA,IAAAkK,yCAAA,yBAMc,IAAAC,yCAAA;QAyFlB5K,EAAA,CAAAQ,YAAA,EAAS;QAIbR,EAAA,CAAAM,cAAA,aAAoB;QAEZN,EAAA,CAAAS,UAAA,IAAAoK,yCAAA,yBAKc,KAAAC,0CAAA;QA2MlB9K,EAAA,CAAAQ,YAAA,EAAS;;;QA3TKR,EAAA,CAAAW,SAAA,GAAkC;QAAlCX,EAAA,CAAAE,UAAA,UAAAF,EAAA,CAAA+K,eAAA,IAAAC,GAAA,EAAAhL,EAAA,CAAAiL,eAAA,IAAAC,GAAA,GAAkC,SAAAlL,EAAA,CAAAiL,eAAA,IAAAE,GAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}