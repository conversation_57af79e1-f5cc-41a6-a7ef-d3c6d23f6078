{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { StyleClassModule } from 'primeng/styleclass';\nimport { DividerModule } from 'primeng/divider';\nimport { ChartModule } from 'primeng/chart';\nimport { PanelModule } from 'primeng/panel';\nimport { ButtonModule } from 'primeng/button';\nimport { ProvidersRoutingModule } from './providers-routing.module';\nimport { ProvidersComponent } from './providers.component';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\nimport { FontAwesomeModule } from '@fortawesome/angular-fontawesome';\nimport { fas } from '@fortawesome/free-solid-svg-icons';\nimport { CardModule } from 'primeng/card';\nimport { InputTextModule } from 'primeng/inputtext';\nimport { DropdownModule } from 'primeng/dropdown';\nimport { PasswordModule } from 'primeng/password';\nimport { BadgeModule } from 'primeng/badge';\nimport { TagModule } from 'primeng/tag';\nimport { TooltipModule } from 'primeng/tooltip';\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\nimport { MessageModule } from 'primeng/message';\nimport { MessagesModule } from 'primeng/messages';\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\nimport { SkeletonModule } from 'primeng/skeleton';\nimport { RippleModule } from 'primeng/ripple';\nimport { DialogModule } from 'primeng/dialog';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@fortawesome/angular-fontawesome\";\nexport class ProvidersModule {\n  constructor(library) {\n    //library.addIcons(faSmile);\n    library.addIconPacks(fas);\n  }\n  static #_ = this.ɵfac = function ProvidersModule_Factory(t) {\n    return new (t || ProvidersModule)(i0.ɵɵinject(i1.FaIconLibrary));\n  };\n  static #_2 = this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n    type: ProvidersModule\n  });\n  static #_3 = this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n    imports: [CommonModule, ProvidersRoutingModule, DividerModule, StyleClassModule, ChartModule, PanelModule, ButtonModule, ReactiveFormsModule, BreadcrumbModule, FontAwesomeModule, CardModule, InputTextModule, DropdownModule, PasswordModule, BadgeModule, TagModule, TooltipModule, ConfirmDialogModule, MessageModule, MessagesModule, ProgressSpinnerModule, SkeletonModule, RippleModule, DialogModule]\n  });\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProvidersModule, {\n    declarations: [ProvidersComponent],\n    imports: [CommonModule, ProvidersRoutingModule, DividerModule, StyleClassModule, ChartModule, PanelModule, ButtonModule, ReactiveFormsModule, BreadcrumbModule, FontAwesomeModule, CardModule, InputTextModule, DropdownModule, PasswordModule, BadgeModule, TagModule, TooltipModule, ConfirmDialogModule, MessageModule, MessagesModule, ProgressSpinnerModule, SkeletonModule, RippleModule, DialogModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "StyleClassModule", "DividerModule", "ChartModule", "PanelModule", "ButtonModule", "ProvidersRoutingModule", "ProvidersComponent", "ReactiveFormsModule", "BreadcrumbModule", "FontAwesomeModule", "fas", "CardModule", "InputTextModule", "DropdownModule", "PasswordModule", "BadgeModule", "TagModule", "TooltipModule", "ConfirmDialogModule", "MessageModule", "MessagesModule", "ProgressSpinnerModule", "SkeletonModule", "RippleModule", "DialogModule", "ProvidersModule", "constructor", "library", "addIconPacks", "_", "i0", "ɵɵinject", "i1", "FaIconLibrary", "_2", "_3", "declarations", "imports"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\providers\\providers.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\r\nimport { CommonModule } from '@angular/common';\r\nimport { StyleClassModule } from 'primeng/styleclass';\r\nimport { DividerModule } from 'primeng/divider';\r\nimport { ChartModule } from 'primeng/chart';\r\nimport { PanelModule } from 'primeng/panel';\r\nimport { ButtonModule } from 'primeng/button';\r\nimport { ProvidersRoutingModule } from './providers-routing.module';\r\nimport { ProvidersComponent } from './providers.component';\r\nimport { ReactiveFormsModule } from '@angular/forms';\r\nimport { BreadcrumbModule } from 'primeng/breadcrumb';\r\nimport { FaIconLibrary, FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\nimport { fas } from '@fortawesome/free-solid-svg-icons';\r\nimport { CardModule } from 'primeng/card';\r\nimport { InputTextModule } from 'primeng/inputtext';\r\nimport { DropdownModule } from 'primeng/dropdown';\r\nimport { PasswordModule } from 'primeng/password';\r\nimport { BadgeModule } from 'primeng/badge';\r\nimport { TagModule } from 'primeng/tag';\r\nimport { TooltipModule } from 'primeng/tooltip';\r\nimport { ConfirmDialogModule } from 'primeng/confirmdialog';\r\nimport { MessageModule } from 'primeng/message';\r\nimport { MessagesModule } from 'primeng/messages';\r\nimport { ProgressSpinnerModule } from 'primeng/progressspinner';\r\nimport { SkeletonModule } from 'primeng/skeleton';\r\nimport { RippleModule } from 'primeng/ripple';\r\nimport { DialogModule } from 'primeng/dialog';\r\n\r\n@NgModule({\r\n    imports: [\r\n        CommonModule,\r\n        ProvidersRoutingModule,\r\n        DividerModule,\r\n        StyleClassModule,\r\n        ChartModule,\r\n        PanelModule,\r\n        ButtonModule,\r\n        ReactiveFormsModule,\r\n        BreadcrumbModule,\r\n        FontAwesomeModule,\r\n        CardModule,\r\n        InputTextModule,\r\n        DropdownModule,\r\n        PasswordModule,\r\n        BadgeModule,\r\n        TagModule,\r\n        TooltipModule,\r\n        ConfirmDialogModule,\r\n        MessageModule,\r\n        MessagesModule,\r\n        ProgressSpinnerModule,\r\n        SkeletonModule,\r\n        RippleModule,\r\n        DialogModule\r\n    ],\r\n    declarations: [ProvidersComponent]\r\n})\r\nexport class ProvidersModule {\r\n    constructor(library: FaIconLibrary){\r\n            //library.addIcons(faSmile);\r\n            library.addIconPacks(fas);\r\n        }\r\n }\r\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,sBAAsB,QAAQ,4BAA4B;AACnE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAAwBC,iBAAiB,QAAQ,kCAAkC;AACnF,SAASC,GAAG,QAAQ,mCAAmC;AACvD,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,SAAS,QAAQ,aAAa;AACvC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,YAAY,QAAQ,gBAAgB;;;AA+B7C,OAAM,MAAOC,eAAe;EACxBC,YAAYC,OAAsB;IAC1B;IACAA,OAAO,CAACC,YAAY,CAAClB,GAAG,CAAC;EAC7B;EAAC,QAAAmB,CAAA,G;qBAJIJ,eAAe,EAAAK,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAfT;EAAe;EAAA,QAAAU,EAAA,G;cA3BpBpC,YAAY,EACZM,sBAAsB,EACtBJ,aAAa,EACbD,gBAAgB,EAChBE,WAAW,EACXC,WAAW,EACXC,YAAY,EACZG,mBAAmB,EACnBC,gBAAgB,EAChBC,iBAAiB,EACjBE,UAAU,EACVC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,WAAW,EACXC,SAAS,EACTC,aAAa,EACbC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,YAAY,EACZC,YAAY;EAAA;;;2EAIPC,eAAe;IAAAW,YAAA,GAFT9B,kBAAkB;IAAA+B,OAAA,GAzB7BtC,YAAY,EACZM,sBAAsB,EACtBJ,aAAa,EACbD,gBAAgB,EAChBE,WAAW,EACXC,WAAW,EACXC,YAAY,EACZG,mBAAmB,EACnBC,gBAAgB,EAChBC,iBAAiB,EACjBE,UAAU,EACVC,eAAe,EACfC,cAAc,EACdC,cAAc,EACdC,WAAW,EACXC,SAAS,EACTC,aAAa,EACbC,mBAAmB,EACnBC,aAAa,EACbC,cAAc,EACdC,qBAAqB,EACrBC,cAAc,EACdC,YAAY,EACZC,YAAY;EAAA;AAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}