{"ast": null, "code": "import { environment } from '../../../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class ProvidersService {\n  constructor(http) {\n    this.http = http;\n  }\n  getProviders() {\n    return this.http.get(environment.solarApi + 'api/providers').toPromise().then(res => res.providers) // Προσαρμογή στη δομή της απόκρισης\n    .catch(error => {\n      console.error('Error fetching providers:', error);\n      return [];\n    });\n  }\n  getUserProviders() {\n    return this.http.get(environment.solarApi + 'api/user/providers').toPromise().then(res => res.providers) // Προσαρμογή στη δομή της απόκρισης\n    .catch(error => {\n      console.error('Error fetching user providers:', error);\n      return [];\n    });\n  }\n  saveUserProviders(request) {\n    return this.http.post(environment.solarApi + 'api/user/providers', request).toPromise().catch(error => {\n      console.error('Error saving user providers:', error);\n      throw error; // Ή επιστρέφεις ένα default αντικείμενο αν χρειάζεται\n    });\n  }\n  // Update individual user provider\n  updateUserProvider(providerId, configuration) {\n    return this.http.put(`${environment.solarApi}api/UserProviders/${this.getCurrentUserId()}/${providerId}`, JSON.stringify(configuration), {\n      headers: {\n        'Content-Type': 'application/json'\n      }\n    }).toPromise().catch(error => {\n      console.error('Error updating user provider:', error);\n      throw error;\n    });\n  }\n  // Delete individual user provider\n  deleteUserProvider(providerId) {\n    return this.http.delete(`${environment.solarApi}api/UserProviders/${this.getCurrentUserId()}/${providerId}`).toPromise().catch(error => {\n      console.error('Error deleting user provider:', error);\n      throw error;\n    });\n  }\n  // Helper method to get current user ID from JWT token\n  getCurrentUserId() {\n    // Since the backend uses JWT authentication and gets userId from claims,\n    // we don't need to pass userId explicitly - the backend will extract it from the token\n    // But the API expects userId in the URL, so we'll use a placeholder that the backend ignores\n    return 'me'; // The backend will use the actual userId from JWT claims\n  }\n  static #_ = this.ɵfac = function ProvidersService_Factory(t) {\n    return new (t || ProvidersService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ProvidersService,\n    factory: ProvidersService.ɵfac\n  });\n}", "map": {"version": 3, "names": ["environment", "ProvidersService", "constructor", "http", "getProviders", "get", "solarApi", "to<PERSON>romise", "then", "res", "providers", "catch", "error", "console", "getUserProviders", "saveUserProviders", "request", "post", "updateUserProvider", "providerId", "configuration", "put", "getCurrentUserId", "JSON", "stringify", "headers", "deleteUserProvider", "delete", "_", "i0", "ɵɵinject", "i1", "HttpClient", "_2", "factory", "ɵfac"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\service\\providers.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Product } from '../api/product';\r\nimport { Station } from '../api/station';\r\nimport { InvertPower } from '../api/invertpower';\r\nimport { GetHistoricDataResponse, GetProvidersResponse, GetStationDevicesResponse, GetStationsResponse, GetStationSumDataResponse, GetUserProvidersResponse, IProvider, IUserProvider, IUserProviderConfiguration, SaveUserProvidersResponse } from '../api/responses';\r\nimport { Device } from '../api/device';\r\nimport { GetHistoricDataRequest, GetStationDevicesRequest, GetStationSumDataRequest, SaveUserProvidersRequest } from '../api/requests';\r\nimport { environment } from '../../../environments/environment';\r\n\r\n\r\n@Injectable()\r\nexport class ProvidersService {\r\n\r\n    \r\n\r\n    constructor(private http: HttpClient) { }\r\n\r\n    \r\n    getProviders(): Promise<IProvider[]> {\r\n        return this.http.get<GetProvidersResponse>(environment.solarApi + 'api/providers')\r\n            .toPromise()\r\n            .then(res => res.providers) // Προσαρμογή στη δομή της απόκρισης\r\n            .catch(error => {\r\n                console.error('Error fetching providers:', error);\r\n                return [];\r\n            });\r\n    }\r\n\r\n    getUserProviders(): Promise<IUserProviderConfiguration[]> {\r\n        return this.http.get<GetUserProvidersResponse>(environment.solarApi + 'api/user/providers')\r\n            .toPromise()\r\n            .then(res => res.providers) // Προσαρμογή στη δομή της απόκρισης\r\n            .catch(error => {\r\n                console.error('Error fetching user providers:', error);\r\n                return [];\r\n            });\r\n    }\r\n\r\n    saveUserProviders(request: SaveUserProvidersRequest): Promise<SaveUserProvidersResponse> {\r\n        return this.http.post<SaveUserProvidersResponse>(\r\n                environment.solarApi + 'api/user/providers',\r\n                request\r\n            )\r\n            .toPromise()\r\n            .catch(error => {\r\n                console.error('Error saving user providers:', error);\r\n                throw error; // Ή επιστρέφεις ένα default αντικείμενο αν χρειάζεται\r\n            });\r\n    }\r\n\r\n    // Update individual user provider\r\n    updateUserProvider(providerId: number, configuration: string): Promise<any> {\r\n        return this.http.put(\r\n                `${environment.solarApi}api/UserProviders/${this.getCurrentUserId()}/${providerId}`,\r\n                JSON.stringify(configuration),\r\n                {\r\n                    headers: { 'Content-Type': 'application/json' }\r\n                }\r\n            )\r\n            .toPromise()\r\n            .catch(error => {\r\n                console.error('Error updating user provider:', error);\r\n                throw error;\r\n            });\r\n    }\r\n\r\n    // Delete individual user provider\r\n    deleteUserProvider(providerId: number): Promise<any> {\r\n        return this.http.delete(\r\n                `${environment.solarApi}api/UserProviders/${this.getCurrentUserId()}/${providerId}`\r\n            )\r\n            .toPromise()\r\n            .catch(error => {\r\n                console.error('Error deleting user provider:', error);\r\n                throw error;\r\n            });\r\n    }\r\n\r\n    // Helper method to get current user ID from JWT token\r\n    private getCurrentUserId(): string {\r\n        // Since the backend uses JWT authentication and gets userId from claims,\r\n        // we don't need to pass userId explicitly - the backend will extract it from the token\r\n        // But the API expects userId in the URL, so we'll use a placeholder that the backend ignores\r\n        return 'me'; // The backend will use the actual userId from JWT claims\r\n    }\r\n    \r\n}\r\n"], "mappings": "AAQA,SAASA,WAAW,QAAQ,mCAAmC;;;AAI/D,OAAM,MAAOC,gBAAgB;EAIzBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EAAgB;EAGxCC,YAAYA,CAAA;IACR,OAAO,IAAI,CAACD,IAAI,CAACE,GAAG,CAAuBL,WAAW,CAACM,QAAQ,GAAG,eAAe,CAAC,CAC7EC,SAAS,EAAE,CACXC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC,CAAC;IAAA,CAC3BC,KAAK,CAACC,KAAK,IAAG;MACXC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,EAAE;IACb,CAAC,CAAC;EACV;EAEAE,gBAAgBA,CAAA;IACZ,OAAO,IAAI,CAACX,IAAI,CAACE,GAAG,CAA2BL,WAAW,CAACM,QAAQ,GAAG,oBAAoB,CAAC,CACtFC,SAAS,EAAE,CACXC,IAAI,CAACC,GAAG,IAAIA,GAAG,CAACC,SAAS,CAAC,CAAC;IAAA,CAC3BC,KAAK,CAACC,KAAK,IAAG;MACXC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,EAAE;IACb,CAAC,CAAC;EACV;EAEAG,iBAAiBA,CAACC,OAAiC;IAC/C,OAAO,IAAI,CAACb,IAAI,CAACc,IAAI,CACbjB,WAAW,CAACM,QAAQ,GAAG,oBAAoB,EAC3CU,OAAO,CACV,CACAT,SAAS,EAAE,CACXI,KAAK,CAACC,KAAK,IAAG;MACXC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,MAAMA,KAAK,CAAC,CAAC;IACjB,CAAC,CAAC;EACV;EAEA;EACAM,kBAAkBA,CAACC,UAAkB,EAAEC,aAAqB;IACxD,OAAO,IAAI,CAACjB,IAAI,CAACkB,GAAG,CACZ,GAAGrB,WAAW,CAACM,QAAQ,qBAAqB,IAAI,CAACgB,gBAAgB,EAAE,IAAIH,UAAU,EAAE,EACnFI,IAAI,CAACC,SAAS,CAACJ,aAAa,CAAC,EAC7B;MACIK,OAAO,EAAE;QAAE,cAAc,EAAE;MAAkB;KAChD,CACJ,CACAlB,SAAS,EAAE,CACXI,KAAK,CAACC,KAAK,IAAG;MACXC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACf,CAAC,CAAC;EACV;EAEA;EACAc,kBAAkBA,CAACP,UAAkB;IACjC,OAAO,IAAI,CAAChB,IAAI,CAACwB,MAAM,CACf,GAAG3B,WAAW,CAACM,QAAQ,qBAAqB,IAAI,CAACgB,gBAAgB,EAAE,IAAIH,UAAU,EAAE,CACtF,CACAZ,SAAS,EAAE,CACXI,KAAK,CAACC,KAAK,IAAG;MACXC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,MAAMA,KAAK;IACf,CAAC,CAAC;EACV;EAEA;EACQU,gBAAgBA,CAAA;IACpB;IACA;IACA;IACA,OAAO,IAAI,CAAC,CAAC;EACjB;EAAC,QAAAM,CAAA,G;qBAzEQ3B,gBAAgB,EAAA4B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAhBhC,gBAAgB;IAAAiC,OAAA,EAAhBjC,gBAAgB,CAAAkC;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}