import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';
import { MenuItem, MessageService } from 'primeng/api';
import { Product } from '../../api/product';
import { ProductService } from '../../service/product.service';
import { Subscription, debounceTime } from 'rxjs';
import { LayoutService } from 'src/app/layout/service/app.layout.service';
import { SelectItem } from 'primeng/api';
import { DataView } from 'primeng/dataview';
import { StationsService } from '../../service/stations.service';
import { Station } from '../../api/station';
import { CacheService } from '../../service/cache.service';
import { DateUtilsService } from '../../service/date-utils.service';
import { ProvidersService } from '../../service/providers.service';
import { IProvider, IUserProvider } from '../../api/responses';
import { StationConfigurationService, StationConfiguration } from '../../service/station-configuration.service';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { GetHistoricDataRequest, SaveUserProvidersRequest } from '../../api/requests';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';
import { TrackerService, TrackerResponse } from '../../service/tracker.service';


@Component({
    templateUrl: './index.component.html', 
    styleUrls: ['./index.component.scss']
})
export class IndexComponent implements OnInit, OnDestroy {

    items!: MenuItem[];

    stations: Station[] = [];
    filteredStations: Station[] = [];
    paginatedStations: Station[] = [];

    chartData: any;

    chartOptions: any;

    subscription!: Subscription;

    sortOptionsCountry: SelectItem[] = [];

    sortOptionsStatus: SelectItem[] = [];

    sortOrder: number = 0;

    sortField: string = '';

    // Filtering properties
    searchTerm: string = '';
    selectedProvider: string = '';
    selectedStatus: string = '';

    // Pagination properties
    currentPage: number = 0;
    itemsPerPage: number = 20;
    totalItems: number = 0;

    // Pagination options - cached to avoid infinite loop
    paginationOptions: number[] = [20, 40, 80, 100];

    // Loading state
    isRefreshing: boolean = false;

    sourceCities: any[] = [];

    targetCities: any[] = [];

    orderCities: any[] = [];

    mapSrc:string;

    barOptions:any;
    lineOptions:any;
    stationsData:Map<string,any> = new Map();
    stationsRawData:Map<string,any> = new Map();
    stationsSumData:Map<string,number> = new Map();

    // Cache για τα computed values
    private currentPowerCache: Map<string, number> = new Map();
    private lastUpdateCache: Map<string, string> = new Map();
    private invertersCache: Map<string, number> = new Map();
    private deviceConfigurationsCache: Map<string, any[]> = new Map();

    // Tracker related caches
    private trackersCache: Map<string, TrackerResponse[]> = new Map();
    private trackerStatusCache: Map<string, 'none' | 'all-tracking' | 'has-errors'> = new Map();

    // Station configurations cache
    private stationConfigurations = new Map<string, StationConfiguration>();


    constructor(public layoutService: LayoutService,
        private stationsService: StationsService,
        private providersService: ProvidersService,
        private messageService: MessageService,
        private fb: FormBuilder,
        private router: Router,
        private cacheService: CacheService,
        private dateUtils: DateUtilsService,
        private trackerService: TrackerService,
        private stationConfigService: StationConfigurationService,
        private cdr: ChangeDetectorRef) {
        this.subscription = this.layoutService.configUpdate$
        .pipe(debounceTime(25))
        .subscribe((config) => {
         //   this.initChart();
        });

        this.initializeChartOptions();
    }

    ngOnInit() {
        this.getUserProviders();
        this.initializeConfigurations();
    }

    private initializeConfigurations() {
        // Subscribe to router events to refresh configurations when navigating back
        this.router.events.pipe(
            filter(event => event instanceof NavigationEnd)
        ).subscribe((event: NavigationEnd) => {
            if (event.url === '/app' || event.url === '/app/') {
                console.log('Navigated back to index, refreshing configurations');
                this.loadStationConfigurations();
            }
        });

        // Load station configurations initially
        this.loadStationConfigurations();
    }

    private loadStationConfigurations() {
        console.log('Loading station configurations...');
        this.stationConfigService.getAllConfigurations().subscribe({
            next: (configurations) => {
                console.log('Configurations loaded:', configurations.length);

                // Update local cache
                this.stationConfigurations.clear();
                configurations.forEach(config => {
                    this.stationConfigurations.set(config.stationId, config);
                });

                // Re-apply filters and sorting with updated display names
                if (this.stations && this.stations.length > 0) {
                    this.applyFilters();
                }

                // Force change detection when configurations update
                this.cdr.detectChanges();
            },
            error: (error) => {
                console.error('Error loading configurations:', error);
            }
        });
    }

    private initializeChartOptions() {
        this.barOptions = {
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              x: {
                display: false
              },
              y: {
                display: false
              }
            }
          };
        
          this.lineOptions = {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
              intersect: false,
              mode: 'index'
            },
            elements: {
              line: {
                tension: 0.4,
                borderWidth: 2
              },
              point: {
                radius: 0,
                hoverRadius: 4,
                hitRadius: 10
              }
            },
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                enabled: true,
                mode: 'index',
                intersect: false,
                position: 'nearest',
                external: null, // Ensure we use the default tooltip positioning
                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                titleColor: '#ffffff',
                bodyColor: '#ffffff',
                borderColor: 'rgba(255, 255, 255, 0.1)',
                borderWidth: 1,
                cornerRadius: 8,
                displayColors: false,
                padding: 12,
                caretPadding: 6,
                caretSize: 5,
                titleFont: {
                  size: 14,
                  weight: 'bold'
                },
                bodyFont: {
                  size: 13
                },
                filter: function(tooltipItem: any) {
                  // Εμφανίζουμε tooltip μόνο αν υπάρχουν δεδομένα
                  return tooltipItem.parsed.y !== null && tooltipItem.parsed.y !== undefined;
                },
                callbacks: {
                  title: function(context: any) {
                    //console.log("Tooltip title callback called!", context);
                    if (context && context.length > 0) {
                      const xValue = context[0].parsed.x;
                      //console.log("xValue: " + xValue);
                      if (xValue !== undefined && xValue !== null) {
                        // Απλή μετατροπή: decimal hours σε HH:MM
                        const hours = Math.floor(xValue);
                        const minutes = Math.round((xValue - hours) * 60);
                        const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
                        return `Time: ${formattedTime}`;
                      }
                    }
                    return 'Time: --:--';
                  },
                  label: function(context: any) {
                    const label = context.dataset.label || '';
                    const value = context.parsed.y;
                    return `${label}: ${value.toFixed(2).replace('.', ',')} kW`;
                  }
                }
              }
            },
            scales: {
              x: {
                type: 'linear',
                display: true,
                position: 'bottom',
                min: 0,
                max: 24,
                ticks: {
                  stepSize: 12,
                  callback: function(value: any) {
                    // Static values: 00:00, 12:00, 24:00
                    if (value === 0) return '00:00';
                    if (value === 12) return '12:00';
                    if (value === 24) return '24:00';
                    return '';
                  },
                  color: '#374151',
                  font: {
                    size: 10,
                    weight: 'bold'
                  },
                  padding: 4
                },
                grid: {
                  display: false
                },
                border: {
                  display: true,
                  color: '#374151',
                  width: 2
                }
              },
              y: {
                display: true,
                position: 'left',
                title: {
                  display: true,
                  text: 'kW',
                  color: '#374151',
                  font: {
                    size: 12,
                    weight: 'bold'
                  }
                },
                ticks: {
                  maxTicksLimit: 3,
                  callback: function(value: any) {
                    // Will be dynamically set per station
                    return value.toFixed(0);
                  },
                  color: '#374151',
                  font: {
                    size: 10,
                    weight: 'bold'
                  },
                  padding: 4,
                  maxRotation: 0,
                  minRotation: 0
                },
                grid: {
                  display: false
                },
                border: {
                  display: true,
                  color: '#374151',
                  width: 2
                }
              }
            },
            animation: {
              duration: 750,
              easing: 'easeInOutQuart'
            },
            layout: {
              padding: {
                top: 20,
                bottom: 5,
                left: 5,
                right: 5
              }
            }
          };


          
        
    }

    getUserProviders(){
      this.isRefreshing = true;
      this.providersService.getUserProviders().then(providersData => {
        if (providersData.length > 0){
          this.stationsService.getUserStations().then(stationsData => {
            this.cacheService.setStations(stationsData);
            this.stations = stationsData;
            console.log("stations set")
            console.log(stationsData)

            // Initialize filter options
            this.initializeFilterOptions();

            // Apply filters and pagination
            this.applyFilters();

            // Φορτώνουμε τα δεδομένα για κάθε σταθμό
            this.loadAllStationsData();

            this.isRefreshing = false;
          });
        }else{
          this.router.navigate(['/app/providers']);
        }
      }).catch(error => {
        console.error('Error loading providers:', error);
        this.isRefreshing = false;
      });
    }

    // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών
    loadAllStationsData() {
        if (!this.stations || this.stations.length === 0) return;
        
        // Φορτώνουμε τα δεδομένα για κάθε σταθμό
        this.stations.forEach(station => {
            this.loadStationData(station);
        });
    }

    // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού
    loadStationData(station: Station) {
        if (!station) return;
        
        const now = new Date();
        const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();
        const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();
        
        let request: GetHistoricDataRequest = {
            devIds: station.deviceIds,
            devTypeId: 1,
            startDateTime: formattedStartDate,
            endDateTime: formattedEndDate,
            separated: true,
            searchType: null,
            stationId: station.id
        };
        
        this.stationsService.getStationHistoricData(request).then(data => {
            if (data && data.data && data.data.length > 0) {
                // Filter data to not show beyond current time
                const filteredData = this.filterDataByCurrentTime(data.data);

                const documentStyle = getComputedStyle(document.documentElement);

                // Βρίσκουμε τις μοναδικές ημερομηνίες με σειρά
                const uniqueDates = Array.from(new Set(data.data.map(d => d.dateTime)));
                const uniqueDateDescriptions = Array.from(new Set(data.data.map(d => d.dateDescription)));
                // Βρίσκουμε τους μοναδικούς inverters
                const uniqueInverters = Array.from(new Set(data.data.map(d => d.name)));

                var datasets:any[] = [];
                // const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));
                const activePowerByName: Record<string, number[]> = {};

                data.data.forEach(d => {
                    if (!activePowerByName[d.name]) {
                        activePowerByName[d.name] = [];
                    }
                    activePowerByName[d.name].push(d.activePower);
                });

                uniqueInverters.forEach(inv => {
                    var color = this.getRandomColor();
                    datasets.push({
                        label: inv,
                        data: activePowerByName[inv],
                        fill: false,
                        backgroundColor: color,
                        borderColor: color,
                        tension: .4
                    });

                });

                const firstName = data.data[0].name; // Παίρνουμε το πρώτο name
                const dateTimesForFirstName = data.data
                .filter(d => d.name === firstName) // Φιλτράρουμε μόνο τα αντικείμενα με το ίδιο name
                .map(d => d.dateTime); // Παίρνουμε μόνο τα dateTime

                // Μετατρέπουμε τα δεδομένα για στατικό άξονα X (0-24 ώρες) και κρατάμε time mapping
                const convertedDatasets = this.convertDataForStaticXAxis(datasets, dateTimesForFirstName);

                const lineData = {
                    labels: [], // Κενά labels για linear scale
                    datasets: convertedDatasets
                };



                // const lineData = {
                //     labels: filteredData.map((e, index) =>
                //         index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''
                //     ),
                //     datasets: [
                //         {
                //             label: 'Active Power',
                //             data: filteredData.map(e => e.activePower),
                //             fill: false,
                //             backgroundColor: documentStyle.getPropertyValue('--primary-500'),
                //             borderColor: documentStyle.getPropertyValue('--primary-500'),
                //             tension: .4
                //         },
                //         {
                //             label: 'Total Input Power',
                //             data: filteredData.map(e => e.totalInputPower),
                //             fill: false,
                //             backgroundColor: documentStyle.getPropertyValue('--primary-200'),
                //             borderColor: documentStyle.getPropertyValue('--primary-200'),
                //             tension: .4
                //         }
                //     ]
                // };

                // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού
                this.stationsData.set(station.id, lineData);
                this.stationsRawData.set(station.id, filteredData);
                this.stationsSumData.set(station.id, data.sum);

                // Set fallback inverters count from raw data if device configs not loaded yet
                if (!station.invertersCount) {
                    station.invertersCount = uniqueInverters.length;
                }

                // Καθαρίζουμε το cache για αυτό το station
                this.clearStationCache(station.id);
            } else {
                // Δεν υπάρχουν δεδομένα για αυτόν τον σταθμό
                console.warn(`No data available for station ${station.name} (${station.id})`);

                // Αποθηκεύουμε κενά δεδομένα για να μην προσπαθήσουμε ξανά
                this.stationsData.set(station.id, { labels: [], datasets: [] });
                this.stationsRawData.set(station.id, []);
                this.stationsSumData.set(station.id, null);
            }
        }).catch(error => {
            console.error(`Error loading data for station ${station.name} (${station.id}):`, error);

            // Αποθηκεύουμε κενά δεδομένα σε περίπτωση error
            this.stationsData.set(station.id, { labels: [], datasets: [] });
            this.stationsRawData.set(station.id, []);
            this.stationsSumData.set(station.id, null);
        });

        // Φορτώνουμε tracker data για αυτόν τον σταθμό
        this.loadStationTrackers(station.id);

        // Φορτώνουμε device configurations για σωστό count
        this.loadStationDeviceConfigurations(station.id);
    }
    

    getRandomColor(){
        // Use variations of the logo blue color (#191C53)
        const logoBlueVariations = [
            '#191C53', // Main logo blue
            '#2a2f7a', // Lighter variation
            '#0f1240', // Darker variation
            '#3d4299', // Even lighter
            '#1a1e5a', // Slightly lighter than main
            '#252a66', // Medium variation
        ];

        return logoBlueVariations[Math.floor(Math.random() * logoBlueVariations.length)];
    }

    // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα
    getStationsRealTimeData(station: Station) {
        // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν
        if (station && station.id && this.stationsData.has(station.id)) {
            return this.stationsData.get(station.id);
        }
        
        // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null
        return null;
    }

    // Load device configurations for accurate device count
    private loadStationDeviceConfigurations(stationId: string) {
        if (!stationId) return;

        this.stationConfigService.getDevices(stationId)
            .subscribe({
                next: (devices) => {
                    this.deviceConfigurationsCache.set(stationId, devices);

                    // Update station object with pre-calculated inverters count
                    const station = this.stations.find(s => s.id === stationId);
                    if (station) {
                        station.invertersCount = devices.length;
                        console.log(`Station ${stationId}: Set invertersCount to ${devices.length} from device configs`);
                    }

                    // Clear inverters cache to force recalculation
                    this.invertersCache.delete(stationId);
                },
                error: (error) => {
                    console.warn(`Could not load device configurations for station ${stationId}:`, error);
                    // Set empty array so we don't keep trying
                    this.deviceConfigurationsCache.set(stationId, []);

                    // Set fallback count using raw data
                    const station = this.stations.find(s => s.id === stationId);
                    if (station) {
                        const data = this.stationsRawData.get(stationId);
                        station.invertersCount = data && data.length > 0
                            ? new Set(data.map(item => item.name)).size
                            : 0;
                    }
                }
            });
    }

    // Clear cache για ένα συγκεκριμένο station
    private clearStationCache(stationId: string) {
        // Δεν χρειάζεται να διαγράψουμε chart options cache αφού δεν το χρησιμοποιούμε πια
        this.currentPowerCache.delete(stationId);
        this.lastUpdateCache.delete(stationId);
        this.invertersCache.delete(stationId);
        this.trackersCache.delete(stationId);
        this.trackerStatusCache.delete(stationId);
        this.deviceConfigurationsCache.delete(stationId);
    }

    getStationsSumData(stationId:string){
      return this.stationsSumData.get(stationId);
    }

    getStationsInverters(stationId:string){
      // Χρησιμοποιούμε cache
      if (this.invertersCache.has(stationId)) {
        console.log("Cache:" + stationId + " " + this.invertersCache.get(stationId)!);
        return this.invertersCache.get(stationId)!;
      }

      // Προτεραιότητα στα device configurations (πιο ακριβή)
      const deviceConfigs = this.deviceConfigurationsCache.get(stationId);
      let result = 0;
      
      if (deviceConfigs && deviceConfigs.length > 0) {
        console.log("Configs:" + stationId + " " + deviceConfigs.length);
        // Μετράμε τα device configurations
        result = deviceConfigs.length;
      } else {
        // Fallback στα raw data από stations API
        const data = this.stationsRawData.get(stationId);
        if (data && data.length > 0) {
          result = new Set(data.map(item => item.name)).size;
        }
      }

      // Αποθηκεύουμε στο cache
      this.invertersCache.set(stationId, result);
      return result;
    }

    getStationLastUpdate(stationId: string) {
      // Χρησιμοποιούμε cache
      if (this.lastUpdateCache.has(stationId)) {
        return this.lastUpdateCache.get(stationId)!;
      }

      const data = this.stationsRawData.get(stationId);
      if (!data || data.length === 0) {
        this.lastUpdateCache.set(stationId, "-");
        return "-";
      }

      const latest = data.reduce((latestSoFar, current) => {
        return new Date(current.dateTime).getTime() > new Date(latestSoFar.dateTime).getTime()
          ? current
          : latestSoFar;
      });

      const result = new Date(latest.dateTime).toLocaleString("en-GB", {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false,
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        timeZone: 'Europe/Athens'
      });

      // Αποθηκεύουμε στο cache
      this.lastUpdateCache.set(stationId, result);
      return result;
    }

    getCurrentPower(stationId: string): number {
      // Χρησιμοποιούμε cache
      if (this.currentPowerCache.has(stationId)) {
        return this.currentPowerCache.get(stationId)!;
      }

      const data = this.stationsRawData.get(stationId);
      if (!data || data.length === 0) {
        this.currentPowerCache.set(stationId, 0);
        return 0;
      }

      // Find the latest timestamp
      const latestTimestamp = Math.max(...data.map(item => new Date(item.dateTime).getTime()));

      // Get all entries with the latest timestamp and sum their activePower
      const latestEntries = data.filter(item =>
        new Date(item.dateTime).getTime() === latestTimestamp
      );

      const result = latestEntries.reduce((sum, item) => sum + (item.activePower || 0), 0);

      // Αποθηκεύουμε στο cache
      this.currentPowerCache.set(stationId, result);
      return result;
    }

    // Get chart options with dynamic axis values for each station
    getChartOptionsForStation(station: Station): any {
      // Δεν χρησιμοποιούμε cache για chart options γιατί τα δεδομένα αλλάζουν

      // Shallow copy των options και manual copy των callbacks (JSON.parse χάνει τις functions)
      const baseOptions = {
        ...this.lineOptions,
        plugins: {
          ...this.lineOptions.plugins,
          tooltip: {
            ...this.lineOptions.plugins.tooltip,
            callbacks: {
              ...this.lineOptions.plugins.tooltip.callbacks
            }
          }
        },
        scales: {
          ...this.lineOptions.scales,
          x: { ...this.lineOptions.scales.x },
          y: { ...this.lineOptions.scales.y }
        }
      };

      // Get station data for X-axis calculation
      const stationData = this.stationsData.get(station.id);
      const rawData = this.stationsRawData.get(station.id);

      // Calculate Y-axis values με καλύτερη κλιμάκωση
      let maxDataValue = 0;

      // Get max value from station data
      if (rawData && rawData.length > 0) {
        maxDataValue = Math.max(...rawData.map((item: any) => item.activePower || 0));
      }

      // Αν δεν υπάρχουν δεδομένα, χρησιμοποιούμε το calculated power
      if (maxDataValue === 0) {
        const calculatedPower = this.getStationsSumData(station.id);
        if (calculatedPower && calculatedPower > 0) {
          maxDataValue = calculatedPower;
        }
      }

      // Προσθέτουμε 10% περιθώριο στο ταβάνι (λιγότερο από πριν)
      let maxValue = maxDataValue * 1.1;

      // Πιο φυσική στρογγυλοποίηση - κρατάμε πιο κοντά στα πραγματικά δεδομένα
      if (maxValue <= 1) {
        maxValue = Math.ceil(maxValue * 10) / 10; // 0.1, 0.2, 0.3, etc.
      } else if (maxValue <= 5) {
        maxValue = Math.ceil(maxValue * 2) / 2; // 0.5 increments: 1.5, 2.0, 2.5, etc.
      } else if (maxValue <= 20) {
        maxValue = Math.ceil(maxValue); // 1 unit increments: 6, 7, 8, 9, 10, etc.
      } else if (maxValue <= 100) {
        maxValue = Math.ceil(maxValue / 2) * 2; // 2 unit increments: 22, 24, 26, etc.
      } else if (maxValue <= 500) {
        maxValue = Math.ceil(maxValue / 5) * 5; // 5 unit increments: 105, 110, 115, etc.
      } else {
        maxValue = Math.ceil(maxValue / 10) * 10; // 10 unit increments για μεγάλες τιμές
      }

      // Εξασφαλίζουμε ότι έχουμε τουλάχιστον κάποια τιμή
      if (maxValue === 0 || maxValue < 0.1) {
        maxValue = 1; // Minimum 1 kW για καλύτερη εμφάνιση
      }

      // Debug logging (προσωρινό)
      //console.log(`Station ${station.id}: maxDataValue=${maxDataValue}, maxValue=${maxValue}`);

      // Update Y-axis configuration - ακριβώς 3 τιμές
      const midValue = maxValue / 2;

      baseOptions.scales.y.min = 0;
      baseOptions.scales.y.max = maxValue;

      // Καθορίζουμε ακριβώς τις τιμές που θέλουμε
      baseOptions.scales.y.ticks = {
        stepSize: midValue, // Βήμα ίσο με τη μέση τιμή
        maxTicksLimit: 3,   // Ακριβώς 3 ticks
        callback: function(value: any) {
          // Εμφανίζουμε μόνο τις 3 συγκεκριμένες τιμές
          if (value === 0) return '0';
          if (value === midValue) return Math.round(midValue).toString();
          if (value === maxValue) return Math.round(maxValue).toString();
          return ''; // Κρύβουμε όλες τις άλλες τιμές
        },
        color: '#374151',
        font: {
          size: 12,
          weight: 'bold'
        }
      };

      // X-axis είναι τώρα στατικός με linear scale - δεν χρειάζεται override

      // Debug logging
     // console.log("Chart options for station", station.id, baseOptions);
     // console.log("Tooltip config:", baseOptions.plugins?.tooltip);

      // Επιστρέφουμε τα options χωρίς caching
      return baseOptions;
    }

    // Μετατροπή δεδομένων για στατικό άξονα X (0-24 ώρες)
    private convertDataForStaticXAxis(datasets: any[], dateTimes: string[]): any[] {
        return datasets.map(dataset => {
            const convertedData: {x: number, y: number}[] = [];

            dataset.data.forEach((value: number, index: number) => {
                if (index < dateTimes.length) {
                    const dateTime = new Date(dateTimes[index]);
                    const hours = dateTime.getHours() + (dateTime.getMinutes() / 60);
                    convertedData.push({x: hours, y: value});
                }
            });

            return {
                ...dataset,
                data: convertedData
            };
        });
    }

    // Helper μέθοδος για μορφοποίηση αριθμών με κόμμα
    formatNumber(value: number, decimals: number = 2): string {
        return value.toFixed(decimals).replace('.', ',');
    }

    // Configuration helper methods with error handling
    getDisplayName(stationId: string, originalName: string): string {
        try {
            if (!stationId) return originalName || 'N/A';
            const config = this.stationConfigurations.get(stationId);
            return config?.customName || config?.name || originalName || 'N/A';
        } catch (error) {
            console.warn(`Error getting display name for station ${stationId}:`, error);
            return originalName || 'N/A';
        }
    }

    getOwner(stationId: string): string {
        try {
            if (!stationId) return 'N/A';
            const config = this.stationConfigurations.get(stationId);
            return config?.owner || 'N/A';
        } catch (error) {
            console.warn(`Error getting owner for station ${stationId}:`, error);
            return 'N/A';
        }
    }

    getPortfolio(stationId: string): string {
        try {
            if (!stationId) return 'N/A';
            const config = this.stationConfigurations.get(stationId);
            return config?.portfolio || 'N/A';
        } catch (error) {
            console.warn(`Error getting portfolio for station ${stationId}:`, error);
            return 'N/A';
        }
    }

    getCountry(stationId: string): string {
        try {
            if (!stationId) return 'N/A';
            const config = this.stationConfigurations.get(stationId);
            return config?.country || 'N/A';
        } catch (error) {
            console.warn(`Error getting country for station ${stationId}:`, error);
            return 'N/A';
        }
    }

    getPrefecture(stationId: string): string {
        try {
            if (!stationId) return 'N/A';
            const config = this.stationConfigurations.get(stationId);
            return config?.prefecture || 'N/A';
        } catch (error) {
            console.warn(`Error getting prefecture for station ${stationId}:`, error);
            return 'N/A';
        }
    }

    getInstalledCapacity(stationId: string): number {
        try {
            if (!stationId) return 0;
            const config = this.stationConfigurations.get(stationId);
            return config?.installedCapacityKW || 0;
        } catch (error) {
            console.warn(`Error getting installed capacity for station ${stationId}:`, error);
            return 0;
        }
    }

    getDataProvider(stationId: string, originalProvider: string): string {
        try {
            if (!stationId) return originalProvider || 'N/A';
            const config = this.stationConfigurations.get(stationId);
            return config?.dataProvider || originalProvider || 'N/A';
        } catch (error) {
            console.warn(`Error getting data provider for station ${stationId}:`, error);
            return originalProvider || 'N/A';
        }
    }

    onSortChange(event: any) {
        const value = event.value;

        if (value.indexOf('!') === 0) {
            this.sortOrder = -1;
            this.sortField = value.substring(1, value.length);
        } else {
            this.sortOrder = 1;
            this.sortField = value;
        }
    }

    onFilter(dv: DataView, event: Event) {
        dv.filter((event.target as HTMLInputElement).value);
    }

    // Initialize filter options based on available stations
    initializeFilterOptions() {
        // Get unique providers
        const providers = [...new Set(this.stations.map(station => station.provider))];
        this.sortOptionsCountry = providers.map(provider => ({
            label: provider,
            value: provider
        }));

        // Get unique statuses
        const statuses = [...new Set(this.stations.map(station => station.status))];
        this.sortOptionsStatus = statuses.map(status => ({
            label: status,
            value: status
        }));
    }

    // Apply all filters and update pagination
    applyFilters() {
        let filtered = [...this.stations];

        // Apply search filter
        if (this.searchTerm) {
            const searchLower = this.searchTerm.toLowerCase();
            filtered = filtered.filter(station =>
                station.name.toLowerCase().includes(searchLower) ||
                station.provider.toLowerCase().includes(searchLower) ||
                (station.location && station.location.toLowerCase().includes(searchLower))
            );
        }

        // Apply provider filter
        if (this.selectedProvider) {
            filtered = filtered.filter(station => station.provider === this.selectedProvider);
        }

        // Apply status filter
        if (this.selectedStatus) {
            filtered = filtered.filter(station => station.status === this.selectedStatus);
        }

        // Sort stations alphabetically by display name (from station configurations)
        filtered = filtered.sort((a, b) => {
            const nameA = this.getDisplayName(a.id, a.name)?.toLowerCase() || '';
            const nameB = this.getDisplayName(b.id, b.name)?.toLowerCase() || '';
            return nameA.localeCompare(nameB);
        });

        this.filteredStations = filtered;
        this.totalItems = filtered.length;
        this.currentPage = 0; // Reset to first page

        // Update pagination options when totalItems changes
        this.updatePaginationOptions();
        this.updatePagination();
    }

    // Update pagination options when totalItems changes
    private updatePaginationOptions() {
        this.paginationOptions = [20, 40, 80, 100];
        if (this.totalItems > 100) {
            this.paginationOptions.push(this.totalItems);
        }
    }

    // Update pagination based on current page
    updatePagination() {
        const startIndex = this.currentPage * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        this.paginatedStations = this.filteredStations.slice(startIndex, endIndex);
    }

    // Handle search input change
    onSearchChange(event: Event) {
        this.searchTerm = (event.target as HTMLInputElement).value;
        this.applyFilters();
    }

    // Handle provider filter change
    onProviderChange(event: any) {
        this.selectedProvider = event.value || '';
        this.applyFilters();
    }

    // Handle status filter change
    onStatusChange(event: any) {
        this.selectedStatus = event.value || '';
        this.applyFilters();
    }

    // Handle pagination change
    onPageChange(event: any) {
        this.currentPage = event.page;
        this.itemsPerPage = event.rows;
        this.updatePagination();
    }

    // Refresh data
    refreshData() {
        // Καθαρίζουμε όλα τα caches
        this.clearAllCaches();
        this.getUserProviders();
    }

    // Clear όλα τα caches
    private clearAllCaches() {
        // Δεν χρειάζεται να καθαρίσουμε chart options cache αφού δεν το χρησιμοποιούμε πια
        this.currentPowerCache.clear();
        this.lastUpdateCache.clear();
        this.invertersCache.clear();
        this.deviceConfigurationsCache.clear();
    }

    // Clear all filters
    clearFilters() {
        this.searchTerm = '';
        this.selectedProvider = '';
        this.selectedStatus = '';
        this.applyFilters();
    }

    // Check if station has equipment data
    hasEquipmentData(station: Station): boolean {
        const inverters = this.getStationsInverters(station.id || '');
        return inverters > 0 ||
               (station.mmpt && station.mmpt > 0) ||
               (station.string && station.string > 0) ||
               (station.pvn && station.pvn > 0);
    }

    private filterDataByCurrentTime(data: any[]): any[] {
        const now = new Date();
        const currentTime = now.getTime();

        return data.filter(item => {
            const itemDateTime = new Date(item.dateTime);
            const itemTime = itemDateTime.getTime();

            // Only include data points that are not in the future
            return itemTime <= currentTime;
        });
    }


    initMap(){
        this.mapSrc = "https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1"
    }

    // initChart() {
    //     const documentStyle = getComputedStyle(document.documentElement);
    //     const textColor = documentStyle.getPropertyValue('--text-color');
    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');
    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');

    //     this.chartData = {
    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],
    //         datasets: [
    //             {
    //                 label: 'First Dataset',
    //                 data: [65, 59, 80, 81, 56, 55, 40],
    //                 fill: false,
    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),
    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),
    //                 tension: .4
    //             },
    //             {
    //                 label: 'Second Dataset',
    //                 data: [28, 48, 40, 19, 86, 27, 90],
    //                 fill: false,
    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),
    //                 borderColor: documentStyle.getPropertyValue('--green-600'),
    //                 tension: .4
    //             }
    //         ]
    //     };

    //     this.chartOptions = {
    //         plugins: {
    //             legend: {
    //                 labels: {
    //                     color: textColor
    //                 }
    //             }
    //         },
    //         scales: {
    //             x: {
    //                 ticks: {
    //                     color: textColorSecondary
    //                 },
    //                 grid: {
    //                     color: surfaceBorder,
    //                     drawBorder: false
    //                 }
    //             },
    //             y: {
    //                 ticks: {
    //                     color: textColorSecondary
    //                 },
    //                 grid: {
    //                     color: surfaceBorder,
    //                     drawBorder: false
    //                 }
    //             }
    //         }
    //     };
    // }

    // TrackBy function για βελτίωση απόδοσης του *ngFor
    trackByStationId(index: number, station: Station): string {
        return station.id || index.toString();
    }

    // Get communication status class for text and circle color
    getCommunicationStatusClass(stationId: string): string {
        const now = new Date();
        const currentHour = now.getHours();

        // Γκρι σκούρο από 22:00-06:00
        if (currentHour >= 22 || currentHour < 6) {
            return 'comm-status-gray';
        }

        const lastUpdateStr = this.getStationLastUpdate(stationId);
        if (lastUpdateStr === '-') {
            return 'comm-status-red'; // Red if no data
        }

        try {
            // Parse the last update time (format: "DD/MM/YYYY, HH:mm")
            const [datePart, timePart] = lastUpdateStr.split(', ');
            const [day, month, year] = datePart.split('/').map(Number);
            const [hours, minutes] = timePart.split(':').map(Number);

            const lastUpdate = new Date(year, month - 1, day, hours, minutes);
            const hoursDiff = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60);

            console.log(`Station ${stationId}: Last update: ${lastUpdateStr}, Hours diff: ${hoursDiff.toFixed(2)}`);

            if (hoursDiff <= 1) {
                return 'comm-status-green'; // Green - less than 1 hour
            } else if (hoursDiff <= 2) {
                return 'comm-status-orange'; // Orange - 1-2 hours
            } else {
                return 'comm-status-red'; // Red - more than 2 hours
            }
        } catch (error) {
            console.error(`Error parsing time for station ${stationId}: ${lastUpdateStr}`, error);
            return 'comm-status-red'; // Red if parsing fails
        }
    }

    // Tracker methods
    loadStationTrackers(stationId: string): void {
        if (!stationId) return;

        this.trackerService.getTrackersByStation(stationId).subscribe({
            next: (trackers) => {
                this.trackersCache.set(stationId, trackers);

                if (trackers && trackers.length > 0) {
                    // Load data for each tracker to check status
                    let completedRequests = 0;
                    let hasErrors = false;

                    trackers.forEach(tracker => {
                        this.trackerService.getTrackerData(tracker.id).subscribe({
                            next: (data) => {
                                tracker.trackingData = data.trackingData;
                                tracker.windSpeed = data.windSpeed;
                                tracker.hasError = data.hasError;
                                tracker.errorMessage = data.errorMessage;

                                // Check if this tracker has non-tracking status
                                const hasNonTrackingStatus = data.trackingData?.some(trackingData =>
                                    trackingData.status && !trackingData.status.toLowerCase().includes('tracking')
                                );

                                if (hasNonTrackingStatus || data.hasError) {
                                    hasErrors = true;
                                }

                                completedRequests++;
                                if (completedRequests === trackers.length) {
                                    // All requests completed, check time delays as well
                                    const hasTimeDelayIssues = this.checkNextTrackingTimeDelays(trackers);
                                    this.trackerStatusCache.set(stationId, (hasErrors || hasTimeDelayIssues) ? 'has-errors' : 'all-tracking');
                                }
                            },
                            error: (error) => {
                                console.error(`Error loading tracker data for station ${stationId}:`, error);
                                hasErrors = true;
                                completedRequests++;
                                if (completedRequests === trackers.length) {
                                    // Αν υπάρχουν errors, δεν χρειάζεται να ελέγξουμε χρονικές αποκλίσεις
                                    this.trackerStatusCache.set(stationId, 'has-errors');
                                }
                            }
                        });
                    });
                } else {
                    // No trackers for this station
                    this.trackerStatusCache.set(stationId, 'none');
                }
            },
            error: (error) => {
                console.error(`Error loading trackers for station ${stationId}:`, error);
                this.trackerStatusCache.set(stationId, 'none');
            }
        });
    }

    getTrackerCount(stationId: string): string {
        const trackers = this.trackersCache.get(stationId);
        if (!trackers || trackers.length === 0) {
            return '0';
        }

        // Μετράμε το συνολικό πλήθος των TrackingData από όλους τους trackers
        let totalTrackingData = 0;
        trackers.forEach(tracker => {
            if (tracker.trackingData && tracker.trackingData.length > 0) {
                totalTrackingData += tracker.trackingData.length;
            }
        });

        return totalTrackingData.toString();
    }

    // Έλεγχος χρονικής απόκλισης στο Next Tracking
    private checkNextTrackingTimeDelays(trackers: TrackerResponse[]): boolean {
        if (!trackers || trackers.length === 0) {
            return false;
        }

        // Συλλέγουμε όλα τα Next Tracking times από όλους τους trackers
        const allNextTrackingTimes: Date[] = [];

        trackers.forEach(tracker => {
            if (tracker.trackingData && tracker.trackingData.length > 0) {
                tracker.trackingData.forEach(data => {
                    if (data.nextTracking) {
                        try {
                            // Μετατρέπουμε το nextTracking string σε Date
                            // Υποθέτουμε format HH:mm:ss (π.χ. "11:15:00")
                            const timeParts = data.nextTracking.split(':');
                            if (timeParts.length >= 2) {
                                const today = new Date();
                                const nextTrackingTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(),
                                    parseInt(timeParts[0]), parseInt(timeParts[1]), parseInt(timeParts[2] || '0'));
                                allNextTrackingTimes.push(nextTrackingTime);
                            }
                        } catch (error) {
                            console.error('Error parsing nextTracking time:', data.nextTracking, error);
                        }
                    }
                });
            }
        });

        if (allNextTrackingTimes.length === 0) {
            return false; // Δεν υπάρχουν δεδομένα για έλεγχο
        }

        // Βρίσκουμε τον πιο προχωρημένο χρονικά tracker (μέγιστος χρόνος)
        const latestTime = new Date(Math.max(...allNextTrackingTimes.map(time => time.getTime())));

        // Ελέγχουμε αν κάποιος tracker έχει απόκλιση μεγαλύτερη από 20 λεπτά
        const twentyMinutesInMs = 20 * 60 * 1000; // 20 λεπτά σε milliseconds

        for (const time of allNextTrackingTimes) {
            const timeDifference = latestTime.getTime() - time.getTime();
            if (timeDifference > twentyMinutesInMs) {
                console.log(`Tracker time delay detected: Latest: ${latestTime.toTimeString()}, Current: ${time.toTimeString()}, Difference: ${Math.round(timeDifference / 60000)} minutes`);
                return true; // Βρέθηκε απόκλιση μεγαλύτερη από 20 λεπτά
            }
        }

        return false; // Όλοι οι trackers είναι εντός του 20λεπτου ορίου
    }

    getTrackerStatusClass(stationId: string): string {
        const status = this.trackerStatusCache.get(stationId);

        switch (status) {
            case 'none':
                return 'comm-status-gray'; // Gray for no trackers
            case 'all-tracking':
                return 'comm-status-green'; // Green for all tracking
            case 'has-errors':
                return 'comm-status-red'; // Red for errors or non-tracking
            default:
                return 'comm-status-gray'; // Default gray while loading
        }
    }



    ngOnDestroy() {
        if (this.subscription) {
            this.subscription.unsubscribe();
        }
    }
}
