{"ast": null, "code": "import { debounceTime } from 'rxjs';\nimport { NavigationEnd } from '@angular/router';\nimport { filter } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/layout/service/app.layout.service\";\nimport * as i2 from \"../../service/stations.service\";\nimport * as i3 from \"../../service/providers.service\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"@angular/forms\";\nimport * as i6 from \"@angular/router\";\nimport * as i7 from \"../../service/cache.service\";\nimport * as i8 from \"../../service/date-utils.service\";\nimport * as i9 from \"../../service/tracker.service\";\nimport * as i10 from \"../../service/station-configuration.service\";\nimport * as i11 from \"@angular/common\";\nimport * as i12 from \"primeng/tooltip\";\nimport * as i13 from \"primeng/button\";\nimport * as i14 from \"primeng/inputtext\";\nimport * as i15 from \"primeng/dropdown\";\nimport * as i16 from \"@coreui/angular-chartjs\";\nimport * as i17 from \"primeng/badge\";\nimport * as i18 from \"@fortawesome/angular-fontawesome\";\nimport * as i19 from \"primeng/card\";\nimport * as i20 from \"primeng/tag\";\nimport * as i21 from \"primeng/ripple\";\nimport * as i22 from \"primeng/paginator\";\nfunction IndexComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8);\n    i0.ɵɵelement(3, \"fa-icon\", 9);\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5, \"Active Energy Systems\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-tag\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 13);\n    i0.ɵɵelement(10, \"fa-icon\", 14);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.stations.length);\n  }\n}\nfunction IndexComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 6)(1, \"div\", 7)(2, \"div\", 8);\n    i0.ɵɵelement(3, \"fa-icon\", 15);\n    i0.ɵɵelementStart(4, \"span\", 10);\n    i0.ɵɵtext(5, \"Combined Power Performance\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 11);\n    i0.ɵɵtext(7, \"33% (4.124kW)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(8, \"p-tag\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 17);\n    i0.ɵɵelement(10, \"fa-icon\", 18);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction IndexComponent_ng_template_9_p_badge_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-badge\", 27);\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"value\", ctx_r4.totalItems);\n  }\n}\nfunction IndexComponent_ng_template_9_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"div\", 20);\n    i0.ɵɵelement(2, \"fa-icon\", 21);\n    i0.ɵɵelementStart(3, \"h2\", 22);\n    i0.ɵɵtext(4, \"Energy Stations\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, IndexComponent_ng_template_9_p_badge_5_Template, 1, 1, \"p-badge\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 24)(7, \"p-button\", 25);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_9_Template_p_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.clearFilters());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p-button\", 26);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_9_Template_p_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.refreshData());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.totalItems > 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"loading\", ctx_r2.isRefreshing);\n  }\n}\nfunction IndexComponent_ng_template_10_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 37);\n    i0.ɵɵelement(2, \"fa-icon\", 38);\n    i0.ɵɵelementStart(3, \"p\", 39);\n    i0.ɵɵtext(4, \"No stations found matching your criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p-button\", 40);\n    i0.ɵɵlistener(\"click\", function IndexComponent_ng_template_10_div_8_Template_p_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r11.clearFilters());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_2_ng_container_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"c-chart\", 76);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const chartData_r18 = ctx.ngIf;\n    const station_r13 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r15 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"data\", chartData_r18)(\"options\", ctx_r15.getChartOptionsForStation(station_r13));\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_ng_template_2_ng_template_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\");\n    i0.ɵɵelement(2, \"fa-icon\", 78);\n    i0.ɵɵelementStart(3, \"p\", 79);\n    i0.ɵɵtext(4, \"No chart data\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nconst _c0 = a1 => [\"/app/station\", a1];\nfunction IndexComponent_ng_template_10_div_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 42)(1, \"div\", 43)(2, \"div\", 44)(3, \"h3\", 45)(4, \"a\", 46);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 47)(7, \"div\", 48);\n    i0.ɵɵelement(8, \"fa-icon\", 49);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 50)(11, \"div\", 51)(12, \"div\", 52)(13, \"div\", 53)(14, \"div\", 54);\n    i0.ɵɵelement(15, \"div\", 55);\n    i0.ɵɵelementStart(16, \"span\", 56);\n    i0.ɵɵtext(17, \"Communication\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(18, \"div\", 57)(19, \"div\", 58);\n    i0.ɵɵtext(20, \"Inverters\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 59);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 60)(24, \"div\", 61);\n    i0.ɵɵelement(25, \"div\", 55);\n    i0.ɵɵelementStart(26, \"span\", 62);\n    i0.ɵɵtext(27, \"Trackers\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 59);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 52)(31, \"div\", 53)(32, \"div\", 54);\n    i0.ɵɵelement(33, \"i\", 63);\n    i0.ɵɵelementStart(34, \"span\", 59);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(36, \"div\", 64)(37, \"div\", 65);\n    i0.ɵɵelement(38, \"i\", 66);\n    i0.ɵɵtext(39);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(40, \"div\", 61);\n    i0.ɵɵelement(41, \"fa-icon\", 67);\n    i0.ɵɵelementStart(42, \"span\");\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(44, \"div\", 68);\n    i0.ɵɵtemplate(45, IndexComponent_ng_template_10_div_9_ng_template_2_ng_container_45_Template, 2, 2, \"ng-container\", 69)(46, IndexComponent_ng_template_10_div_9_ng_template_2_ng_template_46_Template, 5, 0, \"ng-template\", null, 70, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 71)(49, \"div\", 53)(50, \"div\", 58);\n    i0.ɵɵtext(51, \"Portfolio\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(52, \"div\", 59);\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 60)(55, \"div\", 58);\n    i0.ɵɵtext(56, \"Owner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"div\", 59);\n    i0.ɵɵtext(58);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(59, \"div\", 71)(60, \"div\", 53)(61, \"div\", 58);\n    i0.ɵɵtext(62, \"Country\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(63, \"div\", 59);\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(65, \"div\", 60)(66, \"div\", 58);\n    i0.ɵɵtext(67, \"Prefecture\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"div\", 59);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(70, \"div\", 72)(71, \"div\", 73)(72, \"div\", 74);\n    i0.ɵɵelement(73, \"i\", 75);\n    i0.ɵɵtext(74);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const _r17 = i0.ɵɵreference(47);\n    const station_r13 = i0.ɵɵnextContext().$implicit;\n    const ctx_r14 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"routerLink\", i0.ɵɵpureFunction1(19, _c0, station_r13.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.getDisplayName(station_r13.id, station_r13.name), \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.formatNumber(ctx_r14.getInstalledCapacity(station_r13.id) || ctx_r14.getStationsSumData(station_r13.id) || 0, 1), \"kW \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getCommunicationStatusClass(station_r13.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getCommunicationStatusClass(station_r13.id));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(station_r13.invertersCount || 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getTrackerStatusClass(station_r13.id));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", ctx_r14.getTrackerStatusClass(station_r13.id));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r14.getTrackerCount(station_r13.id));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", station_r13.temperature || 0, \"\\u00B0C\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.formatNumber(ctx_r14.getCurrentPower(station_r13.id), 1), \"kW \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r14.getStationLastUpdate(station_r13.id));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r14.getStationsRealTimeData(station_r13))(\"ngIfElse\", _r17);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r14.getPortfolio(station_r13.id));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r14.getOwner(station_r13.id));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r14.getCountry(station_r13.id));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r14.getPrefecture(station_r13.id));\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r14.getDataProvider(station_r13.id, station_r13.provider || \"Aurora\"), \" \");\n  }\n}\nfunction IndexComponent_ng_template_10_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"p-card\", 41);\n    i0.ɵɵtemplate(2, IndexComponent_ng_template_10_div_9_ng_template_2_Template, 75, 21, \"ng-template\", 3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const station_r13 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", \"station-status-\" + (station_r13.status == null ? null : station_r13.status.toLowerCase()));\n  }\n}\nfunction IndexComponent_ng_template_10_div_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 80)(1, \"p-paginator\", 81);\n    i0.ɵɵlistener(\"onPageChange\", function IndexComponent_ng_template_10_div_10_Template_p_paginator_onPageChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r21 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r21.onPageChange($event));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"rows\", ctx_r10.itemsPerPage)(\"totalRecords\", ctx_r10.totalItems)(\"first\", ctx_r10.currentPage * ctx_r10.itemsPerPage)(\"rowsPerPageOptions\", ctx_r10.paginationOptions)(\"showCurrentPageReport\", true);\n  }\n}\nfunction IndexComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 24)(2, \"p-dropdown\", 29);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r23 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r23.selectedProvider = $event);\n    })(\"onChange\", function IndexComponent_ng_template_10_Template_p_dropdown_onChange_2_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r25 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r25.onProviderChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p-dropdown\", 30);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r26.selectedStatus = $event);\n    })(\"onChange\", function IndexComponent_ng_template_10_Template_p_dropdown_onChange_3_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r27 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r27.onStatusChange($event));\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"span\", 31);\n    i0.ɵɵelement(5, \"i\", 32);\n    i0.ɵɵelementStart(6, \"input\", 33);\n    i0.ɵɵlistener(\"ngModelChange\", function IndexComponent_ng_template_10_Template_input_ngModelChange_6_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r28 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r28.searchTerm = $event);\n    })(\"input\", function IndexComponent_ng_template_10_Template_input_input_6_listener($event) {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r29 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r29.onSearchChange($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 0);\n    i0.ɵɵtemplate(8, IndexComponent_ng_template_10_div_8_Template, 6, 0, \"div\", 34)(9, IndexComponent_ng_template_10_div_9_Template, 3, 1, \"div\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(10, IndexComponent_ng_template_10_div_10_Template, 2, 5, \"div\", 36);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r3.sortOptionsCountry)(\"ngModel\", ctx_r3.selectedProvider)(\"showClear\", true);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"options\", ctx_r3.sortOptionsStatus)(\"ngModel\", ctx_r3.selectedStatus)(\"showClear\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModel\", ctx_r3.searchTerm);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.paginatedStations.length === 0 && !ctx_r3.isRefreshing);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.paginatedStations)(\"ngForTrackBy\", ctx_r3.trackByStationId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.totalItems > ctx_r3.itemsPerPage);\n  }\n}\nexport class IndexComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, router, cacheService, dateUtils, trackerService, stationConfigService, cdr) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.router = router;\n    this.cacheService = cacheService;\n    this.dateUtils = dateUtils;\n    this.trackerService = trackerService;\n    this.stationConfigService = stationConfigService;\n    this.cdr = cdr;\n    this.stations = [];\n    this.filteredStations = [];\n    this.paginatedStations = [];\n    this.sortOptionsCountry = [];\n    this.sortOptionsStatus = [];\n    this.sortOrder = 0;\n    this.sortField = '';\n    // Filtering properties\n    this.searchTerm = '';\n    this.selectedProvider = '';\n    this.selectedStatus = '';\n    // Pagination properties\n    this.currentPage = 0;\n    this.itemsPerPage = 20;\n    this.totalItems = 0;\n    // Pagination options - cached to avoid infinite loop\n    this.paginationOptions = [20, 40, 80, 100];\n    // Loading state\n    this.isRefreshing = false;\n    this.sourceCities = [];\n    this.targetCities = [];\n    this.orderCities = [];\n    this.stationsData = new Map();\n    this.stationsRawData = new Map();\n    this.stationsSumData = new Map();\n    // Cache για τα computed values\n    this.currentPowerCache = new Map();\n    this.lastUpdateCache = new Map();\n    this.invertersCache = new Map();\n    this.deviceConfigurationsCache = new Map();\n    // Tracker related caches\n    this.trackersCache = new Map();\n    this.trackerStatusCache = new Map();\n    // Station configurations cache\n    this.stationConfigurations = new Map();\n    this.subscription = this.layoutService.configUpdate$.pipe(debounceTime(25)).subscribe(config => {\n      //   this.initChart();\n    });\n    this.initializeChartOptions();\n  }\n  ngOnInit() {\n    this.getUserProviders();\n    this.initializeConfigurations();\n  }\n  initializeConfigurations() {\n    // Subscribe to router events to refresh configurations when navigating back\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      if (event.url === '/app' || event.url === '/app/') {\n        console.log('Navigated back to index, refreshing configurations');\n        this.loadStationConfigurations();\n      }\n    });\n    // Load station configurations initially\n    this.loadStationConfigurations();\n  }\n  loadStationConfigurations() {\n    console.log('Loading station configurations...');\n    this.stationConfigService.getAllConfigurations().subscribe({\n      next: configurations => {\n        console.log('Configurations loaded:', configurations.length);\n        // Update local cache\n        this.stationConfigurations.clear();\n        configurations.forEach(config => {\n          this.stationConfigurations.set(config.stationId, config);\n        });\n        // Force change detection when configurations update\n        this.cdr.detectChanges();\n      },\n      error: error => {\n        console.error('Error loading configurations:', error);\n      }\n    });\n  }\n  initializeChartOptions() {\n    this.barOptions = {\n      maintainAspectRatio: false,\n      plugins: {\n        legend: {\n          display: false\n        }\n      },\n      scales: {\n        x: {\n          display: false\n        },\n        y: {\n          display: false\n        }\n      }\n    };\n    this.lineOptions = {\n      responsive: true,\n      maintainAspectRatio: false,\n      interaction: {\n        intersect: false,\n        mode: 'index'\n      },\n      elements: {\n        line: {\n          tension: 0.4,\n          borderWidth: 2\n        },\n        point: {\n          radius: 0,\n          hoverRadius: 4,\n          hitRadius: 10\n        }\n      },\n      plugins: {\n        legend: {\n          display: false\n        },\n        tooltip: {\n          enabled: true,\n          mode: 'index',\n          intersect: false,\n          position: 'nearest',\n          external: null,\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          titleColor: '#ffffff',\n          bodyColor: '#ffffff',\n          borderColor: 'rgba(255, 255, 255, 0.1)',\n          borderWidth: 1,\n          cornerRadius: 8,\n          displayColors: false,\n          padding: 12,\n          caretPadding: 6,\n          caretSize: 5,\n          titleFont: {\n            size: 14,\n            weight: 'bold'\n          },\n          bodyFont: {\n            size: 13\n          },\n          filter: function (tooltipItem) {\n            // Εμφανίζουμε tooltip μόνο αν υπάρχουν δεδομένα\n            return tooltipItem.parsed.y !== null && tooltipItem.parsed.y !== undefined;\n          },\n          callbacks: {\n            title: function (context) {\n              //console.log(\"Tooltip title callback called!\", context);\n              if (context && context.length > 0) {\n                const xValue = context[0].parsed.x;\n                //console.log(\"xValue: \" + xValue);\n                if (xValue !== undefined && xValue !== null) {\n                  // Απλή μετατροπή: decimal hours σε HH:MM\n                  const hours = Math.floor(xValue);\n                  const minutes = Math.round((xValue - hours) * 60);\n                  const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\n                  return `Time: ${formattedTime}`;\n                }\n              }\n              return 'Time: --:--';\n            },\n            label: function (context) {\n              const label = context.dataset.label || '';\n              const value = context.parsed.y;\n              return `${label}: ${value.toFixed(2).replace('.', ',')} kW`;\n            }\n          }\n        }\n      },\n      scales: {\n        x: {\n          type: 'linear',\n          display: true,\n          position: 'bottom',\n          min: 0,\n          max: 24,\n          ticks: {\n            stepSize: 12,\n            callback: function (value) {\n              // Static values: 00:00, 12:00, 24:00\n              if (value === 0) return '00:00';\n              if (value === 12) return '12:00';\n              if (value === 24) return '24:00';\n              return '';\n            },\n            color: '#374151',\n            font: {\n              size: 10,\n              weight: 'bold'\n            },\n            padding: 4\n          },\n          grid: {\n            display: false\n          },\n          border: {\n            display: true,\n            color: '#374151',\n            width: 2\n          }\n        },\n        y: {\n          display: true,\n          position: 'left',\n          title: {\n            display: true,\n            text: 'kW',\n            color: '#374151',\n            font: {\n              size: 12,\n              weight: 'bold'\n            }\n          },\n          ticks: {\n            maxTicksLimit: 3,\n            callback: function (value) {\n              // Will be dynamically set per station\n              return value.toFixed(0);\n            },\n            color: '#374151',\n            font: {\n              size: 10,\n              weight: 'bold'\n            },\n            padding: 4,\n            maxRotation: 0,\n            minRotation: 0\n          },\n          grid: {\n            display: false\n          },\n          border: {\n            display: true,\n            color: '#374151',\n            width: 2\n          }\n        }\n      },\n      animation: {\n        duration: 750,\n        easing: 'easeInOutQuart'\n      },\n      layout: {\n        padding: {\n          top: 20,\n          bottom: 5,\n          left: 5,\n          right: 5\n        }\n      }\n    };\n  }\n  getUserProviders() {\n    this.isRefreshing = true;\n    this.providersService.getUserProviders().then(providersData => {\n      if (providersData.length > 0) {\n        this.stationsService.getUserStations().then(stationsData => {\n          this.cacheService.setStations(stationsData);\n          this.stations = stationsData;\n          console.log(\"stations set\");\n          console.log(stationsData);\n          // Initialize filter options\n          this.initializeFilterOptions();\n          // Apply filters and pagination\n          this.applyFilters();\n          // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n          this.loadAllStationsData();\n          this.isRefreshing = false;\n        });\n      } else {\n        this.router.navigate(['/app/providers']);\n      }\n    }).catch(error => {\n      console.error('Error loading providers:', error);\n      this.isRefreshing = false;\n    });\n  }\n  // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών\n  loadAllStationsData() {\n    if (!this.stations || this.stations.length === 0) return;\n    // Φορτώνουμε τα δεδομένα για κάθε σταθμό\n    this.stations.forEach(station => {\n      this.loadStationData(station);\n    });\n  }\n  // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού\n  loadStationData(station) {\n    if (!station) return;\n    const now = new Date();\n    const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\n    const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\n    let request = {\n      devIds: station.deviceIds,\n      devTypeId: 1,\n      startDateTime: formattedStartDate,\n      endDateTime: formattedEndDate,\n      separated: true,\n      searchType: null,\n      stationId: station.id\n    };\n    this.stationsService.getStationHistoricData(request).then(data => {\n      if (data && data.data && data.data.length > 0) {\n        // Filter data to not show beyond current time\n        const filteredData = this.filterDataByCurrentTime(data.data);\n        const documentStyle = getComputedStyle(document.documentElement);\n        // Βρίσκουμε τις μοναδικές ημερομηνίες με σειρά\n        const uniqueDates = Array.from(new Set(data.data.map(d => d.dateTime)));\n        const uniqueDateDescriptions = Array.from(new Set(data.data.map(d => d.dateDescription)));\n        // Βρίσκουμε τους μοναδικούς inverters\n        const uniqueInverters = Array.from(new Set(data.data.map(d => d.name)));\n        var datasets = [];\n        // const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\n        const activePowerByName = {};\n        data.data.forEach(d => {\n          if (!activePowerByName[d.name]) {\n            activePowerByName[d.name] = [];\n          }\n          activePowerByName[d.name].push(d.activePower);\n        });\n        uniqueInverters.forEach(inv => {\n          var color = this.getRandomColor();\n          datasets.push({\n            label: inv,\n            data: activePowerByName[inv],\n            fill: false,\n            backgroundColor: color,\n            borderColor: color,\n            tension: .4\n          });\n        });\n        const firstName = data.data[0].name; // Παίρνουμε το πρώτο name\n        const dateTimesForFirstName = data.data.filter(d => d.name === firstName) // Φιλτράρουμε μόνο τα αντικείμενα με το ίδιο name\n        .map(d => d.dateTime); // Παίρνουμε μόνο τα dateTime\n        // Μετατρέπουμε τα δεδομένα για στατικό άξονα X (0-24 ώρες) και κρατάμε time mapping\n        const convertedDatasets = this.convertDataForStaticXAxis(datasets, dateTimesForFirstName);\n        const lineData = {\n          labels: [],\n          datasets: convertedDatasets\n        };\n        // const lineData = {\n        //     labels: filteredData.map((e, index) =>\n        //         index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''\n        //     ),\n        //     datasets: [\n        //         {\n        //             label: 'Active Power',\n        //             data: filteredData.map(e => e.activePower),\n        //             fill: false,\n        //             backgroundColor: documentStyle.getPropertyValue('--primary-500'),\n        //             borderColor: documentStyle.getPropertyValue('--primary-500'),\n        //             tension: .4\n        //         },\n        //         {\n        //             label: 'Total Input Power',\n        //             data: filteredData.map(e => e.totalInputPower),\n        //             fill: false,\n        //             backgroundColor: documentStyle.getPropertyValue('--primary-200'),\n        //             borderColor: documentStyle.getPropertyValue('--primary-200'),\n        //             tension: .4\n        //         }\n        //     ]\n        // };\n        // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού\n        this.stationsData.set(station.id, lineData);\n        this.stationsRawData.set(station.id, filteredData);\n        this.stationsSumData.set(station.id, data.sum);\n        // Set fallback inverters count from raw data if device configs not loaded yet\n        if (!station.invertersCount) {\n          station.invertersCount = uniqueInverters.length;\n        }\n        // Καθαρίζουμε το cache για αυτό το station\n        this.clearStationCache(station.id);\n      } else {\n        // Δεν υπάρχουν δεδομένα για αυτόν τον σταθμό\n        console.warn(`No data available for station ${station.name} (${station.id})`);\n        // Αποθηκεύουμε κενά δεδομένα για να μην προσπαθήσουμε ξανά\n        this.stationsData.set(station.id, {\n          labels: [],\n          datasets: []\n        });\n        this.stationsRawData.set(station.id, []);\n        this.stationsSumData.set(station.id, null);\n      }\n    }).catch(error => {\n      console.error(`Error loading data for station ${station.name} (${station.id}):`, error);\n      // Αποθηκεύουμε κενά δεδομένα σε περίπτωση error\n      this.stationsData.set(station.id, {\n        labels: [],\n        datasets: []\n      });\n      this.stationsRawData.set(station.id, []);\n      this.stationsSumData.set(station.id, null);\n    });\n    // Φορτώνουμε tracker data για αυτόν τον σταθμό\n    this.loadStationTrackers(station.id);\n    // Φορτώνουμε device configurations για σωστό count\n    this.loadStationDeviceConfigurations(station.id);\n  }\n  getRandomColor() {\n    // Use variations of the logo blue color (#191C53)\n    const logoBlueVariations = ['#191C53', '#2a2f7a', '#0f1240', '#3d4299', '#1a1e5a', '#252a66' // Medium variation\n    ];\n\n    return logoBlueVariations[Math.floor(Math.random() * logoBlueVariations.length)];\n  }\n  // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα\n  getStationsRealTimeData(station) {\n    // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν\n    if (station && station.id && this.stationsData.has(station.id)) {\n      return this.stationsData.get(station.id);\n    }\n    // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null\n    return null;\n  }\n  // Load device configurations for accurate device count\n  loadStationDeviceConfigurations(stationId) {\n    if (!stationId) return;\n    this.stationConfigService.getDevices(stationId).subscribe({\n      next: devices => {\n        this.deviceConfigurationsCache.set(stationId, devices);\n        // Update station object with pre-calculated inverters count\n        const station = this.stations.find(s => s.id === stationId);\n        if (station) {\n          station.invertersCount = devices.length;\n          console.log(`Station ${stationId}: Set invertersCount to ${devices.length} from device configs`);\n        }\n        // Clear inverters cache to force recalculation\n        this.invertersCache.delete(stationId);\n      },\n      error: error => {\n        console.warn(`Could not load device configurations for station ${stationId}:`, error);\n        // Set empty array so we don't keep trying\n        this.deviceConfigurationsCache.set(stationId, []);\n        // Set fallback count using raw data\n        const station = this.stations.find(s => s.id === stationId);\n        if (station) {\n          const data = this.stationsRawData.get(stationId);\n          station.invertersCount = data && data.length > 0 ? new Set(data.map(item => item.name)).size : 0;\n        }\n      }\n    });\n  }\n  // Clear cache για ένα συγκεκριμένο station\n  clearStationCache(stationId) {\n    // Δεν χρειάζεται να διαγράψουμε chart options cache αφού δεν το χρησιμοποιούμε πια\n    this.currentPowerCache.delete(stationId);\n    this.lastUpdateCache.delete(stationId);\n    this.invertersCache.delete(stationId);\n    this.trackersCache.delete(stationId);\n    this.trackerStatusCache.delete(stationId);\n    this.deviceConfigurationsCache.delete(stationId);\n  }\n  getStationsSumData(stationId) {\n    return this.stationsSumData.get(stationId);\n  }\n  getStationsInverters(stationId) {\n    // Χρησιμοποιούμε cache\n    if (this.invertersCache.has(stationId)) {\n      console.log(\"Cache:\" + stationId + \" \" + this.invertersCache.get(stationId));\n      return this.invertersCache.get(stationId);\n    }\n    // Προτεραιότητα στα device configurations (πιο ακριβή)\n    const deviceConfigs = this.deviceConfigurationsCache.get(stationId);\n    let result = 0;\n    if (deviceConfigs && deviceConfigs.length > 0) {\n      console.log(\"Configs:\" + stationId + \" \" + deviceConfigs.length);\n      // Μετράμε τα device configurations\n      result = deviceConfigs.length;\n    } else {\n      // Fallback στα raw data από stations API\n      const data = this.stationsRawData.get(stationId);\n      if (data && data.length > 0) {\n        result = new Set(data.map(item => item.name)).size;\n      }\n    }\n    // Αποθηκεύουμε στο cache\n    this.invertersCache.set(stationId, result);\n    return result;\n  }\n  getStationLastUpdate(stationId) {\n    // Χρησιμοποιούμε cache\n    if (this.lastUpdateCache.has(stationId)) {\n      return this.lastUpdateCache.get(stationId);\n    }\n    const data = this.stationsRawData.get(stationId);\n    if (!data || data.length === 0) {\n      this.lastUpdateCache.set(stationId, \"-\");\n      return \"-\";\n    }\n    const latest = data.reduce((latestSoFar, current) => {\n      return new Date(current.dateTime).getTime() > new Date(latestSoFar.dateTime).getTime() ? current : latestSoFar;\n    });\n    const result = new Date(latest.dateTime).toLocaleString(\"en-GB\", {\n      hour: '2-digit',\n      minute: '2-digit',\n      hour12: false,\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      timeZone: 'Europe/Athens'\n    });\n    // Αποθηκεύουμε στο cache\n    this.lastUpdateCache.set(stationId, result);\n    return result;\n  }\n  getCurrentPower(stationId) {\n    // Χρησιμοποιούμε cache\n    if (this.currentPowerCache.has(stationId)) {\n      return this.currentPowerCache.get(stationId);\n    }\n    const data = this.stationsRawData.get(stationId);\n    if (!data || data.length === 0) {\n      this.currentPowerCache.set(stationId, 0);\n      return 0;\n    }\n    // Find the latest timestamp\n    const latestTimestamp = Math.max(...data.map(item => new Date(item.dateTime).getTime()));\n    // Get all entries with the latest timestamp and sum their activePower\n    const latestEntries = data.filter(item => new Date(item.dateTime).getTime() === latestTimestamp);\n    const result = latestEntries.reduce((sum, item) => sum + (item.activePower || 0), 0);\n    // Αποθηκεύουμε στο cache\n    this.currentPowerCache.set(stationId, result);\n    return result;\n  }\n  // Get chart options with dynamic axis values for each station\n  getChartOptionsForStation(station) {\n    // Δεν χρησιμοποιούμε cache για chart options γιατί τα δεδομένα αλλάζουν\n    // Shallow copy των options και manual copy των callbacks (JSON.parse χάνει τις functions)\n    const baseOptions = {\n      ...this.lineOptions,\n      plugins: {\n        ...this.lineOptions.plugins,\n        tooltip: {\n          ...this.lineOptions.plugins.tooltip,\n          callbacks: {\n            ...this.lineOptions.plugins.tooltip.callbacks\n          }\n        }\n      },\n      scales: {\n        ...this.lineOptions.scales,\n        x: {\n          ...this.lineOptions.scales.x\n        },\n        y: {\n          ...this.lineOptions.scales.y\n        }\n      }\n    };\n    // Get station data for X-axis calculation\n    const stationData = this.stationsData.get(station.id);\n    const rawData = this.stationsRawData.get(station.id);\n    // Calculate Y-axis values με καλύτερη κλιμάκωση\n    let maxDataValue = 0;\n    // Get max value from station data\n    if (rawData && rawData.length > 0) {\n      maxDataValue = Math.max(...rawData.map(item => item.activePower || 0));\n    }\n    // Αν δεν υπάρχουν δεδομένα, χρησιμοποιούμε το calculated power\n    if (maxDataValue === 0) {\n      const calculatedPower = this.getStationsSumData(station.id);\n      if (calculatedPower && calculatedPower > 0) {\n        maxDataValue = calculatedPower;\n      }\n    }\n    // Προσθέτουμε 10% περιθώριο στο ταβάνι (λιγότερο από πριν)\n    let maxValue = maxDataValue * 1.1;\n    // Πιο φυσική στρογγυλοποίηση - κρατάμε πιο κοντά στα πραγματικά δεδομένα\n    if (maxValue <= 1) {\n      maxValue = Math.ceil(maxValue * 10) / 10; // 0.1, 0.2, 0.3, etc.\n    } else if (maxValue <= 5) {\n      maxValue = Math.ceil(maxValue * 2) / 2; // 0.5 increments: 1.5, 2.0, 2.5, etc.\n    } else if (maxValue <= 20) {\n      maxValue = Math.ceil(maxValue); // 1 unit increments: 6, 7, 8, 9, 10, etc.\n    } else if (maxValue <= 100) {\n      maxValue = Math.ceil(maxValue / 2) * 2; // 2 unit increments: 22, 24, 26, etc.\n    } else if (maxValue <= 500) {\n      maxValue = Math.ceil(maxValue / 5) * 5; // 5 unit increments: 105, 110, 115, etc.\n    } else {\n      maxValue = Math.ceil(maxValue / 10) * 10; // 10 unit increments για μεγάλες τιμές\n    }\n    // Εξασφαλίζουμε ότι έχουμε τουλάχιστον κάποια τιμή\n    if (maxValue === 0 || maxValue < 0.1) {\n      maxValue = 1; // Minimum 1 kW για καλύτερη εμφάνιση\n    }\n    // Debug logging (προσωρινό)\n    //console.log(`Station ${station.id}: maxDataValue=${maxDataValue}, maxValue=${maxValue}`);\n    // Update Y-axis configuration - ακριβώς 3 τιμές\n    const midValue = maxValue / 2;\n    baseOptions.scales.y.min = 0;\n    baseOptions.scales.y.max = maxValue;\n    // Καθορίζουμε ακριβώς τις τιμές που θέλουμε\n    baseOptions.scales.y.ticks = {\n      stepSize: midValue,\n      maxTicksLimit: 3,\n      callback: function (value) {\n        // Εμφανίζουμε μόνο τις 3 συγκεκριμένες τιμές\n        if (value === 0) return '0';\n        if (value === midValue) return Math.round(midValue).toString();\n        if (value === maxValue) return Math.round(maxValue).toString();\n        return ''; // Κρύβουμε όλες τις άλλες τιμές\n      },\n\n      color: '#374151',\n      font: {\n        size: 12,\n        weight: 'bold'\n      }\n    };\n    // X-axis είναι τώρα στατικός με linear scale - δεν χρειάζεται override\n    // Debug logging\n    // console.log(\"Chart options for station\", station.id, baseOptions);\n    // console.log(\"Tooltip config:\", baseOptions.plugins?.tooltip);\n    // Επιστρέφουμε τα options χωρίς caching\n    return baseOptions;\n  }\n  // Μετατροπή δεδομένων για στατικό άξονα X (0-24 ώρες)\n  convertDataForStaticXAxis(datasets, dateTimes) {\n    return datasets.map(dataset => {\n      const convertedData = [];\n      dataset.data.forEach((value, index) => {\n        if (index < dateTimes.length) {\n          const dateTime = new Date(dateTimes[index]);\n          const hours = dateTime.getHours() + dateTime.getMinutes() / 60;\n          convertedData.push({\n            x: hours,\n            y: value\n          });\n        }\n      });\n      return {\n        ...dataset,\n        data: convertedData\n      };\n    });\n  }\n  // Helper μέθοδος για μορφοποίηση αριθμών με κόμμα\n  formatNumber(value, decimals = 2) {\n    return value.toFixed(decimals).replace('.', ',');\n  }\n  // Configuration helper methods with error handling\n  getDisplayName(stationId, originalName) {\n    try {\n      if (!stationId) return originalName || 'N/A';\n      const config = this.stationConfigurations.get(stationId);\n      return config?.customName || config?.name || originalName || 'N/A';\n    } catch (error) {\n      console.warn(`Error getting display name for station ${stationId}:`, error);\n      return originalName || 'N/A';\n    }\n  }\n  getOwner(stationId) {\n    try {\n      if (!stationId) return 'N/A';\n      const config = this.stationConfigurations.get(stationId);\n      return config?.owner || 'N/A';\n    } catch (error) {\n      console.warn(`Error getting owner for station ${stationId}:`, error);\n      return 'N/A';\n    }\n  }\n  getPortfolio(stationId) {\n    try {\n      if (!stationId) return 'N/A';\n      const config = this.stationConfigurations.get(stationId);\n      return config?.portfolio || 'N/A';\n    } catch (error) {\n      console.warn(`Error getting portfolio for station ${stationId}:`, error);\n      return 'N/A';\n    }\n  }\n  getCountry(stationId) {\n    try {\n      if (!stationId) return 'N/A';\n      const config = this.stationConfigurations.get(stationId);\n      return config?.country || 'N/A';\n    } catch (error) {\n      console.warn(`Error getting country for station ${stationId}:`, error);\n      return 'N/A';\n    }\n  }\n  getPrefecture(stationId) {\n    try {\n      if (!stationId) return 'N/A';\n      const config = this.stationConfigurations.get(stationId);\n      return config?.prefecture || 'N/A';\n    } catch (error) {\n      console.warn(`Error getting prefecture for station ${stationId}:`, error);\n      return 'N/A';\n    }\n  }\n  getInstalledCapacity(stationId) {\n    try {\n      if (!stationId) return 0;\n      const config = this.stationConfigurations.get(stationId);\n      return config?.installedCapacityKW || 0;\n    } catch (error) {\n      console.warn(`Error getting installed capacity for station ${stationId}:`, error);\n      return 0;\n    }\n  }\n  getDataProvider(stationId, originalProvider) {\n    try {\n      if (!stationId) return originalProvider || 'N/A';\n      const config = this.stationConfigurations.get(stationId);\n      return config?.dataProvider || originalProvider || 'N/A';\n    } catch (error) {\n      console.warn(`Error getting data provider for station ${stationId}:`, error);\n      return originalProvider || 'N/A';\n    }\n  }\n  onSortChange(event) {\n    const value = event.value;\n    if (value.indexOf('!') === 0) {\n      this.sortOrder = -1;\n      this.sortField = value.substring(1, value.length);\n    } else {\n      this.sortOrder = 1;\n      this.sortField = value;\n    }\n  }\n  onFilter(dv, event) {\n    dv.filter(event.target.value);\n  }\n  // Initialize filter options based on available stations\n  initializeFilterOptions() {\n    // Get unique providers\n    const providers = [...new Set(this.stations.map(station => station.provider))];\n    this.sortOptionsCountry = providers.map(provider => ({\n      label: provider,\n      value: provider\n    }));\n    // Get unique statuses\n    const statuses = [...new Set(this.stations.map(station => station.status))];\n    this.sortOptionsStatus = statuses.map(status => ({\n      label: status,\n      value: status\n    }));\n  }\n  // Apply all filters and update pagination\n  applyFilters() {\n    let filtered = [...this.stations];\n    // Apply search filter\n    if (this.searchTerm) {\n      const searchLower = this.searchTerm.toLowerCase();\n      filtered = filtered.filter(station => station.name.toLowerCase().includes(searchLower) || station.provider.toLowerCase().includes(searchLower) || station.location && station.location.toLowerCase().includes(searchLower));\n    }\n    // Apply provider filter\n    if (this.selectedProvider) {\n      filtered = filtered.filter(station => station.provider === this.selectedProvider);\n    }\n    // Apply status filter\n    if (this.selectedStatus) {\n      filtered = filtered.filter(station => station.status === this.selectedStatus);\n    }\n    // Sort stations alphabetically by name\n    filtered = filtered.sort((a, b) => {\n      const nameA = a.name?.toLowerCase() || '';\n      const nameB = b.name?.toLowerCase() || '';\n      return nameA.localeCompare(nameB);\n    });\n    this.filteredStations = filtered;\n    this.totalItems = filtered.length;\n    this.currentPage = 0; // Reset to first page\n    // Update pagination options when totalItems changes\n    this.updatePaginationOptions();\n    this.updatePagination();\n  }\n  // Update pagination options when totalItems changes\n  updatePaginationOptions() {\n    this.paginationOptions = [20, 40, 80, 100];\n    if (this.totalItems > 100) {\n      this.paginationOptions.push(this.totalItems);\n    }\n  }\n  // Update pagination based on current page\n  updatePagination() {\n    const startIndex = this.currentPage * this.itemsPerPage;\n    const endIndex = startIndex + this.itemsPerPage;\n    this.paginatedStations = this.filteredStations.slice(startIndex, endIndex);\n  }\n  // Handle search input change\n  onSearchChange(event) {\n    this.searchTerm = event.target.value;\n    this.applyFilters();\n  }\n  // Handle provider filter change\n  onProviderChange(event) {\n    this.selectedProvider = event.value || '';\n    this.applyFilters();\n  }\n  // Handle status filter change\n  onStatusChange(event) {\n    this.selectedStatus = event.value || '';\n    this.applyFilters();\n  }\n  // Handle pagination change\n  onPageChange(event) {\n    this.currentPage = event.page;\n    this.itemsPerPage = event.rows;\n    this.updatePagination();\n  }\n  // Refresh data\n  refreshData() {\n    // Καθαρίζουμε όλα τα caches\n    this.clearAllCaches();\n    this.getUserProviders();\n  }\n  // Clear όλα τα caches\n  clearAllCaches() {\n    // Δεν χρειάζεται να καθαρίσουμε chart options cache αφού δεν το χρησιμοποιούμε πια\n    this.currentPowerCache.clear();\n    this.lastUpdateCache.clear();\n    this.invertersCache.clear();\n    this.deviceConfigurationsCache.clear();\n  }\n  // Clear all filters\n  clearFilters() {\n    this.searchTerm = '';\n    this.selectedProvider = '';\n    this.selectedStatus = '';\n    this.applyFilters();\n  }\n  // Check if station has equipment data\n  hasEquipmentData(station) {\n    const inverters = this.getStationsInverters(station.id || '');\n    return inverters > 0 || station.mmpt && station.mmpt > 0 || station.string && station.string > 0 || station.pvn && station.pvn > 0;\n  }\n  filterDataByCurrentTime(data) {\n    const now = new Date();\n    const currentTime = now.getTime();\n    return data.filter(item => {\n      const itemDateTime = new Date(item.dateTime);\n      const itemTime = itemDateTime.getTime();\n      // Only include data points that are not in the future\n      return itemTime <= currentTime;\n    });\n  }\n  initMap() {\n    this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\";\n  }\n  // initChart() {\n  //     const documentStyle = getComputedStyle(document.documentElement);\n  //     const textColor = documentStyle.getPropertyValue('--text-color');\n  //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\n  //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\n  //     this.chartData = {\n  //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\n  //         datasets: [\n  //             {\n  //                 label: 'First Dataset',\n  //                 data: [65, 59, 80, 81, 56, 55, 40],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\n  //                 tension: .4\n  //             },\n  //             {\n  //                 label: 'Second Dataset',\n  //                 data: [28, 48, 40, 19, 86, 27, 90],\n  //                 fill: false,\n  //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\n  //                 borderColor: documentStyle.getPropertyValue('--green-600'),\n  //                 tension: .4\n  //             }\n  //         ]\n  //     };\n  //     this.chartOptions = {\n  //         plugins: {\n  //             legend: {\n  //                 labels: {\n  //                     color: textColor\n  //                 }\n  //             }\n  //         },\n  //         scales: {\n  //             x: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             },\n  //             y: {\n  //                 ticks: {\n  //                     color: textColorSecondary\n  //                 },\n  //                 grid: {\n  //                     color: surfaceBorder,\n  //                     drawBorder: false\n  //                 }\n  //             }\n  //         }\n  //     };\n  // }\n  // TrackBy function για βελτίωση απόδοσης του *ngFor\n  trackByStationId(index, station) {\n    return station.id || index.toString();\n  }\n  // Get communication status class for text and circle color\n  getCommunicationStatusClass(stationId) {\n    const now = new Date();\n    const currentHour = now.getHours();\n    // Γκρι σκούρο από 22:00-06:00\n    if (currentHour >= 22 || currentHour < 6) {\n      return 'comm-status-gray';\n    }\n    const lastUpdateStr = this.getStationLastUpdate(stationId);\n    if (lastUpdateStr === '-') {\n      return 'comm-status-red'; // Red if no data\n    }\n\n    try {\n      // Parse the last update time (format: \"DD/MM/YYYY, HH:mm\")\n      const [datePart, timePart] = lastUpdateStr.split(', ');\n      const [day, month, year] = datePart.split('/').map(Number);\n      const [hours, minutes] = timePart.split(':').map(Number);\n      const lastUpdate = new Date(year, month - 1, day, hours, minutes);\n      const hoursDiff = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60);\n      console.log(`Station ${stationId}: Last update: ${lastUpdateStr}, Hours diff: ${hoursDiff.toFixed(2)}`);\n      if (hoursDiff <= 1) {\n        return 'comm-status-green'; // Green - less than 1 hour\n      } else if (hoursDiff <= 2) {\n        return 'comm-status-orange'; // Orange - 1-2 hours\n      } else {\n        return 'comm-status-red'; // Red - more than 2 hours\n      }\n    } catch (error) {\n      console.error(`Error parsing time for station ${stationId}: ${lastUpdateStr}`, error);\n      return 'comm-status-red'; // Red if parsing fails\n    }\n  }\n  // Tracker methods\n  loadStationTrackers(stationId) {\n    if (!stationId) return;\n    this.trackerService.getTrackersByStation(stationId).subscribe({\n      next: trackers => {\n        this.trackersCache.set(stationId, trackers);\n        if (trackers && trackers.length > 0) {\n          // Load data for each tracker to check status\n          let completedRequests = 0;\n          let hasErrors = false;\n          trackers.forEach(tracker => {\n            this.trackerService.getTrackerData(tracker.id).subscribe({\n              next: data => {\n                tracker.trackingData = data.trackingData;\n                tracker.windSpeed = data.windSpeed;\n                tracker.hasError = data.hasError;\n                tracker.errorMessage = data.errorMessage;\n                // Check if this tracker has non-tracking status\n                const hasNonTrackingStatus = data.trackingData?.some(trackingData => trackingData.status && !trackingData.status.toLowerCase().includes('tracking'));\n                if (hasNonTrackingStatus || data.hasError) {\n                  hasErrors = true;\n                }\n                completedRequests++;\n                if (completedRequests === trackers.length) {\n                  // All requests completed, check time delays as well\n                  const hasTimeDelayIssues = this.checkNextTrackingTimeDelays(trackers);\n                  this.trackerStatusCache.set(stationId, hasErrors || hasTimeDelayIssues ? 'has-errors' : 'all-tracking');\n                }\n              },\n              error: error => {\n                console.error(`Error loading tracker data for station ${stationId}:`, error);\n                hasErrors = true;\n                completedRequests++;\n                if (completedRequests === trackers.length) {\n                  // Αν υπάρχουν errors, δεν χρειάζεται να ελέγξουμε χρονικές αποκλίσεις\n                  this.trackerStatusCache.set(stationId, 'has-errors');\n                }\n              }\n            });\n          });\n        } else {\n          // No trackers for this station\n          this.trackerStatusCache.set(stationId, 'none');\n        }\n      },\n      error: error => {\n        console.error(`Error loading trackers for station ${stationId}:`, error);\n        this.trackerStatusCache.set(stationId, 'none');\n      }\n    });\n  }\n  getTrackerCount(stationId) {\n    const trackers = this.trackersCache.get(stationId);\n    if (!trackers || trackers.length === 0) {\n      return '0';\n    }\n    // Μετράμε το συνολικό πλήθος των TrackingData από όλους τους trackers\n    let totalTrackingData = 0;\n    trackers.forEach(tracker => {\n      if (tracker.trackingData && tracker.trackingData.length > 0) {\n        totalTrackingData += tracker.trackingData.length;\n      }\n    });\n    return totalTrackingData.toString();\n  }\n  // Έλεγχος χρονικής απόκλισης στο Next Tracking\n  checkNextTrackingTimeDelays(trackers) {\n    if (!trackers || trackers.length === 0) {\n      return false;\n    }\n    // Συλλέγουμε όλα τα Next Tracking times από όλους τους trackers\n    const allNextTrackingTimes = [];\n    trackers.forEach(tracker => {\n      if (tracker.trackingData && tracker.trackingData.length > 0) {\n        tracker.trackingData.forEach(data => {\n          if (data.nextTracking) {\n            try {\n              // Μετατρέπουμε το nextTracking string σε Date\n              // Υποθέτουμε format HH:mm:ss (π.χ. \"11:15:00\")\n              const timeParts = data.nextTracking.split(':');\n              if (timeParts.length >= 2) {\n                const today = new Date();\n                const nextTrackingTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(), parseInt(timeParts[0]), parseInt(timeParts[1]), parseInt(timeParts[2] || '0'));\n                allNextTrackingTimes.push(nextTrackingTime);\n              }\n            } catch (error) {\n              console.error('Error parsing nextTracking time:', data.nextTracking, error);\n            }\n          }\n        });\n      }\n    });\n    if (allNextTrackingTimes.length === 0) {\n      return false; // Δεν υπάρχουν δεδομένα για έλεγχο\n    }\n    // Βρίσκουμε τον πιο προχωρημένο χρονικά tracker (μέγιστος χρόνος)\n    const latestTime = new Date(Math.max(...allNextTrackingTimes.map(time => time.getTime())));\n    // Ελέγχουμε αν κάποιος tracker έχει απόκλιση μεγαλύτερη από 20 λεπτά\n    const twentyMinutesInMs = 20 * 60 * 1000; // 20 λεπτά σε milliseconds\n    for (const time of allNextTrackingTimes) {\n      const timeDifference = latestTime.getTime() - time.getTime();\n      if (timeDifference > twentyMinutesInMs) {\n        console.log(`Tracker time delay detected: Latest: ${latestTime.toTimeString()}, Current: ${time.toTimeString()}, Difference: ${Math.round(timeDifference / 60000)} minutes`);\n        return true; // Βρέθηκε απόκλιση μεγαλύτερη από 20 λεπτά\n      }\n    }\n\n    return false; // Όλοι οι trackers είναι εντός του 20λεπτου ορίου\n  }\n\n  getTrackerStatusClass(stationId) {\n    const status = this.trackerStatusCache.get(stationId);\n    switch (status) {\n      case 'none':\n        return 'comm-status-gray';\n      // Gray for no trackers\n      case 'all-tracking':\n        return 'comm-status-green';\n      // Green for all tracking\n      case 'has-errors':\n        return 'comm-status-red';\n      // Red for errors or non-tracking\n      default:\n        return 'comm-status-gray';\n      // Default gray while loading\n    }\n  }\n\n  ngOnDestroy() {\n    if (this.subscription) {\n      this.subscription.unsubscribe();\n    }\n  }\n  static #_ = this.ɵfac = function IndexComponent_Factory(t) {\n    return new (t || IndexComponent)(i0.ɵɵdirectiveInject(i1.LayoutService), i0.ɵɵdirectiveInject(i2.StationsService), i0.ɵɵdirectiveInject(i3.ProvidersService), i0.ɵɵdirectiveInject(i4.MessageService), i0.ɵɵdirectiveInject(i5.FormBuilder), i0.ɵɵdirectiveInject(i6.Router), i0.ɵɵdirectiveInject(i7.CacheService), i0.ɵɵdirectiveInject(i8.DateUtilsService), i0.ɵɵdirectiveInject(i9.TrackerService), i0.ɵɵdirectiveInject(i10.StationConfigurationService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: IndexComponent,\n    selectors: [[\"ng-component\"]],\n    decls: 11,\n    vars: 0,\n    consts: [[1, \"grid\"], [1, \"col-12\", \"md:col-6\", \"lg:col-6\"], [1, \"h-full\"], [\"pTemplate\", \"content\"], [1, \"col-12\"], [\"pTemplate\", \"header\"], [1, \"flex\", \"justify-content-between\", \"align-items-center\"], [1, \"flex-1\"], [1, \"flex\", \"align-items-center\", \"gap-2\", \"mb-3\"], [\"icon\", \"solar-panel\", 1, \"text-primary\", \"text-lg\"], [1, \"text-600\", \"font-medium\"], [1, \"text-900\", \"font-bold\", \"text-3xl\"], [\"value\", \"Online\", \"severity\", \"success\", 1, \"mt-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-primary-50\", \"border-round-lg\", 2, \"width\", \"4rem\", \"height\", \"4rem\"], [\"icon\", \"solar-panel\", 1, \"text-primary\", \"text-2xl\"], [\"icon\", \"bolt\", 1, \"text-orange-500\", \"text-lg\"], [\"value\", \"Optimal\", \"severity\", \"warning\", 1, \"mt-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"bg-orange-50\", \"border-round-lg\", 2, \"width\", \"4rem\", \"height\", \"4rem\"], [\"icon\", \"bolt\", 1, \"text-orange-500\", \"text-2xl\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"p-3\"], [1, \"flex\", \"align-items-center\", \"gap-2\"], [\"icon\", \"list\", 1, \"text-primary\", \"text-xl\"], [1, \"text-2xl\", \"font-semibold\", \"m-0\"], [\"severity\", \"info\", 3, \"value\", 4, \"ngIf\"], [1, \"flex\", \"gap-2\"], [\"icon\", \"pi pi-filter-slash\", \"severity\", \"secondary\", \"size\", \"small\", \"pTooltip\", \"Clear all filters\", 3, \"click\"], [\"icon\", \"pi pi-refresh\", \"severity\", \"info\", \"size\", \"small\", \"pTooltip\", \"Refresh data\", 3, \"loading\", \"click\"], [\"severity\", \"info\", 3, \"value\"], [1, \"flex\", \"flex-column\", \"md:flex-row\", \"md:justify-content-between\", \"gap-3\", \"mb-4\"], [\"placeholder\", \"Filter by Provider\", 1, \"w-full\", \"md:w-auto\", 3, \"options\", \"ngModel\", \"showClear\", \"ngModelChange\", \"onChange\"], [\"placeholder\", \"Filter by Status\", 1, \"w-full\", \"md:w-auto\", 3, \"options\", \"ngModel\", \"showClear\", \"ngModelChange\", \"onChange\"], [1, \"p-input-icon-left\"], [1, \"pi\", \"pi-search\"], [\"type\", \"search\", \"pInputText\", \"\", \"placeholder\", \"Search stations...\", 1, \"w-full\", \"md:w-auto\", 3, \"ngModel\", \"ngModelChange\", \"input\"], [\"class\", \"col-12\", 4, \"ngIf\"], [\"class\", \"col-12\", 4, \"ngFor\", \"ngForOf\", \"ngForTrackBy\"], [\"class\", \"mt-4\", 4, \"ngIf\"], [1, \"text-center\", \"py-6\"], [\"icon\", \"inbox\", 1, \"text-6xl\", \"text-300\", \"mb-3\"], [1, \"text-lg\", \"text-600\"], [\"label\", \"Clear Filters\", \"icon\", \"pi pi-filter-slash\", \"severity\", \"secondary\", \"size\", \"small\", 3, \"click\"], [\"pRipple\", \"\", 1, \"mb-3\", 3, \"ngClass\"], [1, \"station-layout\", \"p-3\"], [1, \"flex\", \"align-items-center\", \"justify-content-between\", \"w-full\"], [1, \"station-info-left\", \"flex\", \"flex-column\", 2, \"min-width\", \"180px\"], [1, \"text-xl\", \"font-bold\", \"m-0\", \"mb-3\"], [1, \"text-primary\", \"no-underline\", \"hover:underline\", 3, \"routerLink\"], [1, \"power-info-card\", \"p-2\", \"border-round\"], [1, \"text-lg\", \"font-bold\", \"flex\", \"align-items-center\", \"gap-1\"], [\"icon\", \"solar-panel\", 1, \"text-primary\"], [1, \"station-chart-section\", \"flex-1\", \"mx-4\"], [1, \"flex\", \"align-items-stretch\", 2, \"min-height\", \"120px\"], [1, \"flex\", \"flex-column\", \"justify-content-between\", 2, \"flex\", \"1\", \"padding-right\", \"1rem\"], [1, \"text-center\", \"mb-2\"], [1, \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\"], [1, \"communication-status-circle\", 3, \"ngClass\"], [1, \"text-xs\", \"font-medium\", 3, \"ngClass\"], [1, \"text-center\"], [1, \"text-xs\", \"text-600\"], [1, \"text-sm\", \"font-semibold\"], [1, \"text-center\", \"mt-2\"], [1, \"text-xs\", \"text-500\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\"], [1, \"font-medium\", 3, \"ngClass\"], [1, \"pi\", \"pi-cloud\", \"text-blue-500\"], [1, \"current-power-card\", \"p-2\", \"border-round\", \"text-center\"], [1, \"text-sm\", \"font-bold\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-1\", \"my-1\"], [1, \"pi\", \"pi-flash\", \"text-green-500\"], [\"icon\", \"clock\", 1, \"text-xs\"], [1, \"text-center\", \"chart-container-full\", 2, \"flex\", \"2\", \"margin\", \"0 1rem\"], [4, \"ngIf\", \"ngIfElse\"], [\"noChartData\", \"\"], [1, \"flex\", \"flex-column\", \"justify-content-between\", 2, \"flex\", \"1\", \"padding-left\", \"1rem\"], [1, \"station-info-right\", \"text-center\", 2, \"min-width\", \"120px\"], [1, \"provider-card\", \"p-3\", \"border-round\", 2, \"background\", \"linear-gradient(135deg, rgba(25, 28, 83, 0.1) 0%, rgba(25, 28, 83, 0.2) 100%)\"], [1, \"text-lg\", \"font-bold\", \"flex\", \"align-items-center\", \"justify-content-center\", \"gap-2\", 2, \"color\", \"#191C53\"], [1, \"pi\", \"pi-server\", 2, \"color\", \"#191C53\"], [\"type\", \"line\", \"height\", \"140\", \"width\", \"300\", 1, \"mx-auto\", \"chart-section\", 3, \"data\", \"options\"], [1, \"text-center\", \"p-3\", \"h-full\", \"flex\", \"align-items-center\", \"justify-content-center\"], [\"icon\", \"chart-line\", 1, \"text-300\", \"text-3xl\", \"mb-2\"], [1, \"text-sm\", \"text-600\", \"m-0\"], [1, \"mt-4\"], [\"currentPageReportTemplate\", \"Showing {first} to {last} of {totalRecords} stations\", 3, \"rows\", \"totalRecords\", \"first\", \"rowsPerPageOptions\", \"showCurrentPageReport\", \"onPageChange\"]],\n    template: function IndexComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"p-card\", 2);\n        i0.ɵɵtemplate(3, IndexComponent_ng_template_3_Template, 11, 1, \"ng-template\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(4, \"div\", 1)(5, \"p-card\", 2);\n        i0.ɵɵtemplate(6, IndexComponent_ng_template_6_Template, 11, 0, \"ng-template\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(7, \"div\", 4)(8, \"p-card\");\n        i0.ɵɵtemplate(9, IndexComponent_ng_template_9_Template, 9, 2, \"ng-template\", 5)(10, IndexComponent_ng_template_10_Template, 11, 11, \"ng-template\", 3);\n        i0.ɵɵelementEnd()()();\n      }\n    },\n    dependencies: [i11.NgClass, i11.NgForOf, i11.NgIf, i5.DefaultValueAccessor, i5.NgControlStatus, i5.NgModel, i6.RouterLink, i12.Tooltip, i4.PrimeTemplate, i13.Button, i14.InputText, i15.Dropdown, i16.ChartjsComponent, i17.Badge, i18.FaIconComponent, i19.Card, i20.Tag, i21.Ripple, i22.Paginator],\n    styles: [\"\\n\\n.chart-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: visible;\\n}\\n\\n\\n\\nc-chart[_ngcontent-%COMP%] {\\n  position: relative;\\n  display: block;\\n}\\nc-chart[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  position: relative !important;\\n}\\n\\n\\n\\n[_nghost-%COMP%]     {\\n  \\n\\n  \\n\\n  \\n\\n  \\n\\n}\\n[_nghost-%COMP%]     .chartjs-tooltip {\\n  position: absolute !important;\\n  pointer-events: none;\\n  z-index: 9999 !important;\\n}\\n[_nghost-%COMP%]     .p-card-content {\\n  overflow: visible;\\n}\\n[_nghost-%COMP%]     canvas {\\n  position: relative !important;\\n}\\n[_nghost-%COMP%]     .p-card {\\n  z-index: 1;\\n  position: relative;\\n}\\n[_nghost-%COMP%]     .chart-container-full {\\n  z-index: 100 !important;\\n}\\n[_nghost-%COMP%]     .chart-container-full canvas {\\n  z-index: 101 !important;\\n}\\n\\n\\n\\n.station-card[_ngcontent-%COMP%]   .chart-section[_ngcontent-%COMP%] {\\n  position: relative;\\n  overflow: visible;\\n  min-height: 80px;\\n}\\n.station-card[_ngcontent-%COMP%]   .chart-section[_ngcontent-%COMP%]   c-chart[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n\\n\\n.station-layout[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  background: linear-gradient(135deg, var(--surface-0) 0%, var(--surface-50) 100%);\\n  position: relative;\\n}\\n\\n.station-info-left[_ngcontent-%COMP%] {\\n  padding-right: 1.5rem;\\n  position: relative;\\n}\\n.station-info-left[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #191C53, #2a2f7a);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n  transition: all 0.3s ease;\\n}\\n.station-info-left[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.02);\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.station-info-right[_ngcontent-%COMP%] {\\n  padding-left: 1.5rem;\\n  position: relative;\\n}\\n\\n.station-chart-section[_ngcontent-%COMP%] {\\n  \\n\\n  \\n\\n  \\n\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .chart-container-full[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 100%;\\n  min-height: 160px;\\n  position: relative;\\n  z-index: 10; \\n\\n  padding: 0.25rem;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .chart-container-full[_ngcontent-%COMP%]   canvas[_ngcontent-%COMP%] {\\n  max-width: 100% !important;\\n  max-height: 100% !important;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  position: relative;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.flex-column.justify-content-between[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  padding: 0.5rem 0;\\n  border-radius: 6px;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.flex-column.justify-content-between[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]   .text-xs[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  color: var(--text-color-secondary);\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.flex-column.justify-content-between[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]   .text-sm[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-top: 0.25rem;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.flex-column.justify-content-between[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]   .font-bold[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 700;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.flex-column.justify-content-between[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]   .font-bold.text-orange-600[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--orange-500), var(--orange-600));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.flex-column.justify-content-between[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]   .font-bold.text-green-600[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, var(--green-500), var(--green-600));\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n  min-width: 0;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(1), .station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(2) {\\n  flex: 1;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(3) {\\n  flex: 2;\\n}\\n.station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(4), .station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%]:nth-child(5) {\\n  flex: 1;\\n}\\n\\n\\n\\nc-chart[_ngcontent-%COMP%] {\\n  \\n\\n}\\n\\n\\n\\n.pi-cloud[_ngcontent-%COMP%] {\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));\\n}\\n\\n\\n\\n.power-info-card[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%], .current-power-card[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%], .provider-card[_ngcontent-%COMP%]   .pi[_ngcontent-%COMP%] {\\n  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));\\n}\\n\\n\\n\\n.communication-status-circle[_ngcontent-%COMP%] {\\n  width: 8px;\\n  height: 8px;\\n  border-radius: 50%;\\n  display: inline-block;\\n}\\n\\n.comm-status-gray[_ngcontent-%COMP%] {\\n  color: #6b7280 !important;\\n}\\n.comm-status-gray.communication-status-circle[_ngcontent-%COMP%] {\\n  background-color: #6b7280; \\n\\n}\\n\\n.comm-status-green[_ngcontent-%COMP%] {\\n  color: #10b981 !important;\\n}\\n.comm-status-green.communication-status-circle[_ngcontent-%COMP%] {\\n  background-color: #10b981; \\n\\n}\\n\\n.comm-status-orange[_ngcontent-%COMP%] {\\n  color: #f59e0b !important;\\n}\\n.comm-status-orange.communication-status-circle[_ngcontent-%COMP%] {\\n  background-color: #f59e0b; \\n\\n}\\n\\n.comm-status-red[_ngcontent-%COMP%] {\\n  color: #ef4444 !important;\\n}\\n.comm-status-red.communication-status-circle[_ngcontent-%COMP%] {\\n  background-color: #ef4444; \\n\\n}\\n\\n\\n\\n[_nghost-%COMP%]     .p-tag-gray, [_nghost-%COMP%]     .p-tag-gray.p-tag, [_nghost-%COMP%]     p-tag .p-tag-gray, [_nghost-%COMP%]     p-tag .p-tag.p-tag-gray {\\n  background: #626670 !important; \\n\\n  background-color: #6b7280 !important;\\n  color: white !important;\\n  border-color: #6b7280 !important;\\n  border: 1px solid #6b7280 !important;\\n}\\n\\n\\n\\n[_nghost-%COMP%]     p-tag .p-tag {\\n  border-radius: 6px;\\n  font-weight: 500;\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n[_nghost-%COMP%]     p-tag .p-tag:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\\n}\\n\\n\\n\\n@media (max-width: 1200px) {\\n  .station-chart-section[_ngcontent-%COMP%]   .chart-container-full[_ngcontent-%COMP%] {\\n    padding: 0.25rem;\\n    min-height: 140px;\\n  }\\n  .station-chart-section[_ngcontent-%COMP%]   c-chart[_ngcontent-%COMP%] {\\n    height: 120px !important;\\n    width: 250px !important;\\n  }\\n}\\n@media (max-width: 768px) {\\n  .station-layout[_ngcontent-%COMP%]   .flex.align-items-center.justify-content-between[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .station-layout[_ngcontent-%COMP%]   .station-info-left[_ngcontent-%COMP%], .station-layout[_ngcontent-%COMP%]   .station-info-right[_ngcontent-%COMP%] {\\n    border: none;\\n    padding: 0;\\n    text-align: center;\\n    min-width: auto;\\n  }\\n  .station-layout[_ngcontent-%COMP%]   .station-chart-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin: 0;\\n  }\\n  .station-layout[_ngcontent-%COMP%]   .station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    min-height: auto;\\n  }\\n  .station-layout[_ngcontent-%COMP%]   .station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    border: none;\\n    padding: 0.5rem 0;\\n  }\\n  .station-layout[_ngcontent-%COMP%]   .station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]    > div.chart-container-full[_ngcontent-%COMP%] {\\n    order: 2;\\n    margin: 1rem 0;\\n    min-height: 80px;\\n  }\\n  .station-layout[_ngcontent-%COMP%]   .station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]   .flex.flex-column.justify-content-between[_ngcontent-%COMP%] {\\n    flex-direction: row;\\n    justify-content: space-around;\\n    flex-wrap: wrap;\\n  }\\n  .station-layout[_ngcontent-%COMP%]   .station-chart-section[_ngcontent-%COMP%]   .flex.align-items-stretch[_ngcontent-%COMP%]   .flex.flex-column.justify-content-between[_ngcontent-%COMP%]    > div[_ngcontent-%COMP%] {\\n    flex: 1 1 45%;\\n    text-align: center;\\n    margin: 0.25rem 0;\\n  }\\n  c-chart[_ngcontent-%COMP%] {\\n    height: 100px !important;\\n    width: 220px !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["debounceTime", "NavigationEnd", "filter", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r0", "stations", "length", "ɵɵproperty", "ctx_r4", "totalItems", "ɵɵtemplate", "IndexComponent_ng_template_9_p_badge_5_Template", "ɵɵlistener", "IndexComponent_ng_template_9_Template_p_button_click_7_listener", "ɵɵrestoreView", "_r6", "ctx_r5", "ɵɵnextContext", "ɵɵresetView", "clearFilters", "IndexComponent_ng_template_9_Template_p_button_click_8_listener", "ctx_r7", "refreshData", "ctx_r2", "isRefreshing", "IndexComponent_ng_template_10_div_8_Template_p_button_click_5_listener", "_r12", "ctx_r11", "ɵɵelementContainerStart", "ɵɵelementContainerEnd", "chartData_r18", "ctx_r15", "getChartOptionsForStation", "station_r13", "IndexComponent_ng_template_10_div_9_ng_template_2_ng_container_45_Template", "IndexComponent_ng_template_10_div_9_ng_template_2_ng_template_46_Template", "ɵɵtemplateRefExtractor", "ɵɵpureFunction1", "_c0", "id", "ɵɵtextInterpolate1", "ctx_r14", "getDisplayName", "name", "formatNumber", "getInstalledCapacity", "getStationsSumData", "getCommunicationStatusClass", "invertersCount", "getTrackerStatusClass", "getTrackerCount", "temperature", "getCurrent<PERSON>ower", "getStationLastUpdate", "getStationsRealTimeData", "_r17", "getPortfolio", "get<PERSON>wner", "getCountry", "getPrefecture", "getDataProvider", "provider", "IndexComponent_ng_template_10_div_9_ng_template_2_Template", "status", "toLowerCase", "IndexComponent_ng_template_10_div_10_Template_p_paginator_onPageChange_1_listener", "$event", "_r22", "ctx_r21", "onPageChange", "ctx_r10", "itemsPerPage", "currentPage", "paginationOptions", "IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_2_listener", "_r24", "ctx_r23", "<PERSON><PERSON><PERSON><PERSON>", "IndexComponent_ng_template_10_Template_p_dropdown_onChange_2_listener", "ctx_r25", "onProviderChange", "IndexComponent_ng_template_10_Template_p_dropdown_ngModelChange_3_listener", "ctx_r26", "selectedStatus", "IndexComponent_ng_template_10_Template_p_dropdown_onChange_3_listener", "ctx_r27", "onStatusChange", "IndexComponent_ng_template_10_Template_input_ngModelChange_6_listener", "ctx_r28", "searchTerm", "IndexComponent_ng_template_10_Template_input_input_6_listener", "ctx_r29", "onSearchChange", "IndexComponent_ng_template_10_div_8_Template", "IndexComponent_ng_template_10_div_9_Template", "IndexComponent_ng_template_10_div_10_Template", "ctx_r3", "sortOptionsCountry", "sortOptionsStatus", "paginatedStations", "trackByStationId", "IndexComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "router", "cacheService", "dateUtils", "trackerService", "stationConfigService", "cdr", "filteredStations", "sortOrder", "sortField", "sourceCities", "targetCities", "orderCities", "stationsData", "Map", "stationsRawData", "stationsSumData", "currentPowerCache", "lastUpdateCache", "invertersCache", "deviceConfigurationsCache", "trackersCache", "trackerStatusCache", "stationConfigurations", "subscription", "configUpdate$", "pipe", "subscribe", "config", "initializeChartOptions", "ngOnInit", "getUserProviders", "initializeConfigurations", "events", "event", "url", "console", "log", "loadStationConfigurations", "getAllConfigurations", "next", "configurations", "clear", "for<PERSON>ach", "set", "stationId", "detectChanges", "error", "barOptions", "maintainAspectRatio", "plugins", "legend", "display", "scales", "x", "y", "lineOptions", "responsive", "interaction", "intersect", "mode", "elements", "line", "tension", "borderWidth", "point", "radius", "hoverRadius", "hitRadius", "tooltip", "enabled", "position", "external", "backgroundColor", "titleColor", "bodyColor", "borderColor", "cornerRadius", "displayColors", "padding", "caretPadding", "caretSize", "titleFont", "size", "weight", "bodyFont", "tooltipItem", "parsed", "undefined", "callbacks", "title", "context", "xValue", "hours", "Math", "floor", "minutes", "round", "formattedTime", "toString", "padStart", "label", "dataset", "value", "toFixed", "replace", "type", "min", "max", "ticks", "stepSize", "callback", "color", "font", "grid", "border", "width", "text", "maxTicksLimit", "maxRotation", "minRotation", "animation", "duration", "easing", "layout", "top", "bottom", "left", "right", "then", "providersData", "getUserStations", "setStations", "initializeFilterOptions", "applyFilters", "loadAllStationsData", "navigate", "catch", "station", "loadStationData", "now", "Date", "formattedStartDate", "getFullYear", "getMonth", "getDate", "toISOString", "formattedEndDate", "request", "devIds", "deviceIds", "devTypeId", "startDateTime", "endDateTime", "separated", "searchType", "getStationHistoricData", "data", "filteredData", "filterDataByCurrentTime", "documentStyle", "getComputedStyle", "document", "documentElement", "uniqueDates", "Array", "from", "Set", "map", "d", "dateTime", "uniqueDateDescriptions", "dateDescription", "uniqueInverters", "datasets", "activePowerByName", "push", "activePower", "inv", "getRandomColor", "fill", "firstName", "dateTimesForFirstName", "convertedDatasets", "convertDataForStaticXAxis", "lineData", "labels", "sum", "clearStationCache", "warn", "loadStationTrackers", "loadStationDeviceConfigurations", "logoBlueVariations", "random", "has", "get", "getDevices", "devices", "find", "s", "delete", "item", "getStationsInverters", "deviceConfigs", "result", "latest", "reduce", "latestSoFar", "current", "getTime", "toLocaleString", "hour", "minute", "hour12", "day", "month", "year", "timeZone", "latestTimestamp", "latestEntries", "baseOptions", "stationData", "rawData", "maxDataValue", "calculatedPower", "maxValue", "ceil", "midValue", "dateTimes", "convertedData", "index", "getHours", "getMinutes", "decimals", "originalName", "customName", "owner", "portfolio", "country", "prefecture", "installedCapacityKW", "originalProvider", "dataProvider", "onSortChange", "indexOf", "substring", "onFilter", "dv", "target", "providers", "statuses", "filtered", "searchLower", "includes", "location", "sort", "a", "b", "nameA", "nameB", "localeCompare", "updatePaginationOptions", "updatePagination", "startIndex", "endIndex", "slice", "page", "rows", "clearAllCaches", "hasEquipmentData", "inverters", "mmpt", "string", "pvn", "currentTime", "itemDateTime", "itemTime", "initMap", "mapSrc", "currentHour", "lastUpdateStr", "datePart", "timePart", "split", "Number", "lastUpdate", "hoursDiff", "getTrackersByStation", "trackers", "completedRequests", "hasErrors", "tracker", "getTrackerData", "trackingData", "windSpeed", "<PERSON><PERSON><PERSON><PERSON>", "errorMessage", "hasNonTrackingStatus", "some", "hasTimeDelayIssues", "checkNextTrackingTimeDelays", "totalTrackingData", "allNextTrackingTimes", "nextTracking", "timeParts", "today", "nextTrackingTime", "parseInt", "latestTime", "time", "twentyMinutesInMs", "timeDifference", "toTimeString", "ngOnDestroy", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "LayoutService", "i2", "StationsService", "i3", "ProvidersService", "i4", "MessageService", "i5", "FormBuilder", "i6", "Router", "i7", "CacheService", "i8", "DateUtilsService", "i9", "TrackerService", "i10", "StationConfigurationService", "ChangeDetectorRef", "_2", "selectors", "decls", "vars", "consts", "template", "IndexComponent_Template", "rf", "ctx", "IndexComponent_ng_template_3_Template", "IndexComponent_ng_template_6_Template", "IndexComponent_ng_template_9_Template", "IndexComponent_ng_template_10_Template"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\index\\index.component.html"], "sourcesContent": ["import { Component, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectorRef } from '@angular/core';\r\nimport { MenuItem, MessageService } from 'primeng/api';\r\nimport { Product } from '../../api/product';\r\nimport { ProductService } from '../../service/product.service';\r\nimport { Subscription, debounceTime } from 'rxjs';\r\nimport { LayoutService } from 'src/app/layout/service/app.layout.service';\r\nimport { SelectItem } from 'primeng/api';\r\nimport { DataView } from 'primeng/dataview';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { DateUtilsService } from '../../service/date-utils.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { IProvider, IUserProvider } from '../../api/responses';\r\nimport { StationConfigurationService, StationConfiguration } from '../../service/station-configuration.service';\r\nimport { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { GetHistoricDataRequest, SaveUserProvidersRequest } from '../../api/requests';\r\nimport { Router, NavigationEnd } from '@angular/router';\r\nimport { filter } from 'rxjs/operators';\r\nimport { TrackerService, TrackerResponse } from '../../service/tracker.service';\r\n\r\n\r\n@Component({\r\n    templateUrl: './index.component.html', \r\n    styleUrls: ['./index.component.scss']\r\n})\r\nexport class IndexComponent implements OnInit, OnDestroy {\r\n\r\n    items!: MenuItem[];\r\n\r\n    stations: Station[] = [];\r\n    filteredStations: Station[] = [];\r\n    paginatedStations: Station[] = [];\r\n\r\n    chartData: any;\r\n\r\n    chartOptions: any;\r\n\r\n    subscription!: Subscription;\r\n\r\n    sortOptionsCountry: SelectItem[] = [];\r\n\r\n    sortOptionsStatus: SelectItem[] = [];\r\n\r\n    sortOrder: number = 0;\r\n\r\n    sortField: string = '';\r\n\r\n    // Filtering properties\r\n    searchTerm: string = '';\r\n    selectedProvider: string = '';\r\n    selectedStatus: string = '';\r\n\r\n    // Pagination properties\r\n    currentPage: number = 0;\r\n    itemsPerPage: number = 20;\r\n    totalItems: number = 0;\r\n\r\n    // Pagination options - cached to avoid infinite loop\r\n    paginationOptions: number[] = [20, 40, 80, 100];\r\n\r\n    // Loading state\r\n    isRefreshing: boolean = false;\r\n\r\n    sourceCities: any[] = [];\r\n\r\n    targetCities: any[] = [];\r\n\r\n    orderCities: any[] = [];\r\n\r\n    mapSrc:string;\r\n\r\n    barOptions:any;\r\n    lineOptions:any;\r\n    stationsData:Map<string,any> = new Map();\r\n    stationsRawData:Map<string,any> = new Map();\r\n    stationsSumData:Map<string,number> = new Map();\r\n\r\n    // Cache για τα computed values\r\n    private currentPowerCache: Map<string, number> = new Map();\r\n    private lastUpdateCache: Map<string, string> = new Map();\r\n    private invertersCache: Map<string, number> = new Map();\r\n    private deviceConfigurationsCache: Map<string, any[]> = new Map();\r\n\r\n    // Tracker related caches\r\n    private trackersCache: Map<string, TrackerResponse[]> = new Map();\r\n    private trackerStatusCache: Map<string, 'none' | 'all-tracking' | 'has-errors'> = new Map();\r\n\r\n    // Station configurations cache\r\n    private stationConfigurations = new Map<string, StationConfiguration>();\r\n\r\n\r\n    constructor(public layoutService: LayoutService,\r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private router: Router,\r\n        private cacheService: CacheService,\r\n        private dateUtils: DateUtilsService,\r\n        private trackerService: TrackerService,\r\n        private stationConfigService: StationConfigurationService,\r\n        private cdr: ChangeDetectorRef) {\r\n        this.subscription = this.layoutService.configUpdate$\r\n        .pipe(debounceTime(25))\r\n        .subscribe((config) => {\r\n         //   this.initChart();\r\n        });\r\n\r\n        this.initializeChartOptions();\r\n    }\r\n\r\n    ngOnInit() {\r\n        this.getUserProviders();\r\n        this.initializeConfigurations();\r\n    }\r\n\r\n    private initializeConfigurations() {\r\n        // Subscribe to router events to refresh configurations when navigating back\r\n        this.router.events.pipe(\r\n            filter(event => event instanceof NavigationEnd)\r\n        ).subscribe((event: NavigationEnd) => {\r\n            if (event.url === '/app' || event.url === '/app/') {\r\n                console.log('Navigated back to index, refreshing configurations');\r\n                this.loadStationConfigurations();\r\n            }\r\n        });\r\n\r\n        // Load station configurations initially\r\n        this.loadStationConfigurations();\r\n    }\r\n\r\n    private loadStationConfigurations() {\r\n        console.log('Loading station configurations...');\r\n        this.stationConfigService.getAllConfigurations().subscribe({\r\n            next: (configurations) => {\r\n                console.log('Configurations loaded:', configurations.length);\r\n\r\n                // Update local cache\r\n                this.stationConfigurations.clear();\r\n                configurations.forEach(config => {\r\n                    this.stationConfigurations.set(config.stationId, config);\r\n                });\r\n\r\n                // Force change detection when configurations update\r\n                this.cdr.detectChanges();\r\n            },\r\n            error: (error) => {\r\n                console.error('Error loading configurations:', error);\r\n            }\r\n        });\r\n    }\r\n\r\n    private initializeChartOptions() {\r\n        this.barOptions = {\r\n            maintainAspectRatio: false,\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                display: false\r\n              },\r\n              y: {\r\n                display: false\r\n              }\r\n            }\r\n          };\r\n        \r\n          this.lineOptions = {\r\n            responsive: true,\r\n            maintainAspectRatio: false,\r\n            interaction: {\r\n              intersect: false,\r\n              mode: 'index'\r\n            },\r\n            elements: {\r\n              line: {\r\n                tension: 0.4,\r\n                borderWidth: 2\r\n              },\r\n              point: {\r\n                radius: 0,\r\n                hoverRadius: 4,\r\n                hitRadius: 10\r\n              }\r\n            },\r\n            plugins: {\r\n              legend: {\r\n                display: false\r\n              },\r\n              tooltip: {\r\n                enabled: true,\r\n                mode: 'index',\r\n                intersect: false,\r\n                position: 'nearest',\r\n                external: null, // Ensure we use the default tooltip positioning\r\n                backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n                titleColor: '#ffffff',\r\n                bodyColor: '#ffffff',\r\n                borderColor: 'rgba(255, 255, 255, 0.1)',\r\n                borderWidth: 1,\r\n                cornerRadius: 8,\r\n                displayColors: false,\r\n                padding: 12,\r\n                caretPadding: 6,\r\n                caretSize: 5,\r\n                titleFont: {\r\n                  size: 14,\r\n                  weight: 'bold'\r\n                },\r\n                bodyFont: {\r\n                  size: 13\r\n                },\r\n                filter: function(tooltipItem: any) {\r\n                  // Εμφανίζουμε tooltip μόνο αν υπάρχουν δεδομένα\r\n                  return tooltipItem.parsed.y !== null && tooltipItem.parsed.y !== undefined;\r\n                },\r\n                callbacks: {\r\n                  title: function(context: any) {\r\n                    //console.log(\"Tooltip title callback called!\", context);\r\n                    if (context && context.length > 0) {\r\n                      const xValue = context[0].parsed.x;\r\n                      //console.log(\"xValue: \" + xValue);\r\n                      if (xValue !== undefined && xValue !== null) {\r\n                        // Απλή μετατροπή: decimal hours σε HH:MM\r\n                        const hours = Math.floor(xValue);\r\n                        const minutes = Math.round((xValue - hours) * 60);\r\n                        const formattedTime = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;\r\n                        return `Time: ${formattedTime}`;\r\n                      }\r\n                    }\r\n                    return 'Time: --:--';\r\n                  },\r\n                  label: function(context: any) {\r\n                    const label = context.dataset.label || '';\r\n                    const value = context.parsed.y;\r\n                    return `${label}: ${value.toFixed(2).replace('.', ',')} kW`;\r\n                  }\r\n                }\r\n              }\r\n            },\r\n            scales: {\r\n              x: {\r\n                type: 'linear',\r\n                display: true,\r\n                position: 'bottom',\r\n                min: 0,\r\n                max: 24,\r\n                ticks: {\r\n                  stepSize: 12,\r\n                  callback: function(value: any) {\r\n                    // Static values: 00:00, 12:00, 24:00\r\n                    if (value === 0) return '00:00';\r\n                    if (value === 12) return '12:00';\r\n                    if (value === 24) return '24:00';\r\n                    return '';\r\n                  },\r\n                  color: '#374151',\r\n                  font: {\r\n                    size: 10,\r\n                    weight: 'bold'\r\n                  },\r\n                  padding: 4\r\n                },\r\n                grid: {\r\n                  display: false\r\n                },\r\n                border: {\r\n                  display: true,\r\n                  color: '#374151',\r\n                  width: 2\r\n                }\r\n              },\r\n              y: {\r\n                display: true,\r\n                position: 'left',\r\n                title: {\r\n                  display: true,\r\n                  text: 'kW',\r\n                  color: '#374151',\r\n                  font: {\r\n                    size: 12,\r\n                    weight: 'bold'\r\n                  }\r\n                },\r\n                ticks: {\r\n                  maxTicksLimit: 3,\r\n                  callback: function(value: any) {\r\n                    // Will be dynamically set per station\r\n                    return value.toFixed(0);\r\n                  },\r\n                  color: '#374151',\r\n                  font: {\r\n                    size: 10,\r\n                    weight: 'bold'\r\n                  },\r\n                  padding: 4,\r\n                  maxRotation: 0,\r\n                  minRotation: 0\r\n                },\r\n                grid: {\r\n                  display: false\r\n                },\r\n                border: {\r\n                  display: true,\r\n                  color: '#374151',\r\n                  width: 2\r\n                }\r\n              }\r\n            },\r\n            animation: {\r\n              duration: 750,\r\n              easing: 'easeInOutQuart'\r\n            },\r\n            layout: {\r\n              padding: {\r\n                top: 20,\r\n                bottom: 5,\r\n                left: 5,\r\n                right: 5\r\n              }\r\n            }\r\n          };\r\n\r\n\r\n          \r\n        \r\n    }\r\n\r\n    getUserProviders(){\r\n      this.isRefreshing = true;\r\n      this.providersService.getUserProviders().then(providersData => {\r\n        if (providersData.length > 0){\r\n          this.stationsService.getUserStations().then(stationsData => {\r\n            this.cacheService.setStations(stationsData);\r\n            this.stations = stationsData;\r\n            console.log(\"stations set\")\r\n            console.log(stationsData)\r\n\r\n            // Initialize filter options\r\n            this.initializeFilterOptions();\r\n\r\n            // Apply filters and pagination\r\n            this.applyFilters();\r\n\r\n            // Φορτώνουμε τα δεδομένα για κάθε σταθμό\r\n            this.loadAllStationsData();\r\n\r\n            this.isRefreshing = false;\r\n          });\r\n        }else{\r\n          this.router.navigate(['/app/providers']);\r\n        }\r\n      }).catch(error => {\r\n        console.error('Error loading providers:', error);\r\n        this.isRefreshing = false;\r\n      });\r\n    }\r\n\r\n    // Νέα μέθοδος για φόρτωση δεδομένων όλων των σταθμών\r\n    loadAllStationsData() {\r\n        if (!this.stations || this.stations.length === 0) return;\r\n        \r\n        // Φορτώνουμε τα δεδομένα για κάθε σταθμό\r\n        this.stations.forEach(station => {\r\n            this.loadStationData(station);\r\n        });\r\n    }\r\n\r\n    // Νέα μέθοδος για φόρτωση δεδομένων ενός σταθμού\r\n    loadStationData(station: Station) {\r\n        if (!station) return;\r\n        \r\n        const now = new Date();\r\n        const formattedStartDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0).toISOString();\r\n        const formattedEndDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1, 0, 0, 0).toISOString();\r\n        \r\n        let request: GetHistoricDataRequest = {\r\n            devIds: station.deviceIds,\r\n            devTypeId: 1,\r\n            startDateTime: formattedStartDate,\r\n            endDateTime: formattedEndDate,\r\n            separated: true,\r\n            searchType: null,\r\n            stationId: station.id\r\n        };\r\n        \r\n        this.stationsService.getStationHistoricData(request).then(data => {\r\n            if (data && data.data && data.data.length > 0) {\r\n                // Filter data to not show beyond current time\r\n                const filteredData = this.filterDataByCurrentTime(data.data);\r\n\r\n                const documentStyle = getComputedStyle(document.documentElement);\r\n\r\n                // Βρίσκουμε τις μοναδικές ημερομηνίες με σειρά\r\n                const uniqueDates = Array.from(new Set(data.data.map(d => d.dateTime)));\r\n                const uniqueDateDescriptions = Array.from(new Set(data.data.map(d => d.dateDescription)));\r\n                // Βρίσκουμε τους μοναδικούς inverters\r\n                const uniqueInverters = Array.from(new Set(data.data.map(d => d.name)));\r\n\r\n                var datasets:any[] = [];\r\n                // const uniqueInverters = Array.from(new Set(this.energyData.data.map(d => d.name)));\r\n                const activePowerByName: Record<string, number[]> = {};\r\n\r\n                data.data.forEach(d => {\r\n                    if (!activePowerByName[d.name]) {\r\n                        activePowerByName[d.name] = [];\r\n                    }\r\n                    activePowerByName[d.name].push(d.activePower);\r\n                });\r\n\r\n                uniqueInverters.forEach(inv => {\r\n                    var color = this.getRandomColor();\r\n                    datasets.push({\r\n                        label: inv,\r\n                        data: activePowerByName[inv],\r\n                        fill: false,\r\n                        backgroundColor: color,\r\n                        borderColor: color,\r\n                        tension: .4\r\n                    });\r\n\r\n                });\r\n\r\n                const firstName = data.data[0].name; // Παίρνουμε το πρώτο name\r\n                const dateTimesForFirstName = data.data\r\n                .filter(d => d.name === firstName) // Φιλτράρουμε μόνο τα αντικείμενα με το ίδιο name\r\n                .map(d => d.dateTime); // Παίρνουμε μόνο τα dateTime\r\n\r\n                // Μετατρέπουμε τα δεδομένα για στατικό άξονα X (0-24 ώρες) και κρατάμε time mapping\r\n                const convertedDatasets = this.convertDataForStaticXAxis(datasets, dateTimesForFirstName);\r\n\r\n                const lineData = {\r\n                    labels: [], // Κενά labels για linear scale\r\n                    datasets: convertedDatasets\r\n                };\r\n\r\n\r\n\r\n                // const lineData = {\r\n                //     labels: filteredData.map((e, index) =>\r\n                //         index % 2 === 0 ? this.dateUtils.formatTimeForChart(e.dateTime) : ''\r\n                //     ),\r\n                //     datasets: [\r\n                //         {\r\n                //             label: 'Active Power',\r\n                //             data: filteredData.map(e => e.activePower),\r\n                //             fill: false,\r\n                //             backgroundColor: documentStyle.getPropertyValue('--primary-500'),\r\n                //             borderColor: documentStyle.getPropertyValue('--primary-500'),\r\n                //             tension: .4\r\n                //         },\r\n                //         {\r\n                //             label: 'Total Input Power',\r\n                //             data: filteredData.map(e => e.totalInputPower),\r\n                //             fill: false,\r\n                //             backgroundColor: documentStyle.getPropertyValue('--primary-200'),\r\n                //             borderColor: documentStyle.getPropertyValue('--primary-200'),\r\n                //             tension: .4\r\n                //         }\r\n                //     ]\r\n                // };\r\n\r\n                // Αποθηκεύουμε τα δεδομένα στο Map με κλειδί το ID του σταθμού\r\n                this.stationsData.set(station.id, lineData);\r\n                this.stationsRawData.set(station.id, filteredData);\r\n                this.stationsSumData.set(station.id, data.sum);\r\n\r\n                // Set fallback inverters count from raw data if device configs not loaded yet\r\n                if (!station.invertersCount) {\r\n                    station.invertersCount = uniqueInverters.length;\r\n                }\r\n\r\n                // Καθαρίζουμε το cache για αυτό το station\r\n                this.clearStationCache(station.id);\r\n            } else {\r\n                // Δεν υπάρχουν δεδομένα για αυτόν τον σταθμό\r\n                console.warn(`No data available for station ${station.name} (${station.id})`);\r\n\r\n                // Αποθηκεύουμε κενά δεδομένα για να μην προσπαθήσουμε ξανά\r\n                this.stationsData.set(station.id, { labels: [], datasets: [] });\r\n                this.stationsRawData.set(station.id, []);\r\n                this.stationsSumData.set(station.id, null);\r\n            }\r\n        }).catch(error => {\r\n            console.error(`Error loading data for station ${station.name} (${station.id}):`, error);\r\n\r\n            // Αποθηκεύουμε κενά δεδομένα σε περίπτωση error\r\n            this.stationsData.set(station.id, { labels: [], datasets: [] });\r\n            this.stationsRawData.set(station.id, []);\r\n            this.stationsSumData.set(station.id, null);\r\n        });\r\n\r\n        // Φορτώνουμε tracker data για αυτόν τον σταθμό\r\n        this.loadStationTrackers(station.id);\r\n\r\n        // Φορτώνουμε device configurations για σωστό count\r\n        this.loadStationDeviceConfigurations(station.id);\r\n    }\r\n    \r\n\r\n    getRandomColor(){\r\n        // Use variations of the logo blue color (#191C53)\r\n        const logoBlueVariations = [\r\n            '#191C53', // Main logo blue\r\n            '#2a2f7a', // Lighter variation\r\n            '#0f1240', // Darker variation\r\n            '#3d4299', // Even lighter\r\n            '#1a1e5a', // Slightly lighter than main\r\n            '#252a66', // Medium variation\r\n        ];\r\n\r\n        return logoBlueVariations[Math.floor(Math.random() * logoBlueVariations.length)];\r\n    }\r\n\r\n    // Τροποποιημένη μέθοδος που επιστρέφει τα προ-φορτωμένα δεδομένα\r\n    getStationsRealTimeData(station: Station) {\r\n        // Επιστρέφουμε τα δεδομένα από το Map αν υπάρχουν\r\n        if (station && station.id && this.stationsData.has(station.id)) {\r\n            return this.stationsData.get(station.id);\r\n        }\r\n        \r\n        // Αν δεν έχουν φορτωθεί ακόμα, επιστρέφουμε null\r\n        return null;\r\n    }\r\n\r\n    // Load device configurations for accurate device count\r\n    private loadStationDeviceConfigurations(stationId: string) {\r\n        if (!stationId) return;\r\n\r\n        this.stationConfigService.getDevices(stationId)\r\n            .subscribe({\r\n                next: (devices) => {\r\n                    this.deviceConfigurationsCache.set(stationId, devices);\r\n\r\n                    // Update station object with pre-calculated inverters count\r\n                    const station = this.stations.find(s => s.id === stationId);\r\n                    if (station) {\r\n                        station.invertersCount = devices.length;\r\n                        console.log(`Station ${stationId}: Set invertersCount to ${devices.length} from device configs`);\r\n                    }\r\n\r\n                    // Clear inverters cache to force recalculation\r\n                    this.invertersCache.delete(stationId);\r\n                },\r\n                error: (error) => {\r\n                    console.warn(`Could not load device configurations for station ${stationId}:`, error);\r\n                    // Set empty array so we don't keep trying\r\n                    this.deviceConfigurationsCache.set(stationId, []);\r\n\r\n                    // Set fallback count using raw data\r\n                    const station = this.stations.find(s => s.id === stationId);\r\n                    if (station) {\r\n                        const data = this.stationsRawData.get(stationId);\r\n                        station.invertersCount = data && data.length > 0\r\n                            ? new Set(data.map(item => item.name)).size\r\n                            : 0;\r\n                    }\r\n                }\r\n            });\r\n    }\r\n\r\n    // Clear cache για ένα συγκεκριμένο station\r\n    private clearStationCache(stationId: string) {\r\n        // Δεν χρειάζεται να διαγράψουμε chart options cache αφού δεν το χρησιμοποιούμε πια\r\n        this.currentPowerCache.delete(stationId);\r\n        this.lastUpdateCache.delete(stationId);\r\n        this.invertersCache.delete(stationId);\r\n        this.trackersCache.delete(stationId);\r\n        this.trackerStatusCache.delete(stationId);\r\n        this.deviceConfigurationsCache.delete(stationId);\r\n    }\r\n\r\n    getStationsSumData(stationId:string){\r\n      return this.stationsSumData.get(stationId);\r\n    }\r\n\r\n    getStationsInverters(stationId:string){\r\n      // Χρησιμοποιούμε cache\r\n      if (this.invertersCache.has(stationId)) {\r\n        console.log(\"Cache:\" + stationId + \" \" + this.invertersCache.get(stationId)!);\r\n        return this.invertersCache.get(stationId)!;\r\n      }\r\n\r\n      // Προτεραιότητα στα device configurations (πιο ακριβή)\r\n      const deviceConfigs = this.deviceConfigurationsCache.get(stationId);\r\n      let result = 0;\r\n      \r\n      if (deviceConfigs && deviceConfigs.length > 0) {\r\n        console.log(\"Configs:\" + stationId + \" \" + deviceConfigs.length);\r\n        // Μετράμε τα device configurations\r\n        result = deviceConfigs.length;\r\n      } else {\r\n        // Fallback στα raw data από stations API\r\n        const data = this.stationsRawData.get(stationId);\r\n        if (data && data.length > 0) {\r\n          result = new Set(data.map(item => item.name)).size;\r\n        }\r\n      }\r\n\r\n      // Αποθηκεύουμε στο cache\r\n      this.invertersCache.set(stationId, result);\r\n      return result;\r\n    }\r\n\r\n    getStationLastUpdate(stationId: string) {\r\n      // Χρησιμοποιούμε cache\r\n      if (this.lastUpdateCache.has(stationId)) {\r\n        return this.lastUpdateCache.get(stationId)!;\r\n      }\r\n\r\n      const data = this.stationsRawData.get(stationId);\r\n      if (!data || data.length === 0) {\r\n        this.lastUpdateCache.set(stationId, \"-\");\r\n        return \"-\";\r\n      }\r\n\r\n      const latest = data.reduce((latestSoFar, current) => {\r\n        return new Date(current.dateTime).getTime() > new Date(latestSoFar.dateTime).getTime()\r\n          ? current\r\n          : latestSoFar;\r\n      });\r\n\r\n      const result = new Date(latest.dateTime).toLocaleString(\"en-GB\", {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: false,\r\n        day: '2-digit',\r\n        month: '2-digit',\r\n        year: 'numeric',\r\n        timeZone: 'Europe/Athens'\r\n      });\r\n\r\n      // Αποθηκεύουμε στο cache\r\n      this.lastUpdateCache.set(stationId, result);\r\n      return result;\r\n    }\r\n\r\n    getCurrentPower(stationId: string): number {\r\n      // Χρησιμοποιούμε cache\r\n      if (this.currentPowerCache.has(stationId)) {\r\n        return this.currentPowerCache.get(stationId)!;\r\n      }\r\n\r\n      const data = this.stationsRawData.get(stationId);\r\n      if (!data || data.length === 0) {\r\n        this.currentPowerCache.set(stationId, 0);\r\n        return 0;\r\n      }\r\n\r\n      // Find the latest timestamp\r\n      const latestTimestamp = Math.max(...data.map(item => new Date(item.dateTime).getTime()));\r\n\r\n      // Get all entries with the latest timestamp and sum their activePower\r\n      const latestEntries = data.filter(item =>\r\n        new Date(item.dateTime).getTime() === latestTimestamp\r\n      );\r\n\r\n      const result = latestEntries.reduce((sum, item) => sum + (item.activePower || 0), 0);\r\n\r\n      // Αποθηκεύουμε στο cache\r\n      this.currentPowerCache.set(stationId, result);\r\n      return result;\r\n    }\r\n\r\n    // Get chart options with dynamic axis values for each station\r\n    getChartOptionsForStation(station: Station): any {\r\n      // Δεν χρησιμοποιούμε cache για chart options γιατί τα δεδομένα αλλάζουν\r\n\r\n      // Shallow copy των options και manual copy των callbacks (JSON.parse χάνει τις functions)\r\n      const baseOptions = {\r\n        ...this.lineOptions,\r\n        plugins: {\r\n          ...this.lineOptions.plugins,\r\n          tooltip: {\r\n            ...this.lineOptions.plugins.tooltip,\r\n            callbacks: {\r\n              ...this.lineOptions.plugins.tooltip.callbacks\r\n            }\r\n          }\r\n        },\r\n        scales: {\r\n          ...this.lineOptions.scales,\r\n          x: { ...this.lineOptions.scales.x },\r\n          y: { ...this.lineOptions.scales.y }\r\n        }\r\n      };\r\n\r\n      // Get station data for X-axis calculation\r\n      const stationData = this.stationsData.get(station.id);\r\n      const rawData = this.stationsRawData.get(station.id);\r\n\r\n      // Calculate Y-axis values με καλύτερη κλιμάκωση\r\n      let maxDataValue = 0;\r\n\r\n      // Get max value from station data\r\n      if (rawData && rawData.length > 0) {\r\n        maxDataValue = Math.max(...rawData.map((item: any) => item.activePower || 0));\r\n      }\r\n\r\n      // Αν δεν υπάρχουν δεδομένα, χρησιμοποιούμε το calculated power\r\n      if (maxDataValue === 0) {\r\n        const calculatedPower = this.getStationsSumData(station.id);\r\n        if (calculatedPower && calculatedPower > 0) {\r\n          maxDataValue = calculatedPower;\r\n        }\r\n      }\r\n\r\n      // Προσθέτουμε 10% περιθώριο στο ταβάνι (λιγότερο από πριν)\r\n      let maxValue = maxDataValue * 1.1;\r\n\r\n      // Πιο φυσική στρογγυλοποίηση - κρατάμε πιο κοντά στα πραγματικά δεδομένα\r\n      if (maxValue <= 1) {\r\n        maxValue = Math.ceil(maxValue * 10) / 10; // 0.1, 0.2, 0.3, etc.\r\n      } else if (maxValue <= 5) {\r\n        maxValue = Math.ceil(maxValue * 2) / 2; // 0.5 increments: 1.5, 2.0, 2.5, etc.\r\n      } else if (maxValue <= 20) {\r\n        maxValue = Math.ceil(maxValue); // 1 unit increments: 6, 7, 8, 9, 10, etc.\r\n      } else if (maxValue <= 100) {\r\n        maxValue = Math.ceil(maxValue / 2) * 2; // 2 unit increments: 22, 24, 26, etc.\r\n      } else if (maxValue <= 500) {\r\n        maxValue = Math.ceil(maxValue / 5) * 5; // 5 unit increments: 105, 110, 115, etc.\r\n      } else {\r\n        maxValue = Math.ceil(maxValue / 10) * 10; // 10 unit increments για μεγάλες τιμές\r\n      }\r\n\r\n      // Εξασφαλίζουμε ότι έχουμε τουλάχιστον κάποια τιμή\r\n      if (maxValue === 0 || maxValue < 0.1) {\r\n        maxValue = 1; // Minimum 1 kW για καλύτερη εμφάνιση\r\n      }\r\n\r\n      // Debug logging (προσωρινό)\r\n      //console.log(`Station ${station.id}: maxDataValue=${maxDataValue}, maxValue=${maxValue}`);\r\n\r\n      // Update Y-axis configuration - ακριβώς 3 τιμές\r\n      const midValue = maxValue / 2;\r\n\r\n      baseOptions.scales.y.min = 0;\r\n      baseOptions.scales.y.max = maxValue;\r\n\r\n      // Καθορίζουμε ακριβώς τις τιμές που θέλουμε\r\n      baseOptions.scales.y.ticks = {\r\n        stepSize: midValue, // Βήμα ίσο με τη μέση τιμή\r\n        maxTicksLimit: 3,   // Ακριβώς 3 ticks\r\n        callback: function(value: any) {\r\n          // Εμφανίζουμε μόνο τις 3 συγκεκριμένες τιμές\r\n          if (value === 0) return '0';\r\n          if (value === midValue) return Math.round(midValue).toString();\r\n          if (value === maxValue) return Math.round(maxValue).toString();\r\n          return ''; // Κρύβουμε όλες τις άλλες τιμές\r\n        },\r\n        color: '#374151',\r\n        font: {\r\n          size: 12,\r\n          weight: 'bold'\r\n        }\r\n      };\r\n\r\n      // X-axis είναι τώρα στατικός με linear scale - δεν χρειάζεται override\r\n\r\n      // Debug logging\r\n     // console.log(\"Chart options for station\", station.id, baseOptions);\r\n     // console.log(\"Tooltip config:\", baseOptions.plugins?.tooltip);\r\n\r\n      // Επιστρέφουμε τα options χωρίς caching\r\n      return baseOptions;\r\n    }\r\n\r\n    // Μετατροπή δεδομένων για στατικό άξονα X (0-24 ώρες)\r\n    private convertDataForStaticXAxis(datasets: any[], dateTimes: string[]): any[] {\r\n        return datasets.map(dataset => {\r\n            const convertedData: {x: number, y: number}[] = [];\r\n\r\n            dataset.data.forEach((value: number, index: number) => {\r\n                if (index < dateTimes.length) {\r\n                    const dateTime = new Date(dateTimes[index]);\r\n                    const hours = dateTime.getHours() + (dateTime.getMinutes() / 60);\r\n                    convertedData.push({x: hours, y: value});\r\n                }\r\n            });\r\n\r\n            return {\r\n                ...dataset,\r\n                data: convertedData\r\n            };\r\n        });\r\n    }\r\n\r\n    // Helper μέθοδος για μορφοποίηση αριθμών με κόμμα\r\n    formatNumber(value: number, decimals: number = 2): string {\r\n        return value.toFixed(decimals).replace('.', ',');\r\n    }\r\n\r\n    // Configuration helper methods with error handling\r\n    getDisplayName(stationId: string, originalName: string): string {\r\n        try {\r\n            if (!stationId) return originalName || 'N/A';\r\n            const config = this.stationConfigurations.get(stationId);\r\n            return config?.customName || config?.name || originalName || 'N/A';\r\n        } catch (error) {\r\n            console.warn(`Error getting display name for station ${stationId}:`, error);\r\n            return originalName || 'N/A';\r\n        }\r\n    }\r\n\r\n    getOwner(stationId: string): string {\r\n        try {\r\n            if (!stationId) return 'N/A';\r\n            const config = this.stationConfigurations.get(stationId);\r\n            return config?.owner || 'N/A';\r\n        } catch (error) {\r\n            console.warn(`Error getting owner for station ${stationId}:`, error);\r\n            return 'N/A';\r\n        }\r\n    }\r\n\r\n    getPortfolio(stationId: string): string {\r\n        try {\r\n            if (!stationId) return 'N/A';\r\n            const config = this.stationConfigurations.get(stationId);\r\n            return config?.portfolio || 'N/A';\r\n        } catch (error) {\r\n            console.warn(`Error getting portfolio for station ${stationId}:`, error);\r\n            return 'N/A';\r\n        }\r\n    }\r\n\r\n    getCountry(stationId: string): string {\r\n        try {\r\n            if (!stationId) return 'N/A';\r\n            const config = this.stationConfigurations.get(stationId);\r\n            return config?.country || 'N/A';\r\n        } catch (error) {\r\n            console.warn(`Error getting country for station ${stationId}:`, error);\r\n            return 'N/A';\r\n        }\r\n    }\r\n\r\n    getPrefecture(stationId: string): string {\r\n        try {\r\n            if (!stationId) return 'N/A';\r\n            const config = this.stationConfigurations.get(stationId);\r\n            return config?.prefecture || 'N/A';\r\n        } catch (error) {\r\n            console.warn(`Error getting prefecture for station ${stationId}:`, error);\r\n            return 'N/A';\r\n        }\r\n    }\r\n\r\n    getInstalledCapacity(stationId: string): number {\r\n        try {\r\n            if (!stationId) return 0;\r\n            const config = this.stationConfigurations.get(stationId);\r\n            return config?.installedCapacityKW || 0;\r\n        } catch (error) {\r\n            console.warn(`Error getting installed capacity for station ${stationId}:`, error);\r\n            return 0;\r\n        }\r\n    }\r\n\r\n    getDataProvider(stationId: string, originalProvider: string): string {\r\n        try {\r\n            if (!stationId) return originalProvider || 'N/A';\r\n            const config = this.stationConfigurations.get(stationId);\r\n            return config?.dataProvider || originalProvider || 'N/A';\r\n        } catch (error) {\r\n            console.warn(`Error getting data provider for station ${stationId}:`, error);\r\n            return originalProvider || 'N/A';\r\n        }\r\n    }\r\n\r\n    onSortChange(event: any) {\r\n        const value = event.value;\r\n\r\n        if (value.indexOf('!') === 0) {\r\n            this.sortOrder = -1;\r\n            this.sortField = value.substring(1, value.length);\r\n        } else {\r\n            this.sortOrder = 1;\r\n            this.sortField = value;\r\n        }\r\n    }\r\n\r\n    onFilter(dv: DataView, event: Event) {\r\n        dv.filter((event.target as HTMLInputElement).value);\r\n    }\r\n\r\n    // Initialize filter options based on available stations\r\n    initializeFilterOptions() {\r\n        // Get unique providers\r\n        const providers = [...new Set(this.stations.map(station => station.provider))];\r\n        this.sortOptionsCountry = providers.map(provider => ({\r\n            label: provider,\r\n            value: provider\r\n        }));\r\n\r\n        // Get unique statuses\r\n        const statuses = [...new Set(this.stations.map(station => station.status))];\r\n        this.sortOptionsStatus = statuses.map(status => ({\r\n            label: status,\r\n            value: status\r\n        }));\r\n    }\r\n\r\n    // Apply all filters and update pagination\r\n    applyFilters() {\r\n        let filtered = [...this.stations];\r\n\r\n        // Apply search filter\r\n        if (this.searchTerm) {\r\n            const searchLower = this.searchTerm.toLowerCase();\r\n            filtered = filtered.filter(station =>\r\n                station.name.toLowerCase().includes(searchLower) ||\r\n                station.provider.toLowerCase().includes(searchLower) ||\r\n                (station.location && station.location.toLowerCase().includes(searchLower))\r\n            );\r\n        }\r\n\r\n        // Apply provider filter\r\n        if (this.selectedProvider) {\r\n            filtered = filtered.filter(station => station.provider === this.selectedProvider);\r\n        }\r\n\r\n        // Apply status filter\r\n        if (this.selectedStatus) {\r\n            filtered = filtered.filter(station => station.status === this.selectedStatus);\r\n        }\r\n\r\n        // Sort stations alphabetically by name\r\n        filtered = filtered.sort((a, b) => {\r\n            const nameA = a.name?.toLowerCase() || '';\r\n            const nameB = b.name?.toLowerCase() || '';\r\n            return nameA.localeCompare(nameB);\r\n        });\r\n\r\n        this.filteredStations = filtered;\r\n        this.totalItems = filtered.length;\r\n        this.currentPage = 0; // Reset to first page\r\n\r\n        // Update pagination options when totalItems changes\r\n        this.updatePaginationOptions();\r\n        this.updatePagination();\r\n    }\r\n\r\n    // Update pagination options when totalItems changes\r\n    private updatePaginationOptions() {\r\n        this.paginationOptions = [20, 40, 80, 100];\r\n        if (this.totalItems > 100) {\r\n            this.paginationOptions.push(this.totalItems);\r\n        }\r\n    }\r\n\r\n    // Update pagination based on current page\r\n    updatePagination() {\r\n        const startIndex = this.currentPage * this.itemsPerPage;\r\n        const endIndex = startIndex + this.itemsPerPage;\r\n        this.paginatedStations = this.filteredStations.slice(startIndex, endIndex);\r\n    }\r\n\r\n    // Handle search input change\r\n    onSearchChange(event: Event) {\r\n        this.searchTerm = (event.target as HTMLInputElement).value;\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Handle provider filter change\r\n    onProviderChange(event: any) {\r\n        this.selectedProvider = event.value || '';\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Handle status filter change\r\n    onStatusChange(event: any) {\r\n        this.selectedStatus = event.value || '';\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Handle pagination change\r\n    onPageChange(event: any) {\r\n        this.currentPage = event.page;\r\n        this.itemsPerPage = event.rows;\r\n        this.updatePagination();\r\n    }\r\n\r\n    // Refresh data\r\n    refreshData() {\r\n        // Καθαρίζουμε όλα τα caches\r\n        this.clearAllCaches();\r\n        this.getUserProviders();\r\n    }\r\n\r\n    // Clear όλα τα caches\r\n    private clearAllCaches() {\r\n        // Δεν χρειάζεται να καθαρίσουμε chart options cache αφού δεν το χρησιμοποιούμε πια\r\n        this.currentPowerCache.clear();\r\n        this.lastUpdateCache.clear();\r\n        this.invertersCache.clear();\r\n        this.deviceConfigurationsCache.clear();\r\n    }\r\n\r\n    // Clear all filters\r\n    clearFilters() {\r\n        this.searchTerm = '';\r\n        this.selectedProvider = '';\r\n        this.selectedStatus = '';\r\n        this.applyFilters();\r\n    }\r\n\r\n    // Check if station has equipment data\r\n    hasEquipmentData(station: Station): boolean {\r\n        const inverters = this.getStationsInverters(station.id || '');\r\n        return inverters > 0 ||\r\n               (station.mmpt && station.mmpt > 0) ||\r\n               (station.string && station.string > 0) ||\r\n               (station.pvn && station.pvn > 0);\r\n    }\r\n\r\n    private filterDataByCurrentTime(data: any[]): any[] {\r\n        const now = new Date();\r\n        const currentTime = now.getTime();\r\n\r\n        return data.filter(item => {\r\n            const itemDateTime = new Date(item.dateTime);\r\n            const itemTime = itemDateTime.getTime();\r\n\r\n            // Only include data points that are not in the future\r\n            return itemTime <= currentTime;\r\n        });\r\n    }\r\n\r\n\r\n    initMap(){\r\n        this.mapSrc = \"https://www.digitaltrends.com/wp-content/uploads/2020/02/custom-map.jpg?fit=720%2C479&p=1\"\r\n    }\r\n\r\n    // initChart() {\r\n    //     const documentStyle = getComputedStyle(document.documentElement);\r\n    //     const textColor = documentStyle.getPropertyValue('--text-color');\r\n    //     const textColorSecondary = documentStyle.getPropertyValue('--text-color-secondary');\r\n    //     const surfaceBorder = documentStyle.getPropertyValue('--surface-border');\r\n\r\n    //     this.chartData = {\r\n    //         labels: ['January', 'February', 'March', 'April', 'May', 'June', 'July'],\r\n    //         datasets: [\r\n    //             {\r\n    //                 label: 'First Dataset',\r\n    //                 data: [65, 59, 80, 81, 56, 55, 40],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--bluegray-700'),\r\n    //                 tension: .4\r\n    //             },\r\n    //             {\r\n    //                 label: 'Second Dataset',\r\n    //                 data: [28, 48, 40, 19, 86, 27, 90],\r\n    //                 fill: false,\r\n    //                 backgroundColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 borderColor: documentStyle.getPropertyValue('--green-600'),\r\n    //                 tension: .4\r\n    //             }\r\n    //         ]\r\n    //     };\r\n\r\n    //     this.chartOptions = {\r\n    //         plugins: {\r\n    //             legend: {\r\n    //                 labels: {\r\n    //                     color: textColor\r\n    //                 }\r\n    //             }\r\n    //         },\r\n    //         scales: {\r\n    //             x: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             },\r\n    //             y: {\r\n    //                 ticks: {\r\n    //                     color: textColorSecondary\r\n    //                 },\r\n    //                 grid: {\r\n    //                     color: surfaceBorder,\r\n    //                     drawBorder: false\r\n    //                 }\r\n    //             }\r\n    //         }\r\n    //     };\r\n    // }\r\n\r\n    // TrackBy function για βελτίωση απόδοσης του *ngFor\r\n    trackByStationId(index: number, station: Station): string {\r\n        return station.id || index.toString();\r\n    }\r\n\r\n    // Get communication status class for text and circle color\r\n    getCommunicationStatusClass(stationId: string): string {\r\n        const now = new Date();\r\n        const currentHour = now.getHours();\r\n\r\n        // Γκρι σκούρο από 22:00-06:00\r\n        if (currentHour >= 22 || currentHour < 6) {\r\n            return 'comm-status-gray';\r\n        }\r\n\r\n        const lastUpdateStr = this.getStationLastUpdate(stationId);\r\n        if (lastUpdateStr === '-') {\r\n            return 'comm-status-red'; // Red if no data\r\n        }\r\n\r\n        try {\r\n            // Parse the last update time (format: \"DD/MM/YYYY, HH:mm\")\r\n            const [datePart, timePart] = lastUpdateStr.split(', ');\r\n            const [day, month, year] = datePart.split('/').map(Number);\r\n            const [hours, minutes] = timePart.split(':').map(Number);\r\n\r\n            const lastUpdate = new Date(year, month - 1, day, hours, minutes);\r\n            const hoursDiff = (now.getTime() - lastUpdate.getTime()) / (1000 * 60 * 60);\r\n\r\n            console.log(`Station ${stationId}: Last update: ${lastUpdateStr}, Hours diff: ${hoursDiff.toFixed(2)}`);\r\n\r\n            if (hoursDiff <= 1) {\r\n                return 'comm-status-green'; // Green - less than 1 hour\r\n            } else if (hoursDiff <= 2) {\r\n                return 'comm-status-orange'; // Orange - 1-2 hours\r\n            } else {\r\n                return 'comm-status-red'; // Red - more than 2 hours\r\n            }\r\n        } catch (error) {\r\n            console.error(`Error parsing time for station ${stationId}: ${lastUpdateStr}`, error);\r\n            return 'comm-status-red'; // Red if parsing fails\r\n        }\r\n    }\r\n\r\n    // Tracker methods\r\n    loadStationTrackers(stationId: string): void {\r\n        if (!stationId) return;\r\n\r\n        this.trackerService.getTrackersByStation(stationId).subscribe({\r\n            next: (trackers) => {\r\n                this.trackersCache.set(stationId, trackers);\r\n\r\n                if (trackers && trackers.length > 0) {\r\n                    // Load data for each tracker to check status\r\n                    let completedRequests = 0;\r\n                    let hasErrors = false;\r\n\r\n                    trackers.forEach(tracker => {\r\n                        this.trackerService.getTrackerData(tracker.id).subscribe({\r\n                            next: (data) => {\r\n                                tracker.trackingData = data.trackingData;\r\n                                tracker.windSpeed = data.windSpeed;\r\n                                tracker.hasError = data.hasError;\r\n                                tracker.errorMessage = data.errorMessage;\r\n\r\n                                // Check if this tracker has non-tracking status\r\n                                const hasNonTrackingStatus = data.trackingData?.some(trackingData =>\r\n                                    trackingData.status && !trackingData.status.toLowerCase().includes('tracking')\r\n                                );\r\n\r\n                                if (hasNonTrackingStatus || data.hasError) {\r\n                                    hasErrors = true;\r\n                                }\r\n\r\n                                completedRequests++;\r\n                                if (completedRequests === trackers.length) {\r\n                                    // All requests completed, check time delays as well\r\n                                    const hasTimeDelayIssues = this.checkNextTrackingTimeDelays(trackers);\r\n                                    this.trackerStatusCache.set(stationId, (hasErrors || hasTimeDelayIssues) ? 'has-errors' : 'all-tracking');\r\n                                }\r\n                            },\r\n                            error: (error) => {\r\n                                console.error(`Error loading tracker data for station ${stationId}:`, error);\r\n                                hasErrors = true;\r\n                                completedRequests++;\r\n                                if (completedRequests === trackers.length) {\r\n                                    // Αν υπάρχουν errors, δεν χρειάζεται να ελέγξουμε χρονικές αποκλίσεις\r\n                                    this.trackerStatusCache.set(stationId, 'has-errors');\r\n                                }\r\n                            }\r\n                        });\r\n                    });\r\n                } else {\r\n                    // No trackers for this station\r\n                    this.trackerStatusCache.set(stationId, 'none');\r\n                }\r\n            },\r\n            error: (error) => {\r\n                console.error(`Error loading trackers for station ${stationId}:`, error);\r\n                this.trackerStatusCache.set(stationId, 'none');\r\n            }\r\n        });\r\n    }\r\n\r\n    getTrackerCount(stationId: string): string {\r\n        const trackers = this.trackersCache.get(stationId);\r\n        if (!trackers || trackers.length === 0) {\r\n            return '0';\r\n        }\r\n\r\n        // Μετράμε το συνολικό πλήθος των TrackingData από όλους τους trackers\r\n        let totalTrackingData = 0;\r\n        trackers.forEach(tracker => {\r\n            if (tracker.trackingData && tracker.trackingData.length > 0) {\r\n                totalTrackingData += tracker.trackingData.length;\r\n            }\r\n        });\r\n\r\n        return totalTrackingData.toString();\r\n    }\r\n\r\n    // Έλεγχος χρονικής απόκλισης στο Next Tracking\r\n    private checkNextTrackingTimeDelays(trackers: TrackerResponse[]): boolean {\r\n        if (!trackers || trackers.length === 0) {\r\n            return false;\r\n        }\r\n\r\n        // Συλλέγουμε όλα τα Next Tracking times από όλους τους trackers\r\n        const allNextTrackingTimes: Date[] = [];\r\n\r\n        trackers.forEach(tracker => {\r\n            if (tracker.trackingData && tracker.trackingData.length > 0) {\r\n                tracker.trackingData.forEach(data => {\r\n                    if (data.nextTracking) {\r\n                        try {\r\n                            // Μετατρέπουμε το nextTracking string σε Date\r\n                            // Υποθέτουμε format HH:mm:ss (π.χ. \"11:15:00\")\r\n                            const timeParts = data.nextTracking.split(':');\r\n                            if (timeParts.length >= 2) {\r\n                                const today = new Date();\r\n                                const nextTrackingTime = new Date(today.getFullYear(), today.getMonth(), today.getDate(),\r\n                                    parseInt(timeParts[0]), parseInt(timeParts[1]), parseInt(timeParts[2] || '0'));\r\n                                allNextTrackingTimes.push(nextTrackingTime);\r\n                            }\r\n                        } catch (error) {\r\n                            console.error('Error parsing nextTracking time:', data.nextTracking, error);\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n        });\r\n\r\n        if (allNextTrackingTimes.length === 0) {\r\n            return false; // Δεν υπάρχουν δεδομένα για έλεγχο\r\n        }\r\n\r\n        // Βρίσκουμε τον πιο προχωρημένο χρονικά tracker (μέγιστος χρόνος)\r\n        const latestTime = new Date(Math.max(...allNextTrackingTimes.map(time => time.getTime())));\r\n\r\n        // Ελέγχουμε αν κάποιος tracker έχει απόκλιση μεγαλύτερη από 20 λεπτά\r\n        const twentyMinutesInMs = 20 * 60 * 1000; // 20 λεπτά σε milliseconds\r\n\r\n        for (const time of allNextTrackingTimes) {\r\n            const timeDifference = latestTime.getTime() - time.getTime();\r\n            if (timeDifference > twentyMinutesInMs) {\r\n                console.log(`Tracker time delay detected: Latest: ${latestTime.toTimeString()}, Current: ${time.toTimeString()}, Difference: ${Math.round(timeDifference / 60000)} minutes`);\r\n                return true; // Βρέθηκε απόκλιση μεγαλύτερη από 20 λεπτά\r\n            }\r\n        }\r\n\r\n        return false; // Όλοι οι trackers είναι εντός του 20λεπτου ορίου\r\n    }\r\n\r\n    getTrackerStatusClass(stationId: string): string {\r\n        const status = this.trackerStatusCache.get(stationId);\r\n\r\n        switch (status) {\r\n            case 'none':\r\n                return 'comm-status-gray'; // Gray for no trackers\r\n            case 'all-tracking':\r\n                return 'comm-status-green'; // Green for all tracking\r\n            case 'has-errors':\r\n                return 'comm-status-red'; // Red for errors or non-tracking\r\n            default:\r\n                return 'comm-status-gray'; // Default gray while loading\r\n        }\r\n    }\r\n\r\n\r\n\r\n    ngOnDestroy() {\r\n        if (this.subscription) {\r\n            this.subscription.unsubscribe();\r\n        }\r\n    }\r\n}\r\n", "<div class=\"grid\">\r\n    <!-- Summary Cards -->\r\n    <div class=\"col-12 md:col-6 lg:col-6\">\r\n        <p-card class=\"h-full\">\r\n            <ng-template pTemplate=\"content\">\r\n                <div class=\"flex justify-content-between align-items-center\">\r\n                    <div class=\"flex-1\">\r\n                        <div class=\"flex align-items-center gap-2 mb-3\">\r\n                            <fa-icon icon=\"solar-panel\" class=\"text-primary text-lg\"></fa-icon>\r\n                            <span class=\"text-600 font-medium\">Active Energy Systems</span>\r\n                        </div>\r\n                        <div class=\"text-900 font-bold text-3xl\">{{stations.length}}</div>\r\n                        <p-tag value=\"Online\" severity=\"success\" class=\"mt-2\"></p-tag>\r\n                    </div>\r\n                    <div class=\"flex align-items-center justify-content-center bg-primary-50 border-round-lg\"\r\n                         style=\"width: 4rem; height: 4rem;\">\r\n                        <fa-icon icon=\"solar-panel\" class=\"text-primary text-2xl\"></fa-icon>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n\r\n    <div class=\"col-12 md:col-6 lg:col-6\">\r\n        <p-card class=\"h-full\">\r\n            <ng-template pTemplate=\"content\">\r\n                <div class=\"flex justify-content-between align-items-center\">\r\n                    <div class=\"flex-1\">\r\n                        <div class=\"flex align-items-center gap-2 mb-3\">\r\n                            <fa-icon icon=\"bolt\" class=\"text-orange-500 text-lg\"></fa-icon>\r\n                            <span class=\"text-600 font-medium\">Combined Power Performance</span>\r\n                        </div>\r\n                        <div class=\"text-900 font-bold text-3xl\">33% (4.124kW)</div>\r\n                        <p-tag value=\"Optimal\" severity=\"warning\" class=\"mt-2\"></p-tag>\r\n                    </div>\r\n                    <div class=\"flex align-items-center justify-content-center bg-orange-50 border-round-lg\"\r\n                         style=\"width: 4rem; height: 4rem;\">\r\n                        <fa-icon icon=\"bolt\" class=\"text-orange-500 text-2xl\"></fa-icon>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n\t \r\n\r\n    <!-- Stations List -->\r\n    <div class=\"col-12\">\r\n        <p-card>\r\n            <ng-template pTemplate=\"header\">\r\n                <div class=\"flex align-items-center justify-content-between p-3\">\r\n                    <div class=\"flex align-items-center gap-2\">\r\n                        <fa-icon icon=\"list\" class=\"text-primary text-xl\"></fa-icon>\r\n                        <h2 class=\"text-2xl font-semibold m-0\">Energy Stations</h2>\r\n                        <p-badge *ngIf=\"totalItems > 0\" [value]=\"totalItems\" severity=\"info\"></p-badge>\r\n                    </div>\r\n                    <div class=\"flex gap-2\">\r\n                        <p-button icon=\"pi pi-filter-slash\"\r\n                                  severity=\"secondary\"\r\n                                  size=\"small\"\r\n                                  pTooltip=\"Clear all filters\"\r\n                                  (click)=\"clearFilters()\">\r\n                        </p-button>\r\n                        <p-button icon=\"pi pi-refresh\"\r\n                                  severity=\"info\"\r\n                                  size=\"small\"\r\n                                  [loading]=\"isRefreshing\"\r\n                                  pTooltip=\"Refresh data\"\r\n                                  (click)=\"refreshData()\">\r\n                        </p-button>\r\n                    </div>\r\n                </div>\r\n            </ng-template>\r\n\r\n            <ng-template pTemplate=\"content\">\r\n                <!-- Filters -->\r\n                <div class=\"flex flex-column md:flex-row md:justify-content-between gap-3 mb-4\">\r\n                    <div class=\"flex gap-2\">\r\n                        <p-dropdown [options]=\"sortOptionsCountry\"\r\n                                   placeholder=\"Filter by Provider\"\r\n                                   [(ngModel)]=\"selectedProvider\"\r\n                                   (onChange)=\"onProviderChange($event)\"\r\n                                   [showClear]=\"true\"\r\n                                   class=\"w-full md:w-auto\">\r\n                        </p-dropdown>\r\n                        <p-dropdown [options]=\"sortOptionsStatus\"\r\n                                   placeholder=\"Filter by Status\"\r\n                                   [(ngModel)]=\"selectedStatus\"\r\n                                   (onChange)=\"onStatusChange($event)\"\r\n                                   [showClear]=\"true\"\r\n                                   class=\"w-full md:w-auto\">\r\n                        </p-dropdown>\r\n                    </div>\r\n                    <span class=\"p-input-icon-left\">\r\n                        <i class=\"pi pi-search\"></i>\r\n                        <input type=\"search\"\r\n                               pInputText\r\n                               [(ngModel)]=\"searchTerm\"\r\n                               (input)=\"onSearchChange($event)\"\r\n                               placeholder=\"Search stations...\"\r\n                               class=\"w-full md:w-auto\">\r\n                    </span>\r\n                </div>\r\n\r\n                <!-- Stations Grid -->\r\n                <div class=\"grid\">\r\n                    <div *ngIf=\"paginatedStations.length === 0 && !isRefreshing\" class=\"col-12\">\r\n                        <div class=\"text-center py-6\">\r\n                            <fa-icon icon=\"inbox\" class=\"text-6xl text-300 mb-3\"></fa-icon>\r\n                            <p class=\"text-lg text-600\">No stations found matching your criteria.</p>\r\n                            <p-button label=\"Clear Filters\"\r\n                                      icon=\"pi pi-filter-slash\"\r\n                                      severity=\"secondary\"\r\n                                      size=\"small\"\r\n                                      (click)=\"clearFilters()\">\r\n                            </p-button>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div *ngFor=\"let station of paginatedStations; trackBy: trackByStationId\" class=\"col-12\">\r\n                        <p-card class=\"mb-3\"\r\n                                [ngClass]=\"'station-status-' + station.status?.toLowerCase()\"\r\n                                pRipple>\r\n                            <!-- <ng-template pTemplate=\"header\">\r\n                                <div class=\"bg-primary-50 p-3 border-round-top\">\r\n                                    <div class=\"flex align-items-center justify-content-between\">\r\n                                        <div class=\"flex align-items-center gap-2\">\r\n                                            <p-tag [value]=\"station.provider\"\r\n                                                   severity=\"info\"\r\n                                                   icon=\"pi pi-server\">\r\n                                            </p-tag>\r\n                                            <p-tag [value]=\"station.status?.toLowerCase() || 'unknown'\"\r\n                                                   [severity]=\"station.status?.toLowerCase() === 'online' ? 'success' : 'warning'\">\r\n                                            </p-tag>\r\n                                        </div>\r\n                                        <div class=\"flex align-items-center gap-2 text-600\">\r\n                                            <fa-icon icon=\"clock\" class=\"text-sm\"></fa-icon>\r\n                                            <span class=\"text-sm\">{{getStationLastUpdate(station.id)}}</span>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </ng-template> -->\r\n\r\n                            <ng-template pTemplate=\"content\">\r\n                                <div class=\"station-layout p-3\">\r\n                                    <!-- Main Layout Container -->\r\n                                    <div class=\"flex align-items-center justify-content-between w-full\">\r\n\r\n                                        <!-- Left Section: Station Name & Calculated Power -->\r\n                                        <div class=\"station-info-left flex flex-column\" style=\"min-width: 180px;\">\r\n                                            <h3 class=\"text-xl font-bold m-0 mb-3\">\r\n                                                <a [routerLink]=\"['/app/station',station.id]\"\r\n                                                   class=\"text-primary no-underline hover:underline\">\r\n                                                    {{getDisplayName(station.id, station.name)}}\r\n                                                </a>\r\n                                            </h3>\r\n                                            <div class=\"power-info-card p-2 border-round\">\r\n                                                <div class=\"text-lg font-bold flex align-items-center gap-1\">\r\n                                                    <fa-icon icon=\"solar-panel\" class=\"text-primary\"></fa-icon>\r\n                                                    {{formatNumber(getInstalledCapacity(station.id) || getStationsSumData(station.id) || 0, 1)}}kW\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <!-- Center Section: Info grid with full-height chart -->\r\n                                        <div class=\"station-chart-section flex-1 mx-4\">\r\n                                            <div class=\"flex align-items-stretch\" style=\"min-height: 120px;\">\r\n                                                <!-- Left Info Column -->\r\n                                                <div class=\"flex flex-column justify-content-between\" style=\"flex: 1; padding-right: 1rem;\">\r\n                                                    <div class=\"text-center mb-2\">\r\n                                                        <div class=\"flex align-items-center justify-content-center gap-1\">\r\n                                                            <div class=\"communication-status-circle\" [ngClass]=\"getCommunicationStatusClass(station.id)\"></div>\r\n                                                            <span class=\"text-xs font-medium\" [ngClass]=\"getCommunicationStatusClass(station.id)\">Communication</span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div class=\"text-center\">\r\n                                                        <div class=\"text-xs text-600\">Inverters</div>\r\n                                                        <div class=\"text-sm font-semibold\">{{station.invertersCount || 0}}</div>\r\n                                                    </div>\r\n                                                    <div class=\"text-center mt-2\">\r\n                                                        <div class=\"text-xs text-500 flex align-items-center justify-content-center gap-1\">\r\n                                                            <div class=\"communication-status-circle\" [ngClass]=\"getTrackerStatusClass(station.id)\"></div>\r\n                                                            <span class=\"font-medium\" [ngClass]=\"getTrackerStatusClass(station.id)\">Trackers</span>\r\n                                                        </div>\r\n                                                        <div class=\"text-sm font-semibold\">{{getTrackerCount(station.id)}}</div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <!-- Weather & Current Power Column -->\r\n                                                <div class=\"flex flex-column justify-content-between\" style=\"flex: 1; padding-right: 1rem;\">\r\n                                                    <div class=\"text-center mb-2\">\r\n                                                        <div class=\"flex align-items-center justify-content-center gap-1\">\r\n                                                            <i class=\"pi pi-cloud text-blue-500\"></i>\r\n                                                            <span class=\"text-sm font-semibold\">{{station.temperature || 0}}°C</span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                    <div class=\"current-power-card p-2 border-round text-center\">\r\n                                                        <div class=\"text-sm font-bold flex align-items-center justify-content-center gap-1 my-1\">\r\n                                                            <i class=\"pi pi-flash text-green-500\"></i>\r\n                                                            {{formatNumber(getCurrentPower(station.id), 1)}}kW\r\n                                                        </div>\r\n                                                        <div class=\"text-xs text-500 flex align-items-center justify-content-center gap-1\">\r\n                                                            <fa-icon icon=\"clock\" class=\"text-xs\"></fa-icon>\r\n                                                            <span>{{getStationLastUpdate(station.id)}}</span>\r\n                                                        </div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <!-- Full Height Chart -->\r\n                                                <div class=\"text-center chart-container-full\" style=\"flex: 2; margin: 0 1rem;\">\r\n                                                    <ng-container *ngIf=\"getStationsRealTimeData(station) as chartData; else noChartData\">\r\n                                                        <c-chart [data]=\"chartData\"\r\n                                                                 [options]=\"getChartOptionsForStation(station)\"\r\n                                                                 type=\"line\"\r\n                                                                 class=\"mx-auto chart-section\"\r\n                                                                 height=\"140\"\r\n                                                                 width=\"300\" />\r\n                                                    </ng-container>\r\n                                                    <ng-template #noChartData>\r\n                                                        <div class=\"text-center p-3 h-full flex align-items-center justify-content-center\">\r\n                                                            <div>\r\n                                                                <fa-icon icon=\"chart-line\" class=\"text-300 text-3xl mb-2\"></fa-icon>\r\n                                                                <p class=\"text-sm text-600 m-0\">No chart data</p>\r\n                                                            </div>\r\n                                                        </div>\r\n                                                    </ng-template>\r\n                                                </div>\r\n\r\n                                                <!-- Portfolio & Owner Column -->\r\n                                                <div class=\"flex flex-column justify-content-between\" style=\"flex: 1; padding-left: 1rem;\">\r\n                                                    <div class=\"text-center mb-2\">\r\n                                                        <div class=\"text-xs text-600\">Portfolio</div>\r\n                                                        <div class=\"text-sm font-semibold\">{{getPortfolio(station.id)}}</div>\r\n                                                    </div>\r\n                                                    <div class=\"text-center mt-2\">\r\n                                                        <div class=\"text-xs text-600\">Owner</div>\r\n                                                        <div class=\"text-sm font-semibold\">{{getOwner(station.id)}}</div>\r\n                                                    </div>\r\n                                                </div>\r\n\r\n                                                <!-- Country & Prefecture Column -->\r\n                                                <div class=\"flex flex-column justify-content-between\" style=\"flex: 1; padding-left: 1rem;\">\r\n                                                    <div class=\"text-center mb-2\">\r\n                                                        <div class=\"text-xs text-600\">Country</div>\r\n                                                        <div class=\"text-sm font-semibold\">{{getCountry(station.id)}}</div>\r\n                                                    </div>\r\n                                                    <div class=\"text-center mt-2\">\r\n                                                        <div class=\"text-xs text-600\">Prefecture</div>\r\n                                                        <div class=\"text-sm font-semibold\">{{getPrefecture(station.id)}}</div>\r\n                                                        \r\n                                                    </div>\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n\r\n                                        <!-- Right Section: Provider -->\r\n                                        <div class=\"station-info-right text-center\" style=\"min-width: 120px;\">\r\n                                            <div class=\"provider-card p-3 border-round\" style=\"background: linear-gradient(135deg, rgba(25, 28, 83, 0.1) 0%, rgba(25, 28, 83, 0.2) 100%);\">\r\n                                                <!-- <div class=\"text-xs text-600 mb-2 font-medium\">Provider</div> -->\r\n                                                <div class=\"text-lg font-bold flex align-items-center justify-content-center gap-2\" style=\"color: #191C53;\">\r\n                                                    <i class=\"pi pi-server\" style=\"color: #191C53;\"></i>\r\n                                                    {{getDataProvider(station.id, station.provider || 'Aurora')}}\r\n                                                </div>\r\n                                            </div>\r\n                                        </div>\r\n                                    </div>\r\n                                </div>\r\n                            </ng-template>\r\n                        </p-card>\r\n                    </div>\r\n                </div>\r\n\r\n                <!-- Pagination -->\r\n                <div *ngIf=\"totalItems > itemsPerPage\" class=\"mt-4\">\r\n                    <p-paginator [rows]=\"itemsPerPage\"\r\n                                 [totalRecords]=\"totalItems\"\r\n                                 [first]=\"currentPage * itemsPerPage\"\r\n                                 [rowsPerPageOptions]=\"paginationOptions\"\r\n                                 [showCurrentPageReport]=\"true\"\r\n                                 currentPageReportTemplate=\"Showing {first} to {last} of {totalRecords} stations\"\r\n                                 (onPageChange)=\"onPageChange($event)\">\r\n                    </p-paginator>\r\n                </div>\r\n            </ng-template>\r\n        </p-card>\r\n    </div>\r\n</div>"], "mappings": "AAIA,SAAuBA,YAAY,QAAQ,MAAM;AAajD,SAAiBC,aAAa,QAAQ,iBAAiB;AACvD,SAASC,MAAM,QAAQ,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;ICbvBC,EAAA,CAAAC,cAAA,aAA6D;IAGjDD,EAAA,CAAAE,SAAA,iBAAmE;IACnEF,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,4BAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAEnEJ,EAAA,CAAAC,cAAA,cAAyC;IAAAD,EAAA,CAAAG,MAAA,GAAmB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAClEJ,EAAA,CAAAE,SAAA,gBAA8D;IAClEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cACwC;IACpCD,EAAA,CAAAE,SAAA,mBAAoE;IACxEF,EAAA,CAAAI,YAAA,EAAM;;;;IANuCJ,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,iBAAA,CAAAC,MAAA,CAAAC,QAAA,CAAAC,MAAA,CAAmB;;;;;IAepET,EAAA,CAAAC,cAAA,aAA6D;IAGjDD,EAAA,CAAAE,SAAA,kBAA+D;IAC/DF,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,iCAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAExEJ,EAAA,CAAAC,cAAA,cAAyC;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC5DJ,EAAA,CAAAE,SAAA,gBAA+D;IACnEF,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cACwC;IACpCD,EAAA,CAAAE,SAAA,mBAAgE;IACpEF,EAAA,CAAAI,YAAA,EAAM;;;;;IAeFJ,EAAA,CAAAE,SAAA,kBAA+E;;;;IAA/CF,EAAA,CAAAU,UAAA,UAAAC,MAAA,CAAAC,UAAA,CAAoB;;;;;;IAJ5DZ,EAAA,CAAAC,cAAA,cAAiE;IAEzDD,EAAA,CAAAE,SAAA,kBAA4D;IAC5DF,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAG,MAAA,sBAAe;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3DJ,EAAA,CAAAa,UAAA,IAAAC,+CAAA,sBAA+E;IACnFd,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,cAAwB;IAKVD,EAAA,CAAAe,UAAA,mBAAAC,gEAAA;MAAAhB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAnB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IAClCtB,EAAA,CAAAI,YAAA,EAAW;IACXJ,EAAA,CAAAC,cAAA,mBAKkC;IAAxBD,EAAA,CAAAe,UAAA,mBAAAQ,gEAAA;MAAAvB,EAAA,CAAAiB,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAxB,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAG,MAAA,CAAAC,WAAA,EAAa;IAAA,EAAC;IACjCzB,EAAA,CAAAI,YAAA,EAAW;;;;IAfDJ,EAAA,CAAAK,SAAA,GAAoB;IAApBL,EAAA,CAAAU,UAAA,SAAAgB,MAAA,CAAAd,UAAA,KAAoB;IAYpBZ,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAU,UAAA,YAAAgB,MAAA,CAAAC,YAAA,CAAwB;;;;;;IAwCtC3B,EAAA,CAAAC,cAAA,aAA4E;IAEpED,EAAA,CAAAE,SAAA,kBAA+D;IAC/DF,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAG,MAAA,gDAAyC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACzEJ,EAAA,CAAAC,cAAA,mBAImC;IAAzBD,EAAA,CAAAe,UAAA,mBAAAa,uEAAA;MAAA5B,EAAA,CAAAiB,aAAA,CAAAY,IAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAoB,aAAA;MAAA,OAASpB,EAAA,CAAAqB,WAAA,CAAAS,OAAA,CAAAR,YAAA,EAAc;IAAA,EAAC;IAClCtB,EAAA,CAAAI,YAAA,EAAW;;;;;IA+FaJ,EAAA,CAAA+B,uBAAA,GAAsF;IAClF/B,EAAA,CAAAE,SAAA,kBAKuB;IAC3BF,EAAA,CAAAgC,qBAAA,EAAe;;;;;;IANFhC,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAU,UAAA,SAAAuB,aAAA,CAAkB,YAAAC,OAAA,CAAAC,yBAAA,CAAAC,WAAA;;;;;IAQ3BpC,EAAA,CAAAC,cAAA,cAAmF;IAE3ED,EAAA,CAAAE,SAAA,kBAAoE;IACpEF,EAAA,CAAAC,cAAA,YAAgC;IAAAD,EAAA,CAAAG,MAAA,oBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAI;;;;;;IA9EjFJ,EAAA,CAAAC,cAAA,cAAgC;IASZD,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAERJ,EAAA,CAAAC,cAAA,cAA8C;IAEtCD,EAAA,CAAAE,SAAA,kBAA2D;IAC3DF,EAAA,CAAAG,MAAA,GACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAKdJ,EAAA,CAAAC,cAAA,eAA+C;IAM3BD,EAAA,CAAAE,SAAA,eAAmG;IACnGF,EAAA,CAAAC,cAAA,gBAAsF;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGlHJ,EAAA,CAAAC,cAAA,eAAyB;IACSD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC7CJ,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,IAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAE5EJ,EAAA,CAAAC,cAAA,eAA8B;IAEtBD,EAAA,CAAAE,SAAA,eAA6F;IAC7FF,EAAA,CAAAC,cAAA,gBAAwE;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAE3FJ,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,IAA+B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAKhFJ,EAAA,CAAAC,cAAA,eAA4F;IAGhFD,EAAA,CAAAE,SAAA,aAAyC;IACzCF,EAAA,CAAAC,cAAA,gBAAoC;IAAAD,EAAA,CAAAG,MAAA,IAA8B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAGjFJ,EAAA,CAAAC,cAAA,eAA6D;IAErDD,EAAA,CAAAE,SAAA,aAA0C;IAC1CF,EAAA,CAAAG,MAAA,IACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACNJ,EAAA,CAAAC,cAAA,eAAmF;IAC/ED,EAAA,CAAAE,SAAA,mBAAgD;IAChDF,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAoC;IAAAH,EAAA,CAAAI,YAAA,EAAO;IAM7DJ,EAAA,CAAAC,cAAA,eAA+E;IAC3ED,EAAA,CAAAa,UAAA,KAAAwB,0EAAA,2BAOe,KAAAC,yEAAA,iCAAAtC,EAAA,CAAAuC,sBAAA;IASnBvC,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAC,cAAA,eAA2F;IAErDD,EAAA,CAAAG,MAAA,iBAAS;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC7CJ,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,IAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEzEJ,EAAA,CAAAC,cAAA,eAA8B;IACID,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAM;IACzCJ,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAKzEJ,EAAA,CAAAC,cAAA,eAA2F;IAErDD,EAAA,CAAAG,MAAA,eAAO;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC3CJ,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,IAA0B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAEvEJ,EAAA,CAAAC,cAAA,eAA8B;IACID,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAC9CJ,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAG,MAAA,IAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAM;IAQtFJ,EAAA,CAAAC,cAAA,eAAsE;IAI1DD,EAAA,CAAAE,SAAA,aAAoD;IACpDF,EAAA,CAAAG,MAAA,IACJ;IAAAH,EAAA,CAAAI,YAAA,EAAM;;;;;;IA/GHJ,EAAA,CAAAK,SAAA,GAA0C;IAA1CL,EAAA,CAAAU,UAAA,eAAAV,EAAA,CAAAwC,eAAA,KAAAC,GAAA,EAAAL,WAAA,CAAAM,EAAA,EAA0C;IAEzC1C,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAA2C,kBAAA,MAAAC,OAAA,CAAAC,cAAA,CAAAT,WAAA,CAAAM,EAAA,EAAAN,WAAA,CAAAU,IAAA,OACJ;IAKI9C,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAA2C,kBAAA,MAAAC,OAAA,CAAAG,YAAA,CAAAH,OAAA,CAAAI,oBAAA,CAAAZ,WAAA,CAAAM,EAAA,KAAAE,OAAA,CAAAK,kBAAA,CAAAb,WAAA,CAAAM,EAAA,kBACJ;IAWqD1C,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAU,UAAA,YAAAkC,OAAA,CAAAM,2BAAA,CAAAd,WAAA,CAAAM,EAAA,EAAmD;IAC1D1C,EAAA,CAAAK,SAAA,GAAmD;IAAnDL,EAAA,CAAAU,UAAA,YAAAkC,OAAA,CAAAM,2BAAA,CAAAd,WAAA,CAAAM,EAAA,EAAmD;IAKtD1C,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAA8B,WAAA,CAAAe,cAAA,MAA+B;IAIrBnD,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAU,UAAA,YAAAkC,OAAA,CAAAQ,qBAAA,CAAAhB,WAAA,CAAAM,EAAA,EAA6C;IAC5D1C,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAU,UAAA,YAAAkC,OAAA,CAAAQ,qBAAA,CAAAhB,WAAA,CAAAM,EAAA,EAA6C;IAExC1C,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAM,iBAAA,CAAAsC,OAAA,CAAAS,eAAA,CAAAjB,WAAA,CAAAM,EAAA,EAA+B;IAS1B1C,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAA2C,kBAAA,KAAAP,WAAA,CAAAkB,WAAA,iBAA8B;IAMlEtD,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAA2C,kBAAA,MAAAC,OAAA,CAAAG,YAAA,CAAAH,OAAA,CAAAW,eAAA,CAAAnB,WAAA,CAAAM,EAAA,aACJ;IAGU1C,EAAA,CAAAK,SAAA,GAAoC;IAApCL,EAAA,CAAAM,iBAAA,CAAAsC,OAAA,CAAAY,oBAAA,CAAApB,WAAA,CAAAM,EAAA,EAAoC;IAOnC1C,EAAA,CAAAK,SAAA,GAAuC;IAAvCL,EAAA,CAAAU,UAAA,SAAAkC,OAAA,CAAAa,uBAAA,CAAArB,WAAA,EAAuC,aAAAsB,IAAA;IAsBf1D,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAM,iBAAA,CAAAsC,OAAA,CAAAe,YAAA,CAAAvB,WAAA,CAAAM,EAAA,EAA4B;IAI5B1C,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,iBAAA,CAAAsC,OAAA,CAAAgB,QAAA,CAAAxB,WAAA,CAAAM,EAAA,EAAwB;IAQxB1C,EAAA,CAAAK,SAAA,GAA0B;IAA1BL,EAAA,CAAAM,iBAAA,CAAAsC,OAAA,CAAAiB,UAAA,CAAAzB,WAAA,CAAAM,EAAA,EAA0B;IAI1B1C,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAM,iBAAA,CAAAsC,OAAA,CAAAkB,aAAA,CAAA1B,WAAA,CAAAM,EAAA,EAA6B;IAapE1C,EAAA,CAAAK,SAAA,GACJ;IADIL,EAAA,CAAA2C,kBAAA,MAAAC,OAAA,CAAAmB,eAAA,CAAA3B,WAAA,CAAAM,EAAA,EAAAN,WAAA,CAAA4B,QAAA,mBACJ;;;;;IA/I5BhE,EAAA,CAAAC,cAAA,aAAyF;IAwBjFD,EAAA,CAAAa,UAAA,IAAAoD,0DAAA,2BA4Hc;IAClBjE,EAAA,CAAAI,YAAA,EAAS;;;;IAnJDJ,EAAA,CAAAK,SAAA,GAA6D;IAA7DL,EAAA,CAAAU,UAAA,iCAAA0B,WAAA,CAAA8B,MAAA,kBAAA9B,WAAA,CAAA8B,MAAA,CAAAC,WAAA,IAA6D;;;;;;IAwJ7EnE,EAAA,CAAAC,cAAA,cAAoD;IAOnCD,EAAA,CAAAe,UAAA,0BAAAqD,kFAAAC,MAAA;MAAArE,EAAA,CAAAiB,aAAA,CAAAqD,IAAA;MAAA,MAAAC,OAAA,GAAAvE,EAAA,CAAAoB,aAAA;MAAA,OAAgBpB,EAAA,CAAAqB,WAAA,CAAAkD,OAAA,CAAAC,YAAA,CAAAH,MAAA,CAAoB;IAAA,EAAC;IAClDrE,EAAA,CAAAI,YAAA,EAAc;;;;IAPDJ,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAU,UAAA,SAAA+D,OAAA,CAAAC,YAAA,CAAqB,iBAAAD,OAAA,CAAA7D,UAAA,WAAA6D,OAAA,CAAAE,WAAA,GAAAF,OAAA,CAAAC,YAAA,wBAAAD,OAAA,CAAAG,iBAAA;;;;;;IAtMtC5E,EAAA,CAAAC,cAAA,cAAgF;IAI7DD,EAAA,CAAAe,UAAA,2BAAA8D,2EAAAR,MAAA;MAAArE,EAAA,CAAAiB,aAAA,CAAA6D,IAAA;MAAA,MAAAC,OAAA,GAAA/E,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAA0D,OAAA,CAAAC,gBAAA,GAAAX,MAAA;IAAA,EAA8B,sBAAAY,sEAAAZ,MAAA;MAAArE,EAAA,CAAAiB,aAAA,CAAA6D,IAAA;MAAA,MAAAI,OAAA,GAAAlF,EAAA,CAAAoB,aAAA;MAAA,OAClBpB,EAAA,CAAAqB,WAAA,CAAA6D,OAAA,CAAAC,gBAAA,CAAAd,MAAA,CAAwB;IAAA,EADN;IAIzCrE,EAAA,CAAAI,YAAA,EAAa;IACbJ,EAAA,CAAAC,cAAA,qBAKoC;IAHzBD,EAAA,CAAAe,UAAA,2BAAAqE,2EAAAf,MAAA;MAAArE,EAAA,CAAAiB,aAAA,CAAA6D,IAAA;MAAA,MAAAO,OAAA,GAAArF,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAAgE,OAAA,CAAAC,cAAA,GAAAjB,MAAA;IAAA,EAA4B,sBAAAkB,sEAAAlB,MAAA;MAAArE,EAAA,CAAAiB,aAAA,CAAA6D,IAAA;MAAA,MAAAU,OAAA,GAAAxF,EAAA,CAAAoB,aAAA;MAAA,OAChBpB,EAAA,CAAAqB,WAAA,CAAAmE,OAAA,CAAAC,cAAA,CAAApB,MAAA,CAAsB;IAAA,EADN;IAIvCrE,EAAA,CAAAI,YAAA,EAAa;IAEjBJ,EAAA,CAAAC,cAAA,eAAgC;IAC5BD,EAAA,CAAAE,SAAA,YAA4B;IAC5BF,EAAA,CAAAC,cAAA,gBAKgC;IAHzBD,EAAA,CAAAe,UAAA,2BAAA2E,sEAAArB,MAAA;MAAArE,EAAA,CAAAiB,aAAA,CAAA6D,IAAA;MAAA,MAAAa,OAAA,GAAA3F,EAAA,CAAAoB,aAAA;MAAA,OAAApB,EAAA,CAAAqB,WAAA,CAAAsE,OAAA,CAAAC,UAAA,GAAAvB,MAAA;IAAA,EAAwB,mBAAAwB,8DAAAxB,MAAA;MAAArE,EAAA,CAAAiB,aAAA,CAAA6D,IAAA;MAAA,MAAAgB,OAAA,GAAA9F,EAAA,CAAAoB,aAAA;MAAA,OACfpB,EAAA,CAAAqB,WAAA,CAAAyE,OAAA,CAAAC,cAAA,CAAA1B,MAAA,CAAsB;IAAA,EADP;IAF/BrE,EAAA,CAAAI,YAAA,EAKgC;IAKxCJ,EAAA,CAAAC,cAAA,aAAkB;IACdD,EAAA,CAAAa,UAAA,IAAAmF,4CAAA,kBAWM,IAAAC,4CAAA;IAyJVjG,EAAA,CAAAI,YAAA,EAAM;IAGNJ,EAAA,CAAAa,UAAA,KAAAqF,6CAAA,kBASM;;;;IA5MclG,EAAA,CAAAK,SAAA,GAA8B;IAA9BL,EAAA,CAAAU,UAAA,YAAAyF,MAAA,CAAAC,kBAAA,CAA8B,YAAAD,MAAA,CAAAnB,gBAAA;IAO9BhF,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAU,UAAA,YAAAyF,MAAA,CAAAE,iBAAA,CAA6B,YAAAF,MAAA,CAAAb,cAAA;IAYlCtF,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAU,UAAA,YAAAyF,MAAA,CAAAP,UAAA,CAAwB;IAS7B5F,EAAA,CAAAK,SAAA,GAAqD;IAArDL,EAAA,CAAAU,UAAA,SAAAyF,MAAA,CAAAG,iBAAA,CAAA7F,MAAA,WAAA0F,MAAA,CAAAxE,YAAA,CAAqD;IAalC3B,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAU,UAAA,YAAAyF,MAAA,CAAAG,iBAAA,CAAsB,iBAAAH,MAAA,CAAAI,gBAAA;IA0J7CvG,EAAA,CAAAK,SAAA,GAA+B;IAA/BL,EAAA,CAAAU,UAAA,SAAAyF,MAAA,CAAAvF,UAAA,GAAAuF,MAAA,CAAAzB,YAAA,CAA+B;;;ADtPrD,OAAM,MAAO8B,cAAc;EAkEvBC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,MAAc,EACdC,YAA0B,EAC1BC,SAA2B,EAC3BC,cAA8B,EAC9BC,oBAAiD,EACjDC,GAAsB;IAVf,KAAAV,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,SAAS,GAATA,SAAS;IACT,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,GAAG,GAAHA,GAAG;IAxEf,KAAA5G,QAAQ,GAAc,EAAE;IACxB,KAAA6G,gBAAgB,GAAc,EAAE;IAChC,KAAAf,iBAAiB,GAAc,EAAE;IAQjC,KAAAF,kBAAkB,GAAiB,EAAE;IAErC,KAAAC,iBAAiB,GAAiB,EAAE;IAEpC,KAAAiB,SAAS,GAAW,CAAC;IAErB,KAAAC,SAAS,GAAW,EAAE;IAEtB;IACA,KAAA3B,UAAU,GAAW,EAAE;IACvB,KAAAZ,gBAAgB,GAAW,EAAE;IAC7B,KAAAM,cAAc,GAAW,EAAE;IAE3B;IACA,KAAAX,WAAW,GAAW,CAAC;IACvB,KAAAD,YAAY,GAAW,EAAE;IACzB,KAAA9D,UAAU,GAAW,CAAC;IAEtB;IACA,KAAAgE,iBAAiB,GAAa,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAE/C;IACA,KAAAjD,YAAY,GAAY,KAAK;IAE7B,KAAA6F,YAAY,GAAU,EAAE;IAExB,KAAAC,YAAY,GAAU,EAAE;IAExB,KAAAC,WAAW,GAAU,EAAE;IAMvB,KAAAC,YAAY,GAAmB,IAAIC,GAAG,EAAE;IACxC,KAAAC,eAAe,GAAmB,IAAID,GAAG,EAAE;IAC3C,KAAAE,eAAe,GAAsB,IAAIF,GAAG,EAAE;IAE9C;IACQ,KAAAG,iBAAiB,GAAwB,IAAIH,GAAG,EAAE;IAClD,KAAAI,eAAe,GAAwB,IAAIJ,GAAG,EAAE;IAChD,KAAAK,cAAc,GAAwB,IAAIL,GAAG,EAAE;IAC/C,KAAAM,yBAAyB,GAAuB,IAAIN,GAAG,EAAE;IAEjE;IACQ,KAAAO,aAAa,GAAmC,IAAIP,GAAG,EAAE;IACzD,KAAAQ,kBAAkB,GAAwD,IAAIR,GAAG,EAAE;IAE3F;IACQ,KAAAS,qBAAqB,GAAG,IAAIT,GAAG,EAAgC;IAcnE,IAAI,CAACU,YAAY,GAAG,IAAI,CAAC5B,aAAa,CAAC6B,aAAa,CACnDC,IAAI,CAAC3I,YAAY,CAAC,EAAE,CAAC,CAAC,CACtB4I,SAAS,CAAEC,MAAM,IAAI;MACrB;IAAA,CACA,CAAC;IAEF,IAAI,CAACC,sBAAsB,EAAE;EACjC;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,wBAAwB,EAAE;EACnC;EAEQA,wBAAwBA,CAAA;IAC5B;IACA,IAAI,CAAC/B,MAAM,CAACgC,MAAM,CAACP,IAAI,CACnBzI,MAAM,CAACiJ,KAAK,IAAIA,KAAK,YAAYlJ,aAAa,CAAC,CAClD,CAAC2I,SAAS,CAAEO,KAAoB,IAAI;MACjC,IAAIA,KAAK,CAACC,GAAG,KAAK,MAAM,IAAID,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;QAC/CC,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;QACjE,IAAI,CAACC,yBAAyB,EAAE;;IAExC,CAAC,CAAC;IAEF;IACA,IAAI,CAACA,yBAAyB,EAAE;EACpC;EAEQA,yBAAyBA,CAAA;IAC7BF,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChD,IAAI,CAAChC,oBAAoB,CAACkC,oBAAoB,EAAE,CAACZ,SAAS,CAAC;MACvDa,IAAI,EAAGC,cAAc,IAAI;QACrBL,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEI,cAAc,CAAC9I,MAAM,CAAC;QAE5D;QACA,IAAI,CAAC4H,qBAAqB,CAACmB,KAAK,EAAE;QAClCD,cAAc,CAACE,OAAO,CAACf,MAAM,IAAG;UAC5B,IAAI,CAACL,qBAAqB,CAACqB,GAAG,CAAChB,MAAM,CAACiB,SAAS,EAAEjB,MAAM,CAAC;QAC5D,CAAC,CAAC;QAEF;QACA,IAAI,CAACtB,GAAG,CAACwC,aAAa,EAAE;MAC5B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACbX,OAAO,CAACW,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACzD;KACH,CAAC;EACN;EAEQlB,sBAAsBA,CAAA;IAC1B,IAAI,CAACmB,UAAU,GAAG;MACdC,mBAAmB,EAAE,KAAK;MAC1BC,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;;OAEZ;MACDC,MAAM,EAAE;QACNC,CAAC,EAAE;UACDF,OAAO,EAAE;SACV;QACDG,CAAC,EAAE;UACDH,OAAO,EAAE;;;KAGd;IAED,IAAI,CAACI,WAAW,GAAG;MACjBC,UAAU,EAAE,IAAI;MAChBR,mBAAmB,EAAE,KAAK;MAC1BS,WAAW,EAAE;QACXC,SAAS,EAAE,KAAK;QAChBC,IAAI,EAAE;OACP;MACDC,QAAQ,EAAE;QACRC,IAAI,EAAE;UACJC,OAAO,EAAE,GAAG;UACZC,WAAW,EAAE;SACd;QACDC,KAAK,EAAE;UACLC,MAAM,EAAE,CAAC;UACTC,WAAW,EAAE,CAAC;UACdC,SAAS,EAAE;;OAEd;MACDlB,OAAO,EAAE;QACPC,MAAM,EAAE;UACNC,OAAO,EAAE;SACV;QACDiB,OAAO,EAAE;UACPC,OAAO,EAAE,IAAI;UACbV,IAAI,EAAE,OAAO;UACbD,SAAS,EAAE,KAAK;UAChBY,QAAQ,EAAE,SAAS;UACnBC,QAAQ,EAAE,IAAI;UACdC,eAAe,EAAE,oBAAoB;UACrCC,UAAU,EAAE,SAAS;UACrBC,SAAS,EAAE,SAAS;UACpBC,WAAW,EAAE,0BAA0B;UACvCZ,WAAW,EAAE,CAAC;UACda,YAAY,EAAE,CAAC;UACfC,aAAa,EAAE,KAAK;UACpBC,OAAO,EAAE,EAAE;UACXC,YAAY,EAAE,CAAC;UACfC,SAAS,EAAE,CAAC;UACZC,SAAS,EAAE;YACTC,IAAI,EAAE,EAAE;YACRC,MAAM,EAAE;WACT;UACDC,QAAQ,EAAE;YACRF,IAAI,EAAE;WACP;UACDlM,MAAM,EAAE,SAAAA,CAASqM,WAAgB;YAC/B;YACA,OAAOA,WAAW,CAACC,MAAM,CAAChC,CAAC,KAAK,IAAI,IAAI+B,WAAW,CAACC,MAAM,CAAChC,CAAC,KAAKiC,SAAS;UAC5E,CAAC;UACDC,SAAS,EAAE;YACTC,KAAK,EAAE,SAAAA,CAASC,OAAY;cAC1B;cACA,IAAIA,OAAO,IAAIA,OAAO,CAAChM,MAAM,GAAG,CAAC,EAAE;gBACjC,MAAMiM,MAAM,GAAGD,OAAO,CAAC,CAAC,CAAC,CAACJ,MAAM,CAACjC,CAAC;gBAClC;gBACA,IAAIsC,MAAM,KAAKJ,SAAS,IAAII,MAAM,KAAK,IAAI,EAAE;kBAC3C;kBACA,MAAMC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,CAAC;kBAChC,MAAMI,OAAO,GAAGF,IAAI,CAACG,KAAK,CAAC,CAACL,MAAM,GAAGC,KAAK,IAAI,EAAE,CAAC;kBACjD,MAAMK,aAAa,GAAG,GAAGL,KAAK,CAACM,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIJ,OAAO,CAACG,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;kBACnG,OAAO,SAASF,aAAa,EAAE;;;cAGnC,OAAO,aAAa;YACtB,CAAC;YACDG,KAAK,EAAE,SAAAA,CAASV,OAAY;cAC1B,MAAMU,KAAK,GAAGV,OAAO,CAACW,OAAO,CAACD,KAAK,IAAI,EAAE;cACzC,MAAME,KAAK,GAAGZ,OAAO,CAACJ,MAAM,CAAChC,CAAC;cAC9B,OAAO,GAAG8C,KAAK,KAAKE,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,KAAK;YAC7D;;;OAGL;MACDpD,MAAM,EAAE;QACNC,CAAC,EAAE;UACDoD,IAAI,EAAE,QAAQ;UACdtD,OAAO,EAAE,IAAI;UACbmB,QAAQ,EAAE,QAAQ;UAClBoC,GAAG,EAAE,CAAC;UACNC,GAAG,EAAE,EAAE;UACPC,KAAK,EAAE;YACLC,QAAQ,EAAE,EAAE;YACZC,QAAQ,EAAE,SAAAA,CAASR,KAAU;cAC3B;cACA,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,OAAO;cAC/B,IAAIA,KAAK,KAAK,EAAE,EAAE,OAAO,OAAO;cAChC,IAAIA,KAAK,KAAK,EAAE,EAAE,OAAO,OAAO;cAChC,OAAO,EAAE;YACX,CAAC;YACDS,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE;cACJ9B,IAAI,EAAE,EAAE;cACRC,MAAM,EAAE;aACT;YACDL,OAAO,EAAE;WACV;UACDmC,IAAI,EAAE;YACJ9D,OAAO,EAAE;WACV;UACD+D,MAAM,EAAE;YACN/D,OAAO,EAAE,IAAI;YACb4D,KAAK,EAAE,SAAS;YAChBI,KAAK,EAAE;;SAEV;QACD7D,CAAC,EAAE;UACDH,OAAO,EAAE,IAAI;UACbmB,QAAQ,EAAE,MAAM;UAChBmB,KAAK,EAAE;YACLtC,OAAO,EAAE,IAAI;YACbiE,IAAI,EAAE,IAAI;YACVL,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE;cACJ9B,IAAI,EAAE,EAAE;cACRC,MAAM,EAAE;;WAEX;UACDyB,KAAK,EAAE;YACLS,aAAa,EAAE,CAAC;YAChBP,QAAQ,EAAE,SAAAA,CAASR,KAAU;cAC3B;cACA,OAAOA,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;YACzB,CAAC;YACDQ,KAAK,EAAE,SAAS;YAChBC,IAAI,EAAE;cACJ9B,IAAI,EAAE,EAAE;cACRC,MAAM,EAAE;aACT;YACDL,OAAO,EAAE,CAAC;YACVwC,WAAW,EAAE,CAAC;YACdC,WAAW,EAAE;WACd;UACDN,IAAI,EAAE;YACJ9D,OAAO,EAAE;WACV;UACD+D,MAAM,EAAE;YACN/D,OAAO,EAAE,IAAI;YACb4D,KAAK,EAAE,SAAS;YAChBI,KAAK,EAAE;;;OAGZ;MACDK,SAAS,EAAE;QACTC,QAAQ,EAAE,GAAG;QACbC,MAAM,EAAE;OACT;MACDC,MAAM,EAAE;QACN7C,OAAO,EAAE;UACP8C,GAAG,EAAE,EAAE;UACPC,MAAM,EAAE,CAAC;UACTC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE;;;KAGZ;EAKP;EAEAjG,gBAAgBA,CAAA;IACd,IAAI,CAAClH,YAAY,GAAG,IAAI;IACxB,IAAI,CAACiF,gBAAgB,CAACiC,gBAAgB,EAAE,CAACkG,IAAI,CAACC,aAAa,IAAG;MAC5D,IAAIA,aAAa,CAACvO,MAAM,GAAG,CAAC,EAAC;QAC3B,IAAI,CAACkG,eAAe,CAACsI,eAAe,EAAE,CAACF,IAAI,CAACpH,YAAY,IAAG;UACzD,IAAI,CAACX,YAAY,CAACkI,WAAW,CAACvH,YAAY,CAAC;UAC3C,IAAI,CAACnH,QAAQ,GAAGmH,YAAY;UAC5BuB,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;UAC3BD,OAAO,CAACC,GAAG,CAACxB,YAAY,CAAC;UAEzB;UACA,IAAI,CAACwH,uBAAuB,EAAE;UAE9B;UACA,IAAI,CAACC,YAAY,EAAE;UAEnB;UACA,IAAI,CAACC,mBAAmB,EAAE;UAE1B,IAAI,CAAC1N,YAAY,GAAG,KAAK;QAC3B,CAAC,CAAC;OACH,MAAI;QACH,IAAI,CAACoF,MAAM,CAACuI,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;;IAE5C,CAAC,CAAC,CAACC,KAAK,CAAC1F,KAAK,IAAG;MACfX,OAAO,CAACW,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,IAAI,CAAClI,YAAY,GAAG,KAAK;IAC3B,CAAC,CAAC;EACJ;EAEA;EACA0N,mBAAmBA,CAAA;IACf,IAAI,CAAC,IAAI,CAAC7O,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;IAElD;IACA,IAAI,CAACD,QAAQ,CAACiJ,OAAO,CAAC+F,OAAO,IAAG;MAC5B,IAAI,CAACC,eAAe,CAACD,OAAO,CAAC;IACjC,CAAC,CAAC;EACN;EAEA;EACAC,eAAeA,CAACD,OAAgB;IAC5B,IAAI,CAACA,OAAO,EAAE;IAEd,MAAME,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,kBAAkB,GAAG,IAAID,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;IAC5G,MAAMC,gBAAgB,GAAG,IAAIN,IAAI,CAACD,GAAG,CAACG,WAAW,EAAE,EAAEH,GAAG,CAACI,QAAQ,EAAE,EAAEJ,GAAG,CAACK,OAAO,EAAE,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;IAE9G,IAAIE,OAAO,GAA2B;MAClCC,MAAM,EAAEX,OAAO,CAACY,SAAS;MACzBC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAEV,kBAAkB;MACjCW,WAAW,EAAEN,gBAAgB;MAC7BO,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,IAAI;MAChB9G,SAAS,EAAE6F,OAAO,CAAC9M;KACtB;IAED,IAAI,CAACiE,eAAe,CAAC+J,sBAAsB,CAACR,OAAO,CAAC,CAACnB,IAAI,CAAC4B,IAAI,IAAG;MAC7D,IAAIA,IAAI,IAAIA,IAAI,CAACA,IAAI,IAAIA,IAAI,CAACA,IAAI,CAAClQ,MAAM,GAAG,CAAC,EAAE;QAC3C;QACA,MAAMmQ,YAAY,GAAG,IAAI,CAACC,uBAAuB,CAACF,IAAI,CAACA,IAAI,CAAC;QAE5D,MAAMG,aAAa,GAAGC,gBAAgB,CAACC,QAAQ,CAACC,eAAe,CAAC;QAEhE;QACA,MAAMC,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACV,IAAI,CAACA,IAAI,CAACW,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;QACvE,MAAMC,sBAAsB,GAAGN,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACV,IAAI,CAACA,IAAI,CAACW,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACG,eAAe,CAAC,CAAC,CAAC;QACzF;QACA,MAAMC,eAAe,GAAGR,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACV,IAAI,CAACA,IAAI,CAACW,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACzO,IAAI,CAAC,CAAC,CAAC;QAEvE,IAAI8O,QAAQ,GAAS,EAAE;QACvB;QACA,MAAMC,iBAAiB,GAA6B,EAAE;QAEtDlB,IAAI,CAACA,IAAI,CAAClH,OAAO,CAAC8H,CAAC,IAAG;UAClB,IAAI,CAACM,iBAAiB,CAACN,CAAC,CAACzO,IAAI,CAAC,EAAE;YAC5B+O,iBAAiB,CAACN,CAAC,CAACzO,IAAI,CAAC,GAAG,EAAE;;UAElC+O,iBAAiB,CAACN,CAAC,CAACzO,IAAI,CAAC,CAACgP,IAAI,CAACP,CAAC,CAACQ,WAAW,CAAC;QACjD,CAAC,CAAC;QAEFJ,eAAe,CAAClI,OAAO,CAACuI,GAAG,IAAG;UAC1B,IAAIlE,KAAK,GAAG,IAAI,CAACmE,cAAc,EAAE;UACjCL,QAAQ,CAACE,IAAI,CAAC;YACV3E,KAAK,EAAE6E,GAAG;YACVrB,IAAI,EAAEkB,iBAAiB,CAACG,GAAG,CAAC;YAC5BE,IAAI,EAAE,KAAK;YACX3G,eAAe,EAAEuC,KAAK;YACtBpC,WAAW,EAAEoC,KAAK;YAClBjD,OAAO,EAAE;WACZ,CAAC;QAEN,CAAC,CAAC;QAEF,MAAMsH,SAAS,GAAGxB,IAAI,CAACA,IAAI,CAAC,CAAC,CAAC,CAAC7N,IAAI,CAAC,CAAC;QACrC,MAAMsP,qBAAqB,GAAGzB,IAAI,CAACA,IAAI,CACtC5Q,MAAM,CAACwR,CAAC,IAAIA,CAAC,CAACzO,IAAI,KAAKqP,SAAS,CAAC,CAAC;QAAA,CAClCb,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAC;QAEvB;QACA,MAAMa,iBAAiB,GAAG,IAAI,CAACC,yBAAyB,CAACV,QAAQ,EAAEQ,qBAAqB,CAAC;QAEzF,MAAMG,QAAQ,GAAG;UACbC,MAAM,EAAE,EAAE;UACVZ,QAAQ,EAAES;SACb;QAID;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA,IAAI,CAAC1K,YAAY,CAAC+B,GAAG,CAAC8F,OAAO,CAAC9M,EAAE,EAAE6P,QAAQ,CAAC;QAC3C,IAAI,CAAC1K,eAAe,CAAC6B,GAAG,CAAC8F,OAAO,CAAC9M,EAAE,EAAEkO,YAAY,CAAC;QAClD,IAAI,CAAC9I,eAAe,CAAC4B,GAAG,CAAC8F,OAAO,CAAC9M,EAAE,EAAEiO,IAAI,CAAC8B,GAAG,CAAC;QAE9C;QACA,IAAI,CAACjD,OAAO,CAACrM,cAAc,EAAE;UACzBqM,OAAO,CAACrM,cAAc,GAAGwO,eAAe,CAAClR,MAAM;;QAGnD;QACA,IAAI,CAACiS,iBAAiB,CAAClD,OAAO,CAAC9M,EAAE,CAAC;OACrC,MAAM;QACH;QACAwG,OAAO,CAACyJ,IAAI,CAAC,iCAAiCnD,OAAO,CAAC1M,IAAI,KAAK0M,OAAO,CAAC9M,EAAE,GAAG,CAAC;QAE7E;QACA,IAAI,CAACiF,YAAY,CAAC+B,GAAG,CAAC8F,OAAO,CAAC9M,EAAE,EAAE;UAAE8P,MAAM,EAAE,EAAE;UAAEZ,QAAQ,EAAE;QAAE,CAAE,CAAC;QAC/D,IAAI,CAAC/J,eAAe,CAAC6B,GAAG,CAAC8F,OAAO,CAAC9M,EAAE,EAAE,EAAE,CAAC;QACxC,IAAI,CAACoF,eAAe,CAAC4B,GAAG,CAAC8F,OAAO,CAAC9M,EAAE,EAAE,IAAI,CAAC;;IAElD,CAAC,CAAC,CAAC6M,KAAK,CAAC1F,KAAK,IAAG;MACbX,OAAO,CAACW,KAAK,CAAC,kCAAkC2F,OAAO,CAAC1M,IAAI,KAAK0M,OAAO,CAAC9M,EAAE,IAAI,EAAEmH,KAAK,CAAC;MAEvF;MACA,IAAI,CAAClC,YAAY,CAAC+B,GAAG,CAAC8F,OAAO,CAAC9M,EAAE,EAAE;QAAE8P,MAAM,EAAE,EAAE;QAAEZ,QAAQ,EAAE;MAAE,CAAE,CAAC;MAC/D,IAAI,CAAC/J,eAAe,CAAC6B,GAAG,CAAC8F,OAAO,CAAC9M,EAAE,EAAE,EAAE,CAAC;MACxC,IAAI,CAACoF,eAAe,CAAC4B,GAAG,CAAC8F,OAAO,CAAC9M,EAAE,EAAE,IAAI,CAAC;IAC9C,CAAC,CAAC;IAEF;IACA,IAAI,CAACkQ,mBAAmB,CAACpD,OAAO,CAAC9M,EAAE,CAAC;IAEpC;IACA,IAAI,CAACmQ,+BAA+B,CAACrD,OAAO,CAAC9M,EAAE,CAAC;EACpD;EAGAuP,cAAcA,CAAA;IACV;IACA,MAAMa,kBAAkB,GAAG,CACvB,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,CAAE;IAAA,CACd;;IAED,OAAOA,kBAAkB,CAAClG,IAAI,CAACC,KAAK,CAACD,IAAI,CAACmG,MAAM,EAAE,GAAGD,kBAAkB,CAACrS,MAAM,CAAC,CAAC;EACpF;EAEA;EACAgD,uBAAuBA,CAAC+L,OAAgB;IACpC;IACA,IAAIA,OAAO,IAAIA,OAAO,CAAC9M,EAAE,IAAI,IAAI,CAACiF,YAAY,CAACqL,GAAG,CAACxD,OAAO,CAAC9M,EAAE,CAAC,EAAE;MAC5D,OAAO,IAAI,CAACiF,YAAY,CAACsL,GAAG,CAACzD,OAAO,CAAC9M,EAAE,CAAC;;IAG5C;IACA,OAAO,IAAI;EACf;EAEA;EACQmQ,+BAA+BA,CAAClJ,SAAiB;IACrD,IAAI,CAACA,SAAS,EAAE;IAEhB,IAAI,CAACxC,oBAAoB,CAAC+L,UAAU,CAACvJ,SAAS,CAAC,CAC1ClB,SAAS,CAAC;MACPa,IAAI,EAAG6J,OAAO,IAAI;QACd,IAAI,CAACjL,yBAAyB,CAACwB,GAAG,CAACC,SAAS,EAAEwJ,OAAO,CAAC;QAEtD;QACA,MAAM3D,OAAO,GAAG,IAAI,CAAChP,QAAQ,CAAC4S,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3Q,EAAE,KAAKiH,SAAS,CAAC;QAC3D,IAAI6F,OAAO,EAAE;UACTA,OAAO,CAACrM,cAAc,GAAGgQ,OAAO,CAAC1S,MAAM;UACvCyI,OAAO,CAACC,GAAG,CAAC,WAAWQ,SAAS,2BAA2BwJ,OAAO,CAAC1S,MAAM,sBAAsB,CAAC;;QAGpG;QACA,IAAI,CAACwH,cAAc,CAACqL,MAAM,CAAC3J,SAAS,CAAC;MACzC,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACbX,OAAO,CAACyJ,IAAI,CAAC,oDAAoDhJ,SAAS,GAAG,EAAEE,KAAK,CAAC;QACrF;QACA,IAAI,CAAC3B,yBAAyB,CAACwB,GAAG,CAACC,SAAS,EAAE,EAAE,CAAC;QAEjD;QACA,MAAM6F,OAAO,GAAG,IAAI,CAAChP,QAAQ,CAAC4S,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3Q,EAAE,KAAKiH,SAAS,CAAC;QAC3D,IAAI6F,OAAO,EAAE;UACT,MAAMmB,IAAI,GAAG,IAAI,CAAC9I,eAAe,CAACoL,GAAG,CAACtJ,SAAS,CAAC;UAChD6F,OAAO,CAACrM,cAAc,GAAGwN,IAAI,IAAIA,IAAI,CAAClQ,MAAM,GAAG,CAAC,GAC1C,IAAI4Q,GAAG,CAACV,IAAI,CAACW,GAAG,CAACiC,IAAI,IAAIA,IAAI,CAACzQ,IAAI,CAAC,CAAC,CAACmJ,IAAI,GACzC,CAAC;;MAEf;KACH,CAAC;EACV;EAEA;EACQyG,iBAAiBA,CAAC/I,SAAiB;IACvC;IACA,IAAI,CAAC5B,iBAAiB,CAACuL,MAAM,CAAC3J,SAAS,CAAC;IACxC,IAAI,CAAC3B,eAAe,CAACsL,MAAM,CAAC3J,SAAS,CAAC;IACtC,IAAI,CAAC1B,cAAc,CAACqL,MAAM,CAAC3J,SAAS,CAAC;IACrC,IAAI,CAACxB,aAAa,CAACmL,MAAM,CAAC3J,SAAS,CAAC;IACpC,IAAI,CAACvB,kBAAkB,CAACkL,MAAM,CAAC3J,SAAS,CAAC;IACzC,IAAI,CAACzB,yBAAyB,CAACoL,MAAM,CAAC3J,SAAS,CAAC;EACpD;EAEA1G,kBAAkBA,CAAC0G,SAAgB;IACjC,OAAO,IAAI,CAAC7B,eAAe,CAACmL,GAAG,CAACtJ,SAAS,CAAC;EAC5C;EAEA6J,oBAAoBA,CAAC7J,SAAgB;IACnC;IACA,IAAI,IAAI,CAAC1B,cAAc,CAAC+K,GAAG,CAACrJ,SAAS,CAAC,EAAE;MACtCT,OAAO,CAACC,GAAG,CAAC,QAAQ,GAAGQ,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC1B,cAAc,CAACgL,GAAG,CAACtJ,SAAS,CAAE,CAAC;MAC7E,OAAO,IAAI,CAAC1B,cAAc,CAACgL,GAAG,CAACtJ,SAAS,CAAE;;IAG5C;IACA,MAAM8J,aAAa,GAAG,IAAI,CAACvL,yBAAyB,CAAC+K,GAAG,CAACtJ,SAAS,CAAC;IACnE,IAAI+J,MAAM,GAAG,CAAC;IAEd,IAAID,aAAa,IAAIA,aAAa,CAAChT,MAAM,GAAG,CAAC,EAAE;MAC7CyI,OAAO,CAACC,GAAG,CAAC,UAAU,GAAGQ,SAAS,GAAG,GAAG,GAAG8J,aAAa,CAAChT,MAAM,CAAC;MAChE;MACAiT,MAAM,GAAGD,aAAa,CAAChT,MAAM;KAC9B,MAAM;MACL;MACA,MAAMkQ,IAAI,GAAG,IAAI,CAAC9I,eAAe,CAACoL,GAAG,CAACtJ,SAAS,CAAC;MAChD,IAAIgH,IAAI,IAAIA,IAAI,CAAClQ,MAAM,GAAG,CAAC,EAAE;QAC3BiT,MAAM,GAAG,IAAIrC,GAAG,CAACV,IAAI,CAACW,GAAG,CAACiC,IAAI,IAAIA,IAAI,CAACzQ,IAAI,CAAC,CAAC,CAACmJ,IAAI;;;IAItD;IACA,IAAI,CAAChE,cAAc,CAACyB,GAAG,CAACC,SAAS,EAAE+J,MAAM,CAAC;IAC1C,OAAOA,MAAM;EACf;EAEAlQ,oBAAoBA,CAACmG,SAAiB;IACpC;IACA,IAAI,IAAI,CAAC3B,eAAe,CAACgL,GAAG,CAACrJ,SAAS,CAAC,EAAE;MACvC,OAAO,IAAI,CAAC3B,eAAe,CAACiL,GAAG,CAACtJ,SAAS,CAAE;;IAG7C,MAAMgH,IAAI,GAAG,IAAI,CAAC9I,eAAe,CAACoL,GAAG,CAACtJ,SAAS,CAAC;IAChD,IAAI,CAACgH,IAAI,IAAIA,IAAI,CAAClQ,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACuH,eAAe,CAAC0B,GAAG,CAACC,SAAS,EAAE,GAAG,CAAC;MACxC,OAAO,GAAG;;IAGZ,MAAMgK,MAAM,GAAGhD,IAAI,CAACiD,MAAM,CAAC,CAACC,WAAW,EAAEC,OAAO,KAAI;MAClD,OAAO,IAAInE,IAAI,CAACmE,OAAO,CAACtC,QAAQ,CAAC,CAACuC,OAAO,EAAE,GAAG,IAAIpE,IAAI,CAACkE,WAAW,CAACrC,QAAQ,CAAC,CAACuC,OAAO,EAAE,GAClFD,OAAO,GACPD,WAAW;IACjB,CAAC,CAAC;IAEF,MAAMH,MAAM,GAAG,IAAI/D,IAAI,CAACgE,MAAM,CAACnC,QAAQ,CAAC,CAACwC,cAAc,CAAC,OAAO,EAAE;MAC/DC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE,KAAK;MACbC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,QAAQ,EAAE;KACX,CAAC;IAEF;IACA,IAAI,CAACvM,eAAe,CAAC0B,GAAG,CAACC,SAAS,EAAE+J,MAAM,CAAC;IAC3C,OAAOA,MAAM;EACf;EAEAnQ,eAAeA,CAACoG,SAAiB;IAC/B;IACA,IAAI,IAAI,CAAC5B,iBAAiB,CAACiL,GAAG,CAACrJ,SAAS,CAAC,EAAE;MACzC,OAAO,IAAI,CAAC5B,iBAAiB,CAACkL,GAAG,CAACtJ,SAAS,CAAE;;IAG/C,MAAMgH,IAAI,GAAG,IAAI,CAAC9I,eAAe,CAACoL,GAAG,CAACtJ,SAAS,CAAC;IAChD,IAAI,CAACgH,IAAI,IAAIA,IAAI,CAAClQ,MAAM,KAAK,CAAC,EAAE;MAC9B,IAAI,CAACsH,iBAAiB,CAAC2B,GAAG,CAACC,SAAS,EAAE,CAAC,CAAC;MACxC,OAAO,CAAC;;IAGV;IACA,MAAM6K,eAAe,GAAG5H,IAAI,CAACc,GAAG,CAAC,GAAGiD,IAAI,CAACW,GAAG,CAACiC,IAAI,IAAI,IAAI5D,IAAI,CAAC4D,IAAI,CAAC/B,QAAQ,CAAC,CAACuC,OAAO,EAAE,CAAC,CAAC;IAExF;IACA,MAAMU,aAAa,GAAG9D,IAAI,CAAC5Q,MAAM,CAACwT,IAAI,IACpC,IAAI5D,IAAI,CAAC4D,IAAI,CAAC/B,QAAQ,CAAC,CAACuC,OAAO,EAAE,KAAKS,eAAe,CACtD;IAED,MAAMd,MAAM,GAAGe,aAAa,CAACb,MAAM,CAAC,CAACnB,GAAG,EAAEc,IAAI,KAAKd,GAAG,IAAIc,IAAI,CAACxB,WAAW,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAEpF;IACA,IAAI,CAAChK,iBAAiB,CAAC2B,GAAG,CAACC,SAAS,EAAE+J,MAAM,CAAC;IAC7C,OAAOA,MAAM;EACf;EAEA;EACAvR,yBAAyBA,CAACqN,OAAgB;IACxC;IAEA;IACA,MAAMkF,WAAW,GAAG;MAClB,GAAG,IAAI,CAACpK,WAAW;MACnBN,OAAO,EAAE;QACP,GAAG,IAAI,CAACM,WAAW,CAACN,OAAO;QAC3BmB,OAAO,EAAE;UACP,GAAG,IAAI,CAACb,WAAW,CAACN,OAAO,CAACmB,OAAO;UACnCoB,SAAS,EAAE;YACT,GAAG,IAAI,CAACjC,WAAW,CAACN,OAAO,CAACmB,OAAO,CAACoB;;;OAGzC;MACDpC,MAAM,EAAE;QACN,GAAG,IAAI,CAACG,WAAW,CAACH,MAAM;QAC1BC,CAAC,EAAE;UAAE,GAAG,IAAI,CAACE,WAAW,CAACH,MAAM,CAACC;QAAC,CAAE;QACnCC,CAAC,EAAE;UAAE,GAAG,IAAI,CAACC,WAAW,CAACH,MAAM,CAACE;QAAC;;KAEpC;IAED;IACA,MAAMsK,WAAW,GAAG,IAAI,CAAChN,YAAY,CAACsL,GAAG,CAACzD,OAAO,CAAC9M,EAAE,CAAC;IACrD,MAAMkS,OAAO,GAAG,IAAI,CAAC/M,eAAe,CAACoL,GAAG,CAACzD,OAAO,CAAC9M,EAAE,CAAC;IAEpD;IACA,IAAImS,YAAY,GAAG,CAAC;IAEpB;IACA,IAAID,OAAO,IAAIA,OAAO,CAACnU,MAAM,GAAG,CAAC,EAAE;MACjCoU,YAAY,GAAGjI,IAAI,CAACc,GAAG,CAAC,GAAGkH,OAAO,CAACtD,GAAG,CAAEiC,IAAS,IAAKA,IAAI,CAACxB,WAAW,IAAI,CAAC,CAAC,CAAC;;IAG/E;IACA,IAAI8C,YAAY,KAAK,CAAC,EAAE;MACtB,MAAMC,eAAe,GAAG,IAAI,CAAC7R,kBAAkB,CAACuM,OAAO,CAAC9M,EAAE,CAAC;MAC3D,IAAIoS,eAAe,IAAIA,eAAe,GAAG,CAAC,EAAE;QAC1CD,YAAY,GAAGC,eAAe;;;IAIlC;IACA,IAAIC,QAAQ,GAAGF,YAAY,GAAG,GAAG;IAEjC;IACA,IAAIE,QAAQ,IAAI,CAAC,EAAE;MACjBA,QAAQ,GAAGnI,IAAI,CAACoI,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;KAC3C,MAAM,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACxBA,QAAQ,GAAGnI,IAAI,CAACoI,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KACzC,MAAM,IAAIA,QAAQ,IAAI,EAAE,EAAE;MACzBA,QAAQ,GAAGnI,IAAI,CAACoI,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;KACjC,MAAM,IAAIA,QAAQ,IAAI,GAAG,EAAE;MAC1BA,QAAQ,GAAGnI,IAAI,CAACoI,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KACzC,MAAM,IAAIA,QAAQ,IAAI,GAAG,EAAE;MAC1BA,QAAQ,GAAGnI,IAAI,CAACoI,IAAI,CAACD,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;KACzC,MAAM;MACLA,QAAQ,GAAGnI,IAAI,CAACoI,IAAI,CAACD,QAAQ,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;;IAG5C;IACA,IAAIA,QAAQ,KAAK,CAAC,IAAIA,QAAQ,GAAG,GAAG,EAAE;MACpCA,QAAQ,GAAG,CAAC,CAAC,CAAC;;IAGhB;IACA;IAEA;IACA,MAAME,QAAQ,GAAGF,QAAQ,GAAG,CAAC;IAE7BL,WAAW,CAACvK,MAAM,CAACE,CAAC,CAACoD,GAAG,GAAG,CAAC;IAC5BiH,WAAW,CAACvK,MAAM,CAACE,CAAC,CAACqD,GAAG,GAAGqH,QAAQ;IAEnC;IACAL,WAAW,CAACvK,MAAM,CAACE,CAAC,CAACsD,KAAK,GAAG;MAC3BC,QAAQ,EAAEqH,QAAQ;MAClB7G,aAAa,EAAE,CAAC;MAChBP,QAAQ,EAAE,SAAAA,CAASR,KAAU;QAC3B;QACA,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,GAAG;QAC3B,IAAIA,KAAK,KAAK4H,QAAQ,EAAE,OAAOrI,IAAI,CAACG,KAAK,CAACkI,QAAQ,CAAC,CAAChI,QAAQ,EAAE;QAC9D,IAAII,KAAK,KAAK0H,QAAQ,EAAE,OAAOnI,IAAI,CAACG,KAAK,CAACgI,QAAQ,CAAC,CAAC9H,QAAQ,EAAE;QAC9D,OAAO,EAAE,CAAC,CAAC;MACb,CAAC;;MACDa,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;QACJ9B,IAAI,EAAE,EAAE;QACRC,MAAM,EAAE;;KAEX;IAED;IAEA;IACD;IACA;IAEC;IACA,OAAOwI,WAAW;EACpB;EAEA;EACQpC,yBAAyBA,CAACV,QAAe,EAAEsD,SAAmB;IAClE,OAAOtD,QAAQ,CAACN,GAAG,CAAClE,OAAO,IAAG;MAC1B,MAAM+H,aAAa,GAA6B,EAAE;MAElD/H,OAAO,CAACuD,IAAI,CAAClH,OAAO,CAAC,CAAC4D,KAAa,EAAE+H,KAAa,KAAI;QAClD,IAAIA,KAAK,GAAGF,SAAS,CAACzU,MAAM,EAAE;UAC1B,MAAM+Q,QAAQ,GAAG,IAAI7B,IAAI,CAACuF,SAAS,CAACE,KAAK,CAAC,CAAC;UAC3C,MAAMzI,KAAK,GAAG6E,QAAQ,CAAC6D,QAAQ,EAAE,GAAI7D,QAAQ,CAAC8D,UAAU,EAAE,GAAG,EAAG;UAChEH,aAAa,CAACrD,IAAI,CAAC;YAAC1H,CAAC,EAAEuC,KAAK;YAAEtC,CAAC,EAAEgD;UAAK,CAAC,CAAC;;MAEhD,CAAC,CAAC;MAEF,OAAO;QACH,GAAGD,OAAO;QACVuD,IAAI,EAAEwE;OACT;IACL,CAAC,CAAC;EACN;EAEA;EACApS,YAAYA,CAACsK,KAAa,EAAEkI,QAAA,GAAmB,CAAC;IAC5C,OAAOlI,KAAK,CAACC,OAAO,CAACiI,QAAQ,CAAC,CAAChI,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EACpD;EAEA;EACA1K,cAAcA,CAAC8G,SAAiB,EAAE6L,YAAoB;IAClD,IAAI;MACA,IAAI,CAAC7L,SAAS,EAAE,OAAO6L,YAAY,IAAI,KAAK;MAC5C,MAAM9M,MAAM,GAAG,IAAI,CAACL,qBAAqB,CAAC4K,GAAG,CAACtJ,SAAS,CAAC;MACxD,OAAOjB,MAAM,EAAE+M,UAAU,IAAI/M,MAAM,EAAE5F,IAAI,IAAI0S,YAAY,IAAI,KAAK;KACrE,CAAC,OAAO3L,KAAK,EAAE;MACZX,OAAO,CAACyJ,IAAI,CAAC,0CAA0ChJ,SAAS,GAAG,EAAEE,KAAK,CAAC;MAC3E,OAAO2L,YAAY,IAAI,KAAK;;EAEpC;EAEA5R,QAAQA,CAAC+F,SAAiB;IACtB,IAAI;MACA,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;MAC5B,MAAMjB,MAAM,GAAG,IAAI,CAACL,qBAAqB,CAAC4K,GAAG,CAACtJ,SAAS,CAAC;MACxD,OAAOjB,MAAM,EAAEgN,KAAK,IAAI,KAAK;KAChC,CAAC,OAAO7L,KAAK,EAAE;MACZX,OAAO,CAACyJ,IAAI,CAAC,mCAAmChJ,SAAS,GAAG,EAAEE,KAAK,CAAC;MACpE,OAAO,KAAK;;EAEpB;EAEAlG,YAAYA,CAACgG,SAAiB;IAC1B,IAAI;MACA,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;MAC5B,MAAMjB,MAAM,GAAG,IAAI,CAACL,qBAAqB,CAAC4K,GAAG,CAACtJ,SAAS,CAAC;MACxD,OAAOjB,MAAM,EAAEiN,SAAS,IAAI,KAAK;KACpC,CAAC,OAAO9L,KAAK,EAAE;MACZX,OAAO,CAACyJ,IAAI,CAAC,uCAAuChJ,SAAS,GAAG,EAAEE,KAAK,CAAC;MACxE,OAAO,KAAK;;EAEpB;EAEAhG,UAAUA,CAAC8F,SAAiB;IACxB,IAAI;MACA,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;MAC5B,MAAMjB,MAAM,GAAG,IAAI,CAACL,qBAAqB,CAAC4K,GAAG,CAACtJ,SAAS,CAAC;MACxD,OAAOjB,MAAM,EAAEkN,OAAO,IAAI,KAAK;KAClC,CAAC,OAAO/L,KAAK,EAAE;MACZX,OAAO,CAACyJ,IAAI,CAAC,qCAAqChJ,SAAS,GAAG,EAAEE,KAAK,CAAC;MACtE,OAAO,KAAK;;EAEpB;EAEA/F,aAAaA,CAAC6F,SAAiB;IAC3B,IAAI;MACA,IAAI,CAACA,SAAS,EAAE,OAAO,KAAK;MAC5B,MAAMjB,MAAM,GAAG,IAAI,CAACL,qBAAqB,CAAC4K,GAAG,CAACtJ,SAAS,CAAC;MACxD,OAAOjB,MAAM,EAAEmN,UAAU,IAAI,KAAK;KACrC,CAAC,OAAOhM,KAAK,EAAE;MACZX,OAAO,CAACyJ,IAAI,CAAC,wCAAwChJ,SAAS,GAAG,EAAEE,KAAK,CAAC;MACzE,OAAO,KAAK;;EAEpB;EAEA7G,oBAAoBA,CAAC2G,SAAiB;IAClC,IAAI;MACA,IAAI,CAACA,SAAS,EAAE,OAAO,CAAC;MACxB,MAAMjB,MAAM,GAAG,IAAI,CAACL,qBAAqB,CAAC4K,GAAG,CAACtJ,SAAS,CAAC;MACxD,OAAOjB,MAAM,EAAEoN,mBAAmB,IAAI,CAAC;KAC1C,CAAC,OAAOjM,KAAK,EAAE;MACZX,OAAO,CAACyJ,IAAI,CAAC,gDAAgDhJ,SAAS,GAAG,EAAEE,KAAK,CAAC;MACjF,OAAO,CAAC;;EAEhB;EAEA9F,eAAeA,CAAC4F,SAAiB,EAAEoM,gBAAwB;IACvD,IAAI;MACA,IAAI,CAACpM,SAAS,EAAE,OAAOoM,gBAAgB,IAAI,KAAK;MAChD,MAAMrN,MAAM,GAAG,IAAI,CAACL,qBAAqB,CAAC4K,GAAG,CAACtJ,SAAS,CAAC;MACxD,OAAOjB,MAAM,EAAEsN,YAAY,IAAID,gBAAgB,IAAI,KAAK;KAC3D,CAAC,OAAOlM,KAAK,EAAE;MACZX,OAAO,CAACyJ,IAAI,CAAC,2CAA2ChJ,SAAS,GAAG,EAAEE,KAAK,CAAC;MAC5E,OAAOkM,gBAAgB,IAAI,KAAK;;EAExC;EAEAE,YAAYA,CAACjN,KAAU;IACnB,MAAMqE,KAAK,GAAGrE,KAAK,CAACqE,KAAK;IAEzB,IAAIA,KAAK,CAAC6I,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;MAC1B,IAAI,CAAC5O,SAAS,GAAG,CAAC,CAAC;MACnB,IAAI,CAACC,SAAS,GAAG8F,KAAK,CAAC8I,SAAS,CAAC,CAAC,EAAE9I,KAAK,CAAC5M,MAAM,CAAC;KACpD,MAAM;MACH,IAAI,CAAC6G,SAAS,GAAG,CAAC;MAClB,IAAI,CAACC,SAAS,GAAG8F,KAAK;;EAE9B;EAEA+I,QAAQA,CAACC,EAAY,EAAErN,KAAY;IAC/BqN,EAAE,CAACtW,MAAM,CAAEiJ,KAAK,CAACsN,MAA2B,CAACjJ,KAAK,CAAC;EACvD;EAEA;EACA8B,uBAAuBA,CAAA;IACnB;IACA,MAAMoH,SAAS,GAAG,CAAC,GAAG,IAAIlF,GAAG,CAAC,IAAI,CAAC7Q,QAAQ,CAAC8Q,GAAG,CAAC9B,OAAO,IAAIA,OAAO,CAACxL,QAAQ,CAAC,CAAC,CAAC;IAC9E,IAAI,CAACoC,kBAAkB,GAAGmQ,SAAS,CAACjF,GAAG,CAACtN,QAAQ,KAAK;MACjDmJ,KAAK,EAAEnJ,QAAQ;MACfqJ,KAAK,EAAErJ;KACV,CAAC,CAAC;IAEH;IACA,MAAMwS,QAAQ,GAAG,CAAC,GAAG,IAAInF,GAAG,CAAC,IAAI,CAAC7Q,QAAQ,CAAC8Q,GAAG,CAAC9B,OAAO,IAAIA,OAAO,CAACtL,MAAM,CAAC,CAAC,CAAC;IAC3E,IAAI,CAACmC,iBAAiB,GAAGmQ,QAAQ,CAAClF,GAAG,CAACpN,MAAM,KAAK;MAC7CiJ,KAAK,EAAEjJ,MAAM;MACbmJ,KAAK,EAAEnJ;KACV,CAAC,CAAC;EACP;EAEA;EACAkL,YAAYA,CAAA;IACR,IAAIqH,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACjW,QAAQ,CAAC;IAEjC;IACA,IAAI,IAAI,CAACoF,UAAU,EAAE;MACjB,MAAM8Q,WAAW,GAAG,IAAI,CAAC9Q,UAAU,CAACzB,WAAW,EAAE;MACjDsS,QAAQ,GAAGA,QAAQ,CAAC1W,MAAM,CAACyP,OAAO,IAC9BA,OAAO,CAAC1M,IAAI,CAACqB,WAAW,EAAE,CAACwS,QAAQ,CAACD,WAAW,CAAC,IAChDlH,OAAO,CAACxL,QAAQ,CAACG,WAAW,EAAE,CAACwS,QAAQ,CAACD,WAAW,CAAC,IACnDlH,OAAO,CAACoH,QAAQ,IAAIpH,OAAO,CAACoH,QAAQ,CAACzS,WAAW,EAAE,CAACwS,QAAQ,CAACD,WAAW,CAAE,CAC7E;;IAGL;IACA,IAAI,IAAI,CAAC1R,gBAAgB,EAAE;MACvByR,QAAQ,GAAGA,QAAQ,CAAC1W,MAAM,CAACyP,OAAO,IAAIA,OAAO,CAACxL,QAAQ,KAAK,IAAI,CAACgB,gBAAgB,CAAC;;IAGrF;IACA,IAAI,IAAI,CAACM,cAAc,EAAE;MACrBmR,QAAQ,GAAGA,QAAQ,CAAC1W,MAAM,CAACyP,OAAO,IAAIA,OAAO,CAACtL,MAAM,KAAK,IAAI,CAACoB,cAAc,CAAC;;IAGjF;IACAmR,QAAQ,GAAGA,QAAQ,CAACI,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MAC9B,MAAMC,KAAK,GAAGF,CAAC,CAAChU,IAAI,EAAEqB,WAAW,EAAE,IAAI,EAAE;MACzC,MAAM8S,KAAK,GAAGF,CAAC,CAACjU,IAAI,EAAEqB,WAAW,EAAE,IAAI,EAAE;MACzC,OAAO6S,KAAK,CAACE,aAAa,CAACD,KAAK,CAAC;IACrC,CAAC,CAAC;IAEF,IAAI,CAAC5P,gBAAgB,GAAGoP,QAAQ;IAChC,IAAI,CAAC7V,UAAU,GAAG6V,QAAQ,CAAChW,MAAM;IACjC,IAAI,CAACkE,WAAW,GAAG,CAAC,CAAC,CAAC;IAEtB;IACA,IAAI,CAACwS,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;EAC3B;EAEA;EACQD,uBAAuBA,CAAA;IAC3B,IAAI,CAACvS,iBAAiB,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;IAC1C,IAAI,IAAI,CAAChE,UAAU,GAAG,GAAG,EAAE;MACvB,IAAI,CAACgE,iBAAiB,CAACkN,IAAI,CAAC,IAAI,CAAClR,UAAU,CAAC;;EAEpD;EAEA;EACAwW,gBAAgBA,CAAA;IACZ,MAAMC,UAAU,GAAG,IAAI,CAAC1S,WAAW,GAAG,IAAI,CAACD,YAAY;IACvD,MAAM4S,QAAQ,GAAGD,UAAU,GAAG,IAAI,CAAC3S,YAAY;IAC/C,IAAI,CAAC4B,iBAAiB,GAAG,IAAI,CAACe,gBAAgB,CAACkQ,KAAK,CAACF,UAAU,EAAEC,QAAQ,CAAC;EAC9E;EAEA;EACAvR,cAAcA,CAACiD,KAAY;IACvB,IAAI,CAACpD,UAAU,GAAIoD,KAAK,CAACsN,MAA2B,CAACjJ,KAAK;IAC1D,IAAI,CAAC+B,YAAY,EAAE;EACvB;EAEA;EACAjK,gBAAgBA,CAAC6D,KAAU;IACvB,IAAI,CAAChE,gBAAgB,GAAGgE,KAAK,CAACqE,KAAK,IAAI,EAAE;IACzC,IAAI,CAAC+B,YAAY,EAAE;EACvB;EAEA;EACA3J,cAAcA,CAACuD,KAAU;IACrB,IAAI,CAAC1D,cAAc,GAAG0D,KAAK,CAACqE,KAAK,IAAI,EAAE;IACvC,IAAI,CAAC+B,YAAY,EAAE;EACvB;EAEA;EACA5K,YAAYA,CAACwE,KAAU;IACnB,IAAI,CAACrE,WAAW,GAAGqE,KAAK,CAACwO,IAAI;IAC7B,IAAI,CAAC9S,YAAY,GAAGsE,KAAK,CAACyO,IAAI;IAC9B,IAAI,CAACL,gBAAgB,EAAE;EAC3B;EAEA;EACA3V,WAAWA,CAAA;IACP;IACA,IAAI,CAACiW,cAAc,EAAE;IACrB,IAAI,CAAC7O,gBAAgB,EAAE;EAC3B;EAEA;EACQ6O,cAAcA,CAAA;IAClB;IACA,IAAI,CAAC3P,iBAAiB,CAACyB,KAAK,EAAE;IAC9B,IAAI,CAACxB,eAAe,CAACwB,KAAK,EAAE;IAC5B,IAAI,CAACvB,cAAc,CAACuB,KAAK,EAAE;IAC3B,IAAI,CAACtB,yBAAyB,CAACsB,KAAK,EAAE;EAC1C;EAEA;EACAlI,YAAYA,CAAA;IACR,IAAI,CAACsE,UAAU,GAAG,EAAE;IACpB,IAAI,CAACZ,gBAAgB,GAAG,EAAE;IAC1B,IAAI,CAACM,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC8J,YAAY,EAAE;EACvB;EAEA;EACAuI,gBAAgBA,CAACnI,OAAgB;IAC7B,MAAMoI,SAAS,GAAG,IAAI,CAACpE,oBAAoB,CAAChE,OAAO,CAAC9M,EAAE,IAAI,EAAE,CAAC;IAC7D,OAAOkV,SAAS,GAAG,CAAC,IACZpI,OAAO,CAACqI,IAAI,IAAIrI,OAAO,CAACqI,IAAI,GAAG,CAAE,IACjCrI,OAAO,CAACsI,MAAM,IAAItI,OAAO,CAACsI,MAAM,GAAG,CAAE,IACrCtI,OAAO,CAACuI,GAAG,IAAIvI,OAAO,CAACuI,GAAG,GAAG,CAAE;EAC3C;EAEQlH,uBAAuBA,CAACF,IAAW;IACvC,MAAMjB,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMqI,WAAW,GAAGtI,GAAG,CAACqE,OAAO,EAAE;IAEjC,OAAOpD,IAAI,CAAC5Q,MAAM,CAACwT,IAAI,IAAG;MACtB,MAAM0E,YAAY,GAAG,IAAItI,IAAI,CAAC4D,IAAI,CAAC/B,QAAQ,CAAC;MAC5C,MAAM0G,QAAQ,GAAGD,YAAY,CAAClE,OAAO,EAAE;MAEvC;MACA,OAAOmE,QAAQ,IAAIF,WAAW;IAClC,CAAC,CAAC;EACN;EAGAG,OAAOA,CAAA;IACH,IAAI,CAACC,MAAM,GAAG,2FAA2F;EAC7G;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA7R,gBAAgBA,CAAC6O,KAAa,EAAE5F,OAAgB;IAC5C,OAAOA,OAAO,CAAC9M,EAAE,IAAI0S,KAAK,CAACnI,QAAQ,EAAE;EACzC;EAEA;EACA/J,2BAA2BA,CAACyG,SAAiB;IACzC,MAAM+F,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAM0I,WAAW,GAAG3I,GAAG,CAAC2F,QAAQ,EAAE;IAElC;IACA,IAAIgD,WAAW,IAAI,EAAE,IAAIA,WAAW,GAAG,CAAC,EAAE;MACtC,OAAO,kBAAkB;;IAG7B,MAAMC,aAAa,GAAG,IAAI,CAAC9U,oBAAoB,CAACmG,SAAS,CAAC;IAC1D,IAAI2O,aAAa,KAAK,GAAG,EAAE;MACvB,OAAO,iBAAiB,CAAC,CAAC;;;IAG9B,IAAI;MACA;MACA,MAAM,CAACC,QAAQ,EAAEC,QAAQ,CAAC,GAAGF,aAAa,CAACG,KAAK,CAAC,IAAI,CAAC;MACtD,MAAM,CAACrE,GAAG,EAAEC,KAAK,EAAEC,IAAI,CAAC,GAAGiE,QAAQ,CAACE,KAAK,CAAC,GAAG,CAAC,CAACnH,GAAG,CAACoH,MAAM,CAAC;MAC1D,MAAM,CAAC/L,KAAK,EAAEG,OAAO,CAAC,GAAG0L,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC,CAACnH,GAAG,CAACoH,MAAM,CAAC;MAExD,MAAMC,UAAU,GAAG,IAAIhJ,IAAI,CAAC2E,IAAI,EAAED,KAAK,GAAG,CAAC,EAAED,GAAG,EAAEzH,KAAK,EAAEG,OAAO,CAAC;MACjE,MAAM8L,SAAS,GAAG,CAAClJ,GAAG,CAACqE,OAAO,EAAE,GAAG4E,UAAU,CAAC5E,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAE3E7K,OAAO,CAACC,GAAG,CAAC,WAAWQ,SAAS,kBAAkB2O,aAAa,iBAAiBM,SAAS,CAACtL,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;MAEvG,IAAIsL,SAAS,IAAI,CAAC,EAAE;QAChB,OAAO,mBAAmB,CAAC,CAAC;OAC/B,MAAM,IAAIA,SAAS,IAAI,CAAC,EAAE;QACvB,OAAO,oBAAoB,CAAC,CAAC;OAChC,MAAM;QACH,OAAO,iBAAiB,CAAC,CAAC;;KAEjC,CAAC,OAAO/O,KAAK,EAAE;MACZX,OAAO,CAACW,KAAK,CAAC,kCAAkCF,SAAS,KAAK2O,aAAa,EAAE,EAAEzO,KAAK,CAAC;MACrF,OAAO,iBAAiB,CAAC,CAAC;;EAElC;EAEA;EACA+I,mBAAmBA,CAACjJ,SAAiB;IACjC,IAAI,CAACA,SAAS,EAAE;IAEhB,IAAI,CAACzC,cAAc,CAAC2R,oBAAoB,CAAClP,SAAS,CAAC,CAAClB,SAAS,CAAC;MAC1Da,IAAI,EAAGwP,QAAQ,IAAI;QACf,IAAI,CAAC3Q,aAAa,CAACuB,GAAG,CAACC,SAAS,EAAEmP,QAAQ,CAAC;QAE3C,IAAIA,QAAQ,IAAIA,QAAQ,CAACrY,MAAM,GAAG,CAAC,EAAE;UACjC;UACA,IAAIsY,iBAAiB,GAAG,CAAC;UACzB,IAAIC,SAAS,GAAG,KAAK;UAErBF,QAAQ,CAACrP,OAAO,CAACwP,OAAO,IAAG;YACvB,IAAI,CAAC/R,cAAc,CAACgS,cAAc,CAACD,OAAO,CAACvW,EAAE,CAAC,CAAC+F,SAAS,CAAC;cACrDa,IAAI,EAAGqH,IAAI,IAAI;gBACXsI,OAAO,CAACE,YAAY,GAAGxI,IAAI,CAACwI,YAAY;gBACxCF,OAAO,CAACG,SAAS,GAAGzI,IAAI,CAACyI,SAAS;gBAClCH,OAAO,CAACI,QAAQ,GAAG1I,IAAI,CAAC0I,QAAQ;gBAChCJ,OAAO,CAACK,YAAY,GAAG3I,IAAI,CAAC2I,YAAY;gBAExC;gBACA,MAAMC,oBAAoB,GAAG5I,IAAI,CAACwI,YAAY,EAAEK,IAAI,CAACL,YAAY,IAC7DA,YAAY,CAACjV,MAAM,IAAI,CAACiV,YAAY,CAACjV,MAAM,CAACC,WAAW,EAAE,CAACwS,QAAQ,CAAC,UAAU,CAAC,CACjF;gBAED,IAAI4C,oBAAoB,IAAI5I,IAAI,CAAC0I,QAAQ,EAAE;kBACvCL,SAAS,GAAG,IAAI;;gBAGpBD,iBAAiB,EAAE;gBACnB,IAAIA,iBAAiB,KAAKD,QAAQ,CAACrY,MAAM,EAAE;kBACvC;kBACA,MAAMgZ,kBAAkB,GAAG,IAAI,CAACC,2BAA2B,CAACZ,QAAQ,CAAC;kBACrE,IAAI,CAAC1Q,kBAAkB,CAACsB,GAAG,CAACC,SAAS,EAAGqP,SAAS,IAAIS,kBAAkB,GAAI,YAAY,GAAG,cAAc,CAAC;;cAEjH,CAAC;cACD5P,KAAK,EAAGA,KAAK,IAAI;gBACbX,OAAO,CAACW,KAAK,CAAC,0CAA0CF,SAAS,GAAG,EAAEE,KAAK,CAAC;gBAC5EmP,SAAS,GAAG,IAAI;gBAChBD,iBAAiB,EAAE;gBACnB,IAAIA,iBAAiB,KAAKD,QAAQ,CAACrY,MAAM,EAAE;kBACvC;kBACA,IAAI,CAAC2H,kBAAkB,CAACsB,GAAG,CAACC,SAAS,EAAE,YAAY,CAAC;;cAE5D;aACH,CAAC;UACN,CAAC,CAAC;SACL,MAAM;UACH;UACA,IAAI,CAACvB,kBAAkB,CAACsB,GAAG,CAACC,SAAS,EAAE,MAAM,CAAC;;MAEtD,CAAC;MACDE,KAAK,EAAGA,KAAK,IAAI;QACbX,OAAO,CAACW,KAAK,CAAC,sCAAsCF,SAAS,GAAG,EAAEE,KAAK,CAAC;QACxE,IAAI,CAACzB,kBAAkB,CAACsB,GAAG,CAACC,SAAS,EAAE,MAAM,CAAC;MAClD;KACH,CAAC;EACN;EAEAtG,eAAeA,CAACsG,SAAiB;IAC7B,MAAMmP,QAAQ,GAAG,IAAI,CAAC3Q,aAAa,CAAC8K,GAAG,CAACtJ,SAAS,CAAC;IAClD,IAAI,CAACmP,QAAQ,IAAIA,QAAQ,CAACrY,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO,GAAG;;IAGd;IACA,IAAIkZ,iBAAiB,GAAG,CAAC;IACzBb,QAAQ,CAACrP,OAAO,CAACwP,OAAO,IAAG;MACvB,IAAIA,OAAO,CAACE,YAAY,IAAIF,OAAO,CAACE,YAAY,CAAC1Y,MAAM,GAAG,CAAC,EAAE;QACzDkZ,iBAAiB,IAAIV,OAAO,CAACE,YAAY,CAAC1Y,MAAM;;IAExD,CAAC,CAAC;IAEF,OAAOkZ,iBAAiB,CAAC1M,QAAQ,EAAE;EACvC;EAEA;EACQyM,2BAA2BA,CAACZ,QAA2B;IAC3D,IAAI,CAACA,QAAQ,IAAIA,QAAQ,CAACrY,MAAM,KAAK,CAAC,EAAE;MACpC,OAAO,KAAK;;IAGhB;IACA,MAAMmZ,oBAAoB,GAAW,EAAE;IAEvCd,QAAQ,CAACrP,OAAO,CAACwP,OAAO,IAAG;MACvB,IAAIA,OAAO,CAACE,YAAY,IAAIF,OAAO,CAACE,YAAY,CAAC1Y,MAAM,GAAG,CAAC,EAAE;QACzDwY,OAAO,CAACE,YAAY,CAAC1P,OAAO,CAACkH,IAAI,IAAG;UAChC,IAAIA,IAAI,CAACkJ,YAAY,EAAE;YACnB,IAAI;cACA;cACA;cACA,MAAMC,SAAS,GAAGnJ,IAAI,CAACkJ,YAAY,CAACpB,KAAK,CAAC,GAAG,CAAC;cAC9C,IAAIqB,SAAS,CAACrZ,MAAM,IAAI,CAAC,EAAE;gBACvB,MAAMsZ,KAAK,GAAG,IAAIpK,IAAI,EAAE;gBACxB,MAAMqK,gBAAgB,GAAG,IAAIrK,IAAI,CAACoK,KAAK,CAAClK,WAAW,EAAE,EAAEkK,KAAK,CAACjK,QAAQ,EAAE,EAAEiK,KAAK,CAAChK,OAAO,EAAE,EACpFkK,QAAQ,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEG,QAAQ,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,EAAEG,QAAQ,CAACH,SAAS,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;gBAClFF,oBAAoB,CAAC9H,IAAI,CAACkI,gBAAgB,CAAC;;aAElD,CAAC,OAAOnQ,KAAK,EAAE;cACZX,OAAO,CAACW,KAAK,CAAC,kCAAkC,EAAE8G,IAAI,CAACkJ,YAAY,EAAEhQ,KAAK,CAAC;;;QAGvF,CAAC,CAAC;;IAEV,CAAC,CAAC;IAEF,IAAI+P,oBAAoB,CAACnZ,MAAM,KAAK,CAAC,EAAE;MACnC,OAAO,KAAK,CAAC,CAAC;;IAGlB;IACA,MAAMyZ,UAAU,GAAG,IAAIvK,IAAI,CAAC/C,IAAI,CAACc,GAAG,CAAC,GAAGkM,oBAAoB,CAACtI,GAAG,CAAC6I,IAAI,IAAIA,IAAI,CAACpG,OAAO,EAAE,CAAC,CAAC,CAAC;IAE1F;IACA,MAAMqG,iBAAiB,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAE1C,KAAK,MAAMD,IAAI,IAAIP,oBAAoB,EAAE;MACrC,MAAMS,cAAc,GAAGH,UAAU,CAACnG,OAAO,EAAE,GAAGoG,IAAI,CAACpG,OAAO,EAAE;MAC5D,IAAIsG,cAAc,GAAGD,iBAAiB,EAAE;QACpClR,OAAO,CAACC,GAAG,CAAC,wCAAwC+Q,UAAU,CAACI,YAAY,EAAE,cAAcH,IAAI,CAACG,YAAY,EAAE,iBAAiB1N,IAAI,CAACG,KAAK,CAACsN,cAAc,GAAG,KAAK,CAAC,UAAU,CAAC;QAC5K,OAAO,IAAI,CAAC,CAAC;;;;IAIrB,OAAO,KAAK,CAAC,CAAC;EAClB;;EAEAjX,qBAAqBA,CAACuG,SAAiB;IACnC,MAAMzF,MAAM,GAAG,IAAI,CAACkE,kBAAkB,CAAC6K,GAAG,CAACtJ,SAAS,CAAC;IAErD,QAAQzF,MAAM;MACV,KAAK,MAAM;QACP,OAAO,kBAAkB;MAAE;MAC/B,KAAK,cAAc;QACf,OAAO,mBAAmB;MAAE;MAChC,KAAK,YAAY;QACb,OAAO,iBAAiB;MAAE;MAC9B;QACI,OAAO,kBAAkB;MAAE;;EAEvC;;EAIAqW,WAAWA,CAAA;IACP,IAAI,IAAI,CAACjS,YAAY,EAAE;MACnB,IAAI,CAACA,YAAY,CAACkS,WAAW,EAAE;;EAEvC;EAAC,QAAAC,CAAA,G;qBAjvCQjU,cAAc,EAAAxG,EAAA,CAAA0a,iBAAA,CAAAC,EAAA,CAAAC,aAAA,GAAA5a,EAAA,CAAA0a,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAA9a,EAAA,CAAA0a,iBAAA,CAAAK,EAAA,CAAAC,gBAAA,GAAAhb,EAAA,CAAA0a,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAlb,EAAA,CAAA0a,iBAAA,CAAAS,EAAA,CAAAC,WAAA,GAAApb,EAAA,CAAA0a,iBAAA,CAAAW,EAAA,CAAAC,MAAA,GAAAtb,EAAA,CAAA0a,iBAAA,CAAAa,EAAA,CAAAC,YAAA,GAAAxb,EAAA,CAAA0a,iBAAA,CAAAe,EAAA,CAAAC,gBAAA,GAAA1b,EAAA,CAAA0a,iBAAA,CAAAiB,EAAA,CAAAC,cAAA,GAAA5b,EAAA,CAAA0a,iBAAA,CAAAmB,GAAA,CAAAC,2BAAA,GAAA9b,EAAA,CAAA0a,iBAAA,CAAA1a,EAAA,CAAA+b,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAdxV,cAAc;IAAAyV,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC1B3Bvc,EAAA,CAAAC,cAAA,aAAkB;QAIND,EAAA,CAAAa,UAAA,IAAA4b,qCAAA,0BAec;QAClBzc,EAAA,CAAAI,YAAA,EAAS;QAGbJ,EAAA,CAAAC,cAAA,aAAsC;QAE9BD,EAAA,CAAAa,UAAA,IAAA6b,qCAAA,0BAec;QAClB1c,EAAA,CAAAI,YAAA,EAAS;QAKbJ,EAAA,CAAAC,cAAA,aAAoB;QAEZD,EAAA,CAAAa,UAAA,IAAA8b,qCAAA,yBAuBc,KAAAC,sCAAA;QAoNlB5c,EAAA,CAAAI,YAAA,EAAS"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}