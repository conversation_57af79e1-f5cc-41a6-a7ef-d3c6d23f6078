import { Component } from '@angular/core';
import { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MessageService } from 'primeng/api';
import { LayoutService } from '../../../layout/service/app.layout.service';

import { GetStationsRequest, SaveUserProvidersRequest } from '../../api/requests';
import { IProvider, IUserProvider, IUserProviderConfiguration } from '../../api/responses';
import { Station } from '../../api/station';
import { CacheService } from '../../service/cache.service';
import { ProvidersService } from '../../service/providers.service';
import { StationsService } from '../../service/stations.service';
import { FaIconLibrary, FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@Component({
    selector: 'app-providers',
    templateUrl: './providers.component.html'
})
export class ProvidersComponent {

    availableProviders:IProvider[] = [];
    userProviders:IUserProviderConfiguration[] = [];
    userProvidersForm: FormGroup;
    stations: Station[] =[];
    tooltipVisible = false;
    editingProvider: IUserProviderConfiguration | null = null;
    editForm: FormGroup | null = null;

    constructor(public layoutService: LayoutService, 
        private stationsService: StationsService,
        private providersService: ProvidersService,
        private messageService: MessageService,
        private fb: FormBuilder,
        private cacheService: CacheService) {
        
        this.userProvidersForm = this.fb.group({
          providers: this.fb.array([])
        });

    }

    ngOnInit(){
        this.providersService.getProviders().then(data => {
            this.availableProviders = data;
          });

          this.getUserProviders();

          this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά
    }


    addProvider() {
        const providerGroup = this.fb.group({
          providerId: ['', Validators.required],
          username: ['', Validators.required],
          password: ['', Validators.required],
          portfolioId: [''],
          ftpUrl: [''],
          stations: [[]], // Αρχικά empty array για το multiSelect,
          selectedStation:['']
        });
        this.providers.push(providerGroup);

        // Παρακολούθηση του providerId
        providerGroup.get('providerId')?.valueChanges.subscribe((value) => {
          const portfolioIdControl = providerGroup.get('portfolioId');

          if (value === '4') {
            portfolioIdControl?.setValidators(Validators.required);
          } else {
            portfolioIdControl?.clearValidators();
          }

          portfolioIdControl?.updateValueAndValidity();
        });
      }
  
      get providers(): FormArray {
        return this.userProvidersForm.get('providers') as FormArray;
      }

      getSelectedProviderName(id:number): string | undefined {
        return this.availableProviders.find(p => p.id == id).name;
      }

      
    
  
  
      removeProvider(index: number) {
        this.providers.removeAt(index);
      }
  
      getStations(index: number) {
        const providerId = this.providers.at(index).get('providerId')?.value;
    
        if (!providerId) return;

        let request: GetStationsRequest = {
          providerId : this.providers.at(index).get('providerId')?.value,
          username: this.providers.at(index).get('username')?.value, 
          password: this.providers.at(index).get('password')?.value,
          portfolioId: this.providers.at(index).get('portfolioId')?.value,
          ftpUrl: this.providers.at(index).get('ftpUrl')?.value
        }
        console.log('Form Data:', request);
  
        this.stationsService.getStations(request).then(data => {
          this.providers.at(index).patchValue({ stations: data });
        });

        const providerGroup = this.providers.at(index);

        // Set validators based on provider type
        if (providerId === 4) {
          // Aurora provider - Portfolio ID is required
          providerGroup.get('portfolioId')?.setValidators(Validators.required);
          providerGroup.get('ftpUrl')?.clearValidators();
          providerGroup.get('ftpUrl')?.setValue('');
        } else if (providerId === 6) {
          // SMA provider - FTP URL is required
          providerGroup.get('ftpUrl')?.setValidators(Validators.required);
          providerGroup.get('portfolioId')?.clearValidators();
          providerGroup.get('portfolioId')?.setValue('');
        } else {
          // Other providers - clear both
          providerGroup.get('portfolioId')?.clearValidators();
          providerGroup.get('portfolioId')?.setValue('');
          providerGroup.get('ftpUrl')?.clearValidators();
          providerGroup.get('ftpUrl')?.setValue('');
        }

        providerGroup.get('portfolioId')?.updateValueAndValidity();
        providerGroup.get('ftpUrl')?.updateValueAndValidity();

      }
    
      onSubmit() {
        if (this.userProvidersForm.invalid) {
          this.userProvidersForm.markAllAsTouched();
          return;
        }
        if (this.userProvidersForm.valid) {
          console.log('Form Data (raw):', this.userProvidersForm.value.providers);

          // Convert providerId from number to string as expected by API
          const transformedProviders = this.userProvidersForm.value.providers.map((formProvider: any) => ({
            ...formProvider,
            providerId: formProvider.providerId.toString() // Convert number to string
          }));

          console.log('Form Data (transformed):', transformedProviders);

          let request: SaveUserProvidersRequest = {
            providers: transformedProviders
          };

          this.providersService.saveUserProviders(request).then(data => {
            console.log('Save response:', data);
            this.getUserProviders();
          }).catch(error => {
            console.error('Error saving providers:', error);
          });
        }
      }

      needsPortfolio(index:number): boolean{
        return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;
      }
  
      getUserProviders(){
        this.providersService.getUserProviders().then(data => {
          this.userProviders = data;
          console.log(this.userProviders)
          this.userProviders.forEach(up => {
            up.configuration = JSON.parse(up.configuration);

          })
          if (data.length > 0){
            this.stationsService.getUserStations().then(data => {
              this.stations = data;
              this.cacheService.setStations(this.stations);
            });
          }
        });
      }

      // Edit provider functionality
      editProvider(index: number) {
        const provider = this.userProviders[index];
        this.editingProvider = provider;

        // Create edit form with current values
        this.editForm = this.fb.group({
          providerId: [provider.providerId, Validators.required],
          username: [provider.configuration.Username, Validators.required],
          password: ['', Validators.required], // Always require new password for security
          portfolioId: [provider.configuration.PortfolioId || ''],
          ftpUrl: [provider.configuration.FtpUrl || ''],
          stations: [provider.configuration.Stations || []]
        });

        // Set validators based on provider type
        const providerId = parseInt(provider.providerId);
        if (providerId === 4) {
          this.editForm.get('portfolioId')?.setValidators(Validators.required);
          this.editForm.get('ftpUrl')?.clearValidators();
        } else if (providerId === 6) {
          this.editForm.get('ftpUrl')?.setValidators(Validators.required);
          this.editForm.get('portfolioId')?.clearValidators();
        } else {
          this.editForm.get('portfolioId')?.clearValidators();
          this.editForm.get('ftpUrl')?.clearValidators();
        }

        this.editForm.get('portfolioId')?.updateValueAndValidity();
        this.editForm.get('ftpUrl')?.updateValueAndValidity();
      }

      // Save edited provider
      saveEditedProvider() {
        if (!this.editForm || !this.editingProvider || this.editForm.invalid) {
          this.editForm?.markAllAsTouched();
          return;
        }

        const formValue = this.editForm.value;
        const configuration = {
          Username: formValue.username,
          Password: formValue.password,
          PortfolioId: formValue.portfolioId,
          FtpUrl: formValue.ftpUrl,
          Stations: formValue.stations
        };

        this.providersService.updateUserProvider(
          parseInt(this.editingProvider.providerId),
          JSON.stringify(configuration)
        ).then(() => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: 'Provider updated successfully'
          });
          this.cancelEdit();
          this.getUserProviders(); // Refresh the list
        }).catch(error => {
          console.error('Error updating provider:', error);
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to update provider'
          });
        });
      }

      // Cancel edit
      cancelEdit() {
        this.editingProvider = null;
        this.editForm = null;
      }

      // Remove provider functionality
      removeUserProvider(index: number) {
        const provider = this.userProviders[index];

        if (confirm(`Are you sure you want to remove the ${this.getSelectedProviderName(parseInt(provider.providerId))} provider?`)) {
          this.providersService.deleteUserProvider(parseInt(provider.providerId))
            .then(() => {
              this.messageService.add({
                severity: 'success',
                summary: 'Success',
                detail: 'Provider removed successfully'
              });
              this.getUserProviders(); // Refresh the list
            })
            .catch(error => {
              console.error('Error removing provider:', error);
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: 'Failed to remove provider'
              });
            });
        }
      }
    
}
