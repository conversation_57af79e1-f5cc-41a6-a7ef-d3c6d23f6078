{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./service/app.layout.service\";\nimport * as i2 from \"primeng/tooltip\";\nexport class AppFooterComponent {\n  constructor(layoutService) {\n    this.layoutService = layoutService;\n    this.currentYear = new Date().getFullYear();\n    this.appVersion = '1.0.0';\n  }\n  static #_ = this.ɵfac = function AppFooterComponent_Factory(t) {\n    return new (t || AppFooterComponent)(i0.ɵɵdirectiveInject(i1.LayoutService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: AppFooterComponent,\n    selectors: [[\"app-footer\"]],\n    decls: 37,\n    vars: 3,\n    consts: [[1, \"layout-footer\"], [1, \"footer-content\"], [1, \"footer-left\"], [1, \"footer-logo\"], [\"alt\", \"SolarKapital\", \"height\", \"24\", 1, \"footer-logo-img\", 3, \"src\"], [1, \"footer-description\"], [1, \"text-sm\", \"text-600\"], [1, \"footer-center\"], [1, \"footer-links\"], [\"href\", \"#\", 1, \"footer-link\"], [1, \"footer-separator\"], [1, \"footer-right\"], [1, \"footer-info\"], [1, \"text-xs\", \"text-500\"], [1, \"footer-social\"], [\"pTooltip\", \"GitHub\", \"tooltipPosition\", \"top\", 1, \"p-link\", \"footer-social-btn\"], [1, \"pi\", \"pi-github\"], [\"pTooltip\", \"LinkedIn\", \"tooltipPosition\", \"top\", 1, \"p-link\", \"footer-social-btn\"], [1, \"pi\", \"pi-linkedin\"], [\"pTooltip\", \"Twitter\", \"tooltipPosition\", \"top\", 1, \"p-link\", \"footer-social-btn\"], [1, \"pi\", \"pi-twitter\"]],\n    template: function AppFooterComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3);\n        i0.ɵɵelement(4, \"img\", 4);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 5)(6, \"span\", 6);\n        i0.ɵɵtext(7, \"Powering the future with solar energy management\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 7)(9, \"div\", 8)(10, \"a\", 9);\n        i0.ɵɵtext(11, \"Privacy Policy\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"span\", 10);\n        i0.ɵɵtext(13, \"\\u2022\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(14, \"a\", 9);\n        i0.ɵɵtext(15, \"Terms of Service\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(16, \"span\", 10);\n        i0.ɵɵtext(17, \"\\u2022\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"a\", 9);\n        i0.ɵɵtext(19, \"Support\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(20, \"span\", 10);\n        i0.ɵɵtext(21, \"\\u2022\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(22, \"a\", 9);\n        i0.ɵɵtext(23, \"Documentation\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(24, \"div\", 11)(25, \"div\", 12)(26, \"span\", 6);\n        i0.ɵɵtext(27);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"span\", 13);\n        i0.ɵɵtext(29);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(30, \"div\", 14)(31, \"button\", 15);\n        i0.ɵɵelement(32, \"i\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(33, \"button\", 17);\n        i0.ɵɵelement(34, \"i\", 18);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"button\", 19);\n        i0.ɵɵelement(36, \"i\", 20);\n        i0.ɵɵelementEnd()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵpropertyInterpolate1(\"src\", \"assets/layout/images/\", ctx.layoutService.config().colorScheme === \"light\" ? \"logo-dark\" : \"logo-white\", \".png\", i0.ɵɵsanitizeUrl);\n        i0.ɵɵadvance(23);\n        i0.ɵɵtextInterpolate1(\"\\u00A9 \", ctx.currentYear, \" SolarKapital\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\"Version \", ctx.appVersion, \"\");\n      }\n    },\n    dependencies: [i2.Tooltip],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["AppFooterComponent", "constructor", "layoutService", "currentYear", "Date", "getFullYear", "appVersion", "_", "i0", "ɵɵdirectiveInject", "i1", "LayoutService", "_2", "selectors", "decls", "vars", "consts", "template", "AppFooterComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵpropertyInterpolate1", "config", "colorScheme", "ɵɵsanitizeUrl", "ɵɵtextInterpolate1"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\layout\\app.footer.component.ts", "D:\\Greg\\solarkapital\\ui\\src\\app\\layout\\app.footer.component.html"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { LayoutService } from \"./service/app.layout.service\";\n\n@Component({\n    selector: 'app-footer',\n    templateUrl: './app.footer.component.html'\n})\nexport class AppFooterComponent {\n    currentYear: number = new Date().getFullYear();\n    appVersion: string = '1.0.0';\n\n    constructor(public layoutService: LayoutService) { }\n}\n", "<div class=\"layout-footer\">\n    <div class=\"footer-content\">\n        <!-- Left Section -->\n        <div class=\"footer-left\">\n            <div class=\"footer-logo\">\n                <img src=\"assets/layout/images/{{layoutService.config().colorScheme === 'light' ? 'logo-dark' : 'logo-white'}}.png\"\n                     alt=\"SolarKapital\"\n                     height=\"24\"\n                     class=\"footer-logo-img\"/>\n            </div>\n            <div class=\"footer-description\">\n                <span class=\"text-sm text-600\">Powering the future with solar energy management</span>\n            </div>\n        </div>\n\n        <!-- Center Section -->\n        <div class=\"footer-center\">\n            <div class=\"footer-links\">\n                <a href=\"#\" class=\"footer-link\">Privacy Policy</a>\n                <span class=\"footer-separator\">•</span>\n                <a href=\"#\" class=\"footer-link\">Terms of Service</a>\n                <span class=\"footer-separator\">•</span>\n                <a href=\"#\" class=\"footer-link\">Support</a>\n                <span class=\"footer-separator\">•</span>\n                <a href=\"#\" class=\"footer-link\">Documentation</a>\n            </div>\n        </div>\n\n        <!-- Right Section -->\n        <div class=\"footer-right\">\n            <div class=\"footer-info\">\n                <span class=\"text-sm text-600\">© {{ currentYear }} SolarKapital</span>\n                <span class=\"text-xs text-500\">Version {{ appVersion }}</span>\n            </div>\n            <div class=\"footer-social\">\n                <button class=\"p-link footer-social-btn\" pTooltip=\"GitHub\" tooltipPosition=\"top\">\n                    <i class=\"pi pi-github\"></i>\n                </button>\n                <button class=\"p-link footer-social-btn\" pTooltip=\"LinkedIn\" tooltipPosition=\"top\">\n                    <i class=\"pi pi-linkedin\"></i>\n                </button>\n                <button class=\"p-link footer-social-btn\" pTooltip=\"Twitter\" tooltipPosition=\"top\">\n                    <i class=\"pi pi-twitter\"></i>\n                </button>\n            </div>\n        </div>\n    </div>\n</div>\n"], "mappings": ";;;AAOA,OAAM,MAAOA,kBAAkB;EAI3BC,YAAmBC,aAA4B;IAA5B,KAAAA,aAAa,GAAbA,aAAa;IAHhC,KAAAC,WAAW,GAAW,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IAC9C,KAAAC,UAAU,GAAW,OAAO;EAEuB;EAAC,QAAAC,CAAA,G;qBAJ3CP,kBAAkB,EAAAQ,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAlBZ,kBAAkB;IAAAa,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCP/BX,EAAA,CAAAa,cAAA,aAA2B;QAKXb,EAAA,CAAAc,SAAA,aAG8B;QAClCd,EAAA,CAAAe,YAAA,EAAM;QACNf,EAAA,CAAAa,cAAA,aAAgC;QACGb,EAAA,CAAAgB,MAAA,uDAAgD;QAAAhB,EAAA,CAAAe,YAAA,EAAO;QAK9Ff,EAAA,CAAAa,cAAA,aAA2B;QAEab,EAAA,CAAAgB,MAAA,sBAAc;QAAAhB,EAAA,CAAAe,YAAA,EAAI;QAClDf,EAAA,CAAAa,cAAA,gBAA+B;QAAAb,EAAA,CAAAgB,MAAA,cAAC;QAAAhB,EAAA,CAAAe,YAAA,EAAO;QACvCf,EAAA,CAAAa,cAAA,YAAgC;QAAAb,EAAA,CAAAgB,MAAA,wBAAgB;QAAAhB,EAAA,CAAAe,YAAA,EAAI;QACpDf,EAAA,CAAAa,cAAA,gBAA+B;QAAAb,EAAA,CAAAgB,MAAA,cAAC;QAAAhB,EAAA,CAAAe,YAAA,EAAO;QACvCf,EAAA,CAAAa,cAAA,YAAgC;QAAAb,EAAA,CAAAgB,MAAA,eAAO;QAAAhB,EAAA,CAAAe,YAAA,EAAI;QAC3Cf,EAAA,CAAAa,cAAA,gBAA+B;QAAAb,EAAA,CAAAgB,MAAA,cAAC;QAAAhB,EAAA,CAAAe,YAAA,EAAO;QACvCf,EAAA,CAAAa,cAAA,YAAgC;QAAAb,EAAA,CAAAgB,MAAA,qBAAa;QAAAhB,EAAA,CAAAe,YAAA,EAAI;QAKzDf,EAAA,CAAAa,cAAA,eAA0B;QAEab,EAAA,CAAAgB,MAAA,IAAgC;QAAAhB,EAAA,CAAAe,YAAA,EAAO;QACtEf,EAAA,CAAAa,cAAA,gBAA+B;QAAAb,EAAA,CAAAgB,MAAA,IAAwB;QAAAhB,EAAA,CAAAe,YAAA,EAAO;QAElEf,EAAA,CAAAa,cAAA,eAA2B;QAEnBb,EAAA,CAAAc,SAAA,aAA4B;QAChCd,EAAA,CAAAe,YAAA,EAAS;QACTf,EAAA,CAAAa,cAAA,kBAAmF;QAC/Eb,EAAA,CAAAc,SAAA,aAA8B;QAClCd,EAAA,CAAAe,YAAA,EAAS;QACTf,EAAA,CAAAa,cAAA,kBAAkF;QAC9Eb,EAAA,CAAAc,SAAA,aAA6B;QACjCd,EAAA,CAAAe,YAAA,EAAS;;;QAtCJf,EAAA,CAAAiB,SAAA,GAA8G;QAA9GjB,EAAA,CAAAkB,sBAAA,iCAAAN,GAAA,CAAAlB,aAAA,CAAAyB,MAAA,GAAAC,WAAA,mDAAApB,EAAA,CAAAqB,aAAA,CAA8G;QA0BpFrB,EAAA,CAAAiB,SAAA,IAAgC;QAAhCjB,EAAA,CAAAsB,kBAAA,YAAAV,GAAA,CAAAjB,WAAA,kBAAgC;QAChCK,EAAA,CAAAiB,SAAA,GAAwB;QAAxBjB,EAAA,CAAAsB,kBAAA,aAAAV,GAAA,CAAAd,UAAA,KAAwB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}