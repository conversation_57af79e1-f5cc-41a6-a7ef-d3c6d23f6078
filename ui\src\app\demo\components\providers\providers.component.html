<div class="grid">
    <!-- Breadcrumb Section -->
    <div class="col-12">
        <p-breadcrumb [model]="[{ label: 'Providers' }]" [home]="{icon: 'pi pi-home'}"></p-breadcrumb>
    </div>

    <!-- Your Providers Section -->
    <div class="col-12">
        <p-card>
            <ng-template pTemplate="header">
                <div class="flex align-items-center gap-2 p-3">
                    <fa-icon icon="server" class="text-primary text-xl"></fa-icon>
                    <h2 class="text-2xl font-semibold m-0">Your Providers</h2>
                    <p-badge *ngIf="userProviders.length > 0" [value]="userProviders.length" severity="info"></p-badge>
                </div>
            </ng-template>

            <ng-template pTemplate="content">
                <!-- Empty State -->
                <div *ngIf="userProviders.length === 0" class="text-center py-6">
                    <fa-icon icon="inbox" class="text-6xl text-300 mb-3"></fa-icon>
                    <p-message severity="info" [closable]="false" class="w-full">
                        <ng-template pTemplate>
                            <div class="flex align-items-center gap-2">
                                <fa-icon icon="info-circle"></fa-icon>
                                <span>You are not registered to any provider. You can start adding providers now!</span>
                            </div>
                        </ng-template>
                    </p-message>
                </div>

                <!-- Providers List -->
                <div *ngIf="userProviders.length > 0" class="grid">
                    <div *ngFor="let provider of userProviders; let i = index" class="col-12 lg:col-6 xl:col-4">
                        <p-card class="h-full">
                            <ng-template pTemplate="header">
                                <div class="bg-primary-50 p-3 border-round-top">
                                    <div class="flex align-items-center justify-content-between">
                                        <p-tag [value]="getSelectedProviderName(provider.providerId)"
                                               severity="success"
                                               icon="pi pi-server">
                                        </p-tag>
                                        <p-badge value="Active" severity="success"></p-badge>
                                    </div>
                                </div>
                            </ng-template>

                            <ng-template pTemplate="content">
                                <div class="space-y-3">
                                    <!-- Username -->
                                    <div class="field">
                                        <label class="block text-sm font-medium text-600 mb-1">
                                            <fa-icon icon="user" class="mr-1"></fa-icon>
                                            Username
                                        </label>
                                        <div class="text-900 font-medium">{{ provider.configuration.Username }}</div>
                                    </div>

                                    <!-- Password (masked) -->
                                    <div class="field">
                                        <label class="block text-sm font-medium text-600 mb-1">
                                            <fa-icon icon="lock" class="mr-1"></fa-icon>
                                            Password
                                        </label>
                                        <div class="text-900 font-medium">••••••••</div>
                                    </div>

                                    <!-- Station -->
                                    <div *ngIf="provider.configuration.Stations" class="field">
                                        <label class="block text-sm font-medium text-600 mb-1">
                                            <fa-icon icon="solar-panel" class="mr-1"></fa-icon>
                                            Station
                                        </label>
                                        <div *ngIf="provider.configuration.Stations.length > 0" class="text-900 font-medium">
                                            {{ provider.configuration.Stations[0].StationName }}
                                        </div>
                                    </div>
                                </div>
                            </ng-template>

                            <ng-template pTemplate="footer">
                                <div class="flex gap-2">
                                    <p-button label="Edit"
                                              icon="pi pi-pencil"
                                              severity="warning"
                                              size="small"
                                              pTooltip="Edit this provider"
                                              (click)="editProvider(i)">
                                    </p-button>
                                    <p-button label="Remove"
                                              icon="pi pi-trash"
                                              severity="danger"
                                              size="small"
                                              pTooltip="Remove this provider"
                                              (click)="removeUserProvider(i)">
                                    </p-button>
                                </div>
                            </ng-template>
                        </p-card>
                    </div>
                </div>
            </ng-template>
        </p-card>
    </div>

    <!-- Add Providers Section -->
    <div class="col-12">
        <p-card>
            <ng-template pTemplate="header">
                <div class="flex align-items-center gap-2 p-3">
                    <fa-icon icon="plus-circle" class="text-primary text-xl"></fa-icon>
                    <h2 class="text-2xl font-semibold m-0">Add New Providers</h2>
                </div>
            </ng-template>

            <ng-template pTemplate="content">
                <!-- Instructions Panel -->
                <div class="bg-blue-50 border-l-4 border-blue-400 p-4 border-round mb-4">
                    <div class="flex align-items-start gap-3">
                        <fa-icon icon="info-circle" class="text-blue-600 text-xl mt-1"></fa-icon>
                        <div class="flex-1">
                            <h4 class="text-blue-800 font-semibold mb-3 mt-0">Form Instructions</h4>
                            <ul class="list-none p-0 m-0 text-blue-700">
                                <li class="flex align-items-start gap-2 mb-2">
                                    <fa-icon icon="asterisk" class="text-red-500 text-xs mt-1 flex-shrink-0"></fa-icon>
                                    <span>Fill in all required fields marked with an asterisk (*).</span>
                                </li>
                                <li class="flex align-items-start gap-2 mb-2">
                                    <fa-icon icon="lightbulb" class="text-yellow-600 text-sm mt-1 flex-shrink-0"></fa-icon>
                                    <span>
                                        If you select Provider <strong>Aurora</strong>, the Portfolio ID field becomes required.
                                        You can find it in the Aurora Portal.
                                        <fa-icon icon="question-circle"
                                                 class="cursor-pointer text-blue-600 ml-1"
                                                 pTooltip="Click to see Aurora Portal example"
                                                 tooltipPosition="top">
                                        </fa-icon>
                                    </span>
                                </li>
                                <li class="flex align-items-start gap-2 mb-2">
                                    <fa-icon icon="globe" class="text-blue-600 text-sm mt-1 flex-shrink-0"></fa-icon>
                                    <span>
                                        If you select Provider <strong>SMA</strong>, the FTP Url becomes required.
                                        It should be like <em>ftp://ftp.server.com/</em>.
                                        Please use the full path where the stations folders are located.
                                    </span>
                                </li>
                                <li class="flex align-items-start gap-2">
                                    <fa-icon icon="check-circle" class="text-green-600 text-sm mt-1 flex-shrink-0"></fa-icon>
                                    <span>Make sure to select one station from the available list.</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>



                <!-- Dynamic Form -->
                <form [formGroup]="userProvidersForm" (ngSubmit)="onSubmit()">
                    <div formArrayName="providers">
                        <div *ngFor="let provider of providers.controls; let i = index" [formGroupName]="i" class="mb-4">
                            <p-card>
                                <ng-template pTemplate="header">
                                    <div class="flex align-items-center justify-content-between p-3 bg-primary-50">
                                        <div class="flex align-items-center gap-2">
                                            <fa-icon icon="cog" class="text-primary"></fa-icon>
                                            <span class="font-semibold">Provider Configuration {{ i + 1 }}</span>
                                        </div>
                                        <p-button icon="pi pi-times"
                                                  severity="danger"
                                                  size="small"
                                                  [text]="true"
                                                  pTooltip="Remove this provider"
                                                  (click)="removeProvider(i)">
                                        </p-button>
                                    </div>
                                </ng-template>

                                <ng-template pTemplate="content">
                                    <!-- Main Fields Row (for large screens) -->
                                    <div class="grid">
                                        <!-- Provider Selection -->
                                        <div class="col-12 md:col-6 lg:col-3 xl:col-2">
                                            <div class="field">
                                                <label for="provider-{{i}}" class="block font-medium mb-2 text-sm">
                                                    <fa-icon icon="server" class="mr-1"></fa-icon>
                                                    Provider *
                                                </label>
                                                <p-dropdown id="provider-{{i}}"
                                                           formControlName="providerId"
                                                           [options]="availableProviders"
                                                           optionLabel="name"
                                                           optionValue="id"
                                                           placeholder="Select provider"
                                                           class="w-full"
                                                           [showClear]="true">
                                                </p-dropdown>
                                            </div>
                                        </div>

                                        <!-- Username -->
                                        <div class="col-12 md:col-6 lg:col-3 xl:col-2">
                                            <div class="field">
                                                <label for="username-{{i}}" class="block font-medium mb-2 text-sm">
                                                    <fa-icon icon="user" class="mr-1"></fa-icon>
                                                    Username *
                                                </label>
                                                <input pInputText
                                                       id="username-{{i}}"
                                                       formControlName="username"
                                                       placeholder="Username"
                                                       class="w-full">
                                            </div>
                                        </div>

                                        <!-- Password -->
                                        <div class="col-12 md:col-6 lg:col-3 xl:col-2">
                                            <div class="field">
                                                <label for="password-{{i}}" class="block font-medium mb-2 text-sm">
                                                    <fa-icon icon="lock" class="mr-1"></fa-icon>
                                                    Password *
                                                </label>
                                                <p-password id="password-{{i}}"
                                                           formControlName="password"
                                                           placeholder="Password"
                                                           class="w-full"
                                                           [toggleMask]="true"
                                                           [feedback]="false">
                                                </p-password>
                                            </div>
                                        </div>

                                        <!-- Portfolio ID (Aurora only) -->
                                        <div *ngIf="provider.get('providerId')?.value == 4" class="col-12 md:col-6 lg:col-3 xl:col-2">
                                            <div class="field">
                                                <label for="portfolio-{{i}}" class="block font-medium mb-2 text-sm">
                                                    <fa-icon icon="briefcase" class="mr-1"></fa-icon>
                                                    Portfolio ID *
                                                </label>
                                                <input pInputText
                                                       id="portfolio-{{i}}"
                                                       formControlName="portfolioId"
                                                       placeholder="Portfolio ID"
                                                       class="w-full">
                                            </div>
                                        </div>

                                        <!-- FTP URL (SMA only) -->
                                        <div *ngIf="provider.get('providerId')?.value == 6" class="col-12 md:col-6 lg:col-3 xl:col-3">
                                            <div class="field">
                                                <label for="ftp-{{i}}" class="block font-medium mb-2 text-sm">
                                                    <fa-icon icon="globe" class="mr-1"></fa-icon>
                                                    FTP URL *
                                                </label>
                                                <input pInputText
                                                       id="ftp-{{i}}"
                                                       formControlName="ftpUrl"
                                                       placeholder="ftp://ftp.server.com/"
                                                       class="w-full">
                                            </div>
                                        </div>

                                        <!-- Station Selection -->
                                        <div *ngIf="provider.get('stations')?.value.length > 0" class="col-12 md:col-6 lg:col-3 xl:col-4">
                                            <div class="field">
                                                <label for="station-{{i}}" class="block font-medium mb-2 text-sm">
                                                    <fa-icon icon="solar-panel" class="mr-1"></fa-icon>
                                                    Station *
                                                </label>
                                                <p-dropdown id="station-{{i}}"
                                                           formControlName="selectedStation"
                                                           [options]="provider.get('stations')?.value"
                                                           optionLabel="name"
                                                           optionValue="id"
                                                           placeholder="Select station"
                                                           class="w-full"
                                                           [showClear]="true">
                                                </p-dropdown>
                                            </div>
                                        </div>
                                    </div>
                                </ng-template>

                                <ng-template pTemplate="footer">
                                    <div class="flex gap-2">
                                        <p-button label="Get Stations"
                                                  icon="pi pi-download"
                                                  severity="info"
                                                  size="small"
                                                  *ngIf="provider.get('providerId')?.value && userProvidersForm.valid"
                                                  (click)="getStations(i)">
                                        </p-button>
                                    </div>
                                </ng-template>
                            </p-card>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex gap-3 mt-4">
                        <p-button label="Add Provider"
                                  icon="pi pi-plus"
                                  severity="secondary"
                                  (click)="addProvider()">
                        </p-button>

                        <p-button label="Save All Providers"
                                  icon="pi pi-save"
                                  severity="success"
                                  type="submit"
                                  *ngIf="userProvidersForm.valid">
                        </p-button>
                    </div>
                </form>
            </ng-template>
        </p-card>
    </div>
</div>

<!-- Edit Provider Dialog -->
<p-dialog header="Edit Provider"
          [(visible)]="editingProvider !== null"
          [modal]="true"
          [style]="{width: '50vw'}"
          [closable]="true"
          (onHide)="cancelEdit()">

    <form *ngIf="editForm" [formGroup]="editForm" (ngSubmit)="saveEditedProvider()">
        <div class="grid">
            <!-- Provider Selection -->
            <div class="col-12">
                <label for="editProviderId" class="block text-900 font-medium mb-2">Provider *</label>
                <p-dropdown id="editProviderId"
                           formControlName="providerId"
                           [options]="availableProviders"
                           optionLabel="name"
                           optionValue="id"
                           placeholder="Select a provider"
                           [disabled]="true"
                           class="w-full">
                </p-dropdown>
            </div>

            <!-- Username -->
            <div class="col-12">
                <label for="editUsername" class="block text-900 font-medium mb-2">Username *</label>
                <input id="editUsername"
                       type="text"
                       pInputText
                       formControlName="username"
                       class="w-full"
                       placeholder="Enter username" />
                <small class="p-error" *ngIf="editForm.get('username')?.invalid && editForm.get('username')?.touched">
                    Username is required
                </small>
            </div>

            <!-- Password -->
            <div class="col-12">
                <label for="editPassword" class="block text-900 font-medium mb-2">New Password *</label>
                <p-password id="editPassword"
                           formControlName="password"
                           [toggleMask]="true"
                           placeholder="Enter new password"
                           styleClass="w-full">
                </p-password>
                <small class="p-error" *ngIf="editForm.get('password')?.invalid && editForm.get('password')?.touched">
                    Password is required
                </small>
            </div>

            <!-- Portfolio ID (for Aurora) -->
            <div class="col-12" *ngIf="editForm.get('providerId')?.value === 4">
                <label for="editPortfolioId" class="block text-900 font-medium mb-2">Portfolio ID *</label>
                <input id="editPortfolioId"
                       type="text"
                       pInputText
                       formControlName="portfolioId"
                       class="w-full"
                       placeholder="Enter portfolio ID" />
                <small class="p-error" *ngIf="editForm.get('portfolioId')?.invalid && editForm.get('portfolioId')?.touched">
                    Portfolio ID is required for Aurora provider
                </small>
            </div>

            <!-- FTP URL (for SMA) -->
            <div class="col-12" *ngIf="editForm.get('providerId')?.value === 6">
                <label for="editFtpUrl" class="block text-900 font-medium mb-2">FTP URL *</label>
                <input id="editFtpUrl"
                       type="text"
                       pInputText
                       formControlName="ftpUrl"
                       class="w-full"
                       placeholder="Enter FTP URL" />
                <small class="p-error" *ngIf="editForm.get('ftpUrl')?.invalid && editForm.get('ftpUrl')?.touched">
                    FTP URL is required for SMA provider
                </small>
            </div>
        </div>
    </form>

    <ng-template pTemplate="footer">
        <div class="flex gap-2 justify-content-end">
            <p-button label="Cancel"
                      severity="secondary"
                      (click)="cancelEdit()">
            </p-button>
            <p-button label="Save"
                      severity="primary"
                      (click)="saveEditedProvider()"
                      [disabled]="!editForm?.valid">
            </p-button>
        </div>
    </ng-template>
</p-dialog>