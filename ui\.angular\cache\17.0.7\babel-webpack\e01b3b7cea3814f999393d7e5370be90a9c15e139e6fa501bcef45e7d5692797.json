{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { Validators } from '@angular/forms';\nlet ProvidersComponent = class ProvidersComponent {\n  constructor(layoutService, stationsService, providersService, messageService, fb, cacheService) {\n    this.layoutService = layoutService;\n    this.stationsService = stationsService;\n    this.providersService = providersService;\n    this.messageService = messageService;\n    this.fb = fb;\n    this.cacheService = cacheService;\n    this.availableProviders = [];\n    this.userProviders = [];\n    this.stations = [];\n    this.tooltipVisible = false;\n    this.editingProvider = null;\n    this.editForm = null;\n    this.userProvidersForm = this.fb.group({\n      providers: this.fb.array([])\n    });\n  }\n  ngOnInit() {\n    this.providersService.getProviders().then(data => {\n      this.availableProviders = data;\n    });\n    this.getUserProviders();\n    this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\n  }\n\n  addProvider() {\n    const providerGroup = this.fb.group({\n      providerId: ['', Validators.required],\n      username: ['', Validators.required],\n      password: ['', Validators.required],\n      portfolioId: [''],\n      ftpUrl: [''],\n      stations: [[]],\n      selectedStation: ['']\n    });\n    this.providers.push(providerGroup);\n    // Παρακολούθηση του providerId\n    providerGroup.get('providerId')?.valueChanges.subscribe(value => {\n      const portfolioIdControl = providerGroup.get('portfolioId');\n      if (value === '4') {\n        portfolioIdControl?.setValidators(Validators.required);\n      } else {\n        portfolioIdControl?.clearValidators();\n      }\n      portfolioIdControl?.updateValueAndValidity();\n    });\n  }\n  get providers() {\n    return this.userProvidersForm.get('providers');\n  }\n  getSelectedProviderName(id) {\n    return this.availableProviders.find(p => p.id == id).name;\n  }\n  removeProvider(index) {\n    this.providers.removeAt(index);\n  }\n  getStations(index) {\n    const providerId = this.providers.at(index).get('providerId')?.value;\n    if (!providerId) return;\n    let request = {\n      providerId: this.providers.at(index).get('providerId')?.value,\n      username: this.providers.at(index).get('username')?.value,\n      password: this.providers.at(index).get('password')?.value,\n      portfolioId: this.providers.at(index).get('portfolioId')?.value,\n      ftpUrl: this.providers.at(index).get('ftpUrl')?.value\n    };\n    console.log('Form Data:', request);\n    this.stationsService.getStations(request).then(data => {\n      this.providers.at(index).patchValue({\n        stations: data\n      });\n    });\n    const providerGroup = this.providers.at(index);\n    // Set validators based on provider type\n    if (providerId === 4) {\n      // Aurora provider - Portfolio ID is required\n      providerGroup.get('portfolioId')?.setValidators(Validators.required);\n      providerGroup.get('ftpUrl')?.clearValidators();\n      providerGroup.get('ftpUrl')?.setValue('');\n    } else if (providerId === 6) {\n      // SMA provider - FTP URL is required\n      providerGroup.get('ftpUrl')?.setValidators(Validators.required);\n      providerGroup.get('portfolioId')?.clearValidators();\n      providerGroup.get('portfolioId')?.setValue('');\n    } else {\n      // Other providers - clear both\n      providerGroup.get('portfolioId')?.clearValidators();\n      providerGroup.get('portfolioId')?.setValue('');\n      providerGroup.get('ftpUrl')?.clearValidators();\n      providerGroup.get('ftpUrl')?.setValue('');\n    }\n    providerGroup.get('portfolioId')?.updateValueAndValidity();\n    providerGroup.get('ftpUrl')?.updateValueAndValidity();\n  }\n  onSubmit() {\n    if (this.userProvidersForm.invalid) {\n      this.userProvidersForm.markAllAsTouched();\n      return;\n    }\n    if (this.userProvidersForm.valid) {\n      console.log('Form Data (raw):', this.userProvidersForm.value.providers);\n      // Convert providerId from number to string as expected by API\n      const transformedProviders = this.userProvidersForm.value.providers.map(formProvider => ({\n        ...formProvider,\n        providerId: formProvider.providerId.toString() // Convert number to string\n      }));\n\n      console.log('Form Data (transformed):', transformedProviders);\n      let request = {\n        providers: transformedProviders\n      };\n      this.providersService.saveUserProviders(request).then(data => {\n        console.log('Save response:', data);\n        this.getUserProviders();\n      }).catch(error => {\n        console.error('Error saving providers:', error);\n      });\n    }\n  }\n  needsPortfolio(index) {\n    return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;\n  }\n  getUserProviders() {\n    this.providersService.getUserProviders().then(data => {\n      this.userProviders = data;\n      console.log(this.userProviders);\n      this.userProviders.forEach(up => {\n        up.configuration = JSON.parse(up.configuration);\n      });\n      if (data.length > 0) {\n        this.stationsService.getUserStations().then(data => {\n          this.stations = data;\n          this.cacheService.setStations(this.stations);\n        });\n      }\n    });\n  }\n  // Edit provider functionality\n  editProvider(index) {\n    const provider = this.userProviders[index];\n    this.editingProvider = provider;\n    // Create edit form with current values\n    this.editForm = this.fb.group({\n      providerId: [provider.providerId, Validators.required],\n      username: [provider.configuration.Username, Validators.required],\n      password: ['', Validators.required],\n      portfolioId: [provider.configuration.PortfolioId || ''],\n      ftpUrl: [provider.configuration.FtpUrl || ''],\n      stations: [provider.configuration.Stations || []]\n    });\n    // Set validators based on provider type\n    const providerId = parseInt(provider.providerId);\n    if (providerId === 4) {\n      this.editForm.get('portfolioId')?.setValidators(Validators.required);\n      this.editForm.get('ftpUrl')?.clearValidators();\n    } else if (providerId === 6) {\n      this.editForm.get('ftpUrl')?.setValidators(Validators.required);\n      this.editForm.get('portfolioId')?.clearValidators();\n    } else {\n      this.editForm.get('portfolioId')?.clearValidators();\n      this.editForm.get('ftpUrl')?.clearValidators();\n    }\n    this.editForm.get('portfolioId')?.updateValueAndValidity();\n    this.editForm.get('ftpUrl')?.updateValueAndValidity();\n  }\n  // Save edited provider\n  saveEditedProvider() {\n    if (!this.editForm || !this.editingProvider || this.editForm.invalid) {\n      this.editForm?.markAllAsTouched();\n      return;\n    }\n    const formValue = this.editForm.value;\n    const configuration = {\n      Username: formValue.username,\n      Password: formValue.password,\n      PortfolioId: formValue.portfolioId,\n      FtpUrl: formValue.ftpUrl,\n      Stations: formValue.stations\n    };\n    this.providersService.updateUserProvider(parseInt(this.editingProvider.providerId), JSON.stringify(configuration)).then(() => {\n      this.messageService.add({\n        severity: 'success',\n        summary: 'Success',\n        detail: 'Provider updated successfully'\n      });\n      this.cancelEdit();\n      this.getUserProviders(); // Refresh the list\n    }).catch(error => {\n      console.error('Error updating provider:', error);\n      this.messageService.add({\n        severity: 'error',\n        summary: 'Error',\n        detail: 'Failed to update provider'\n      });\n    });\n  }\n  // Cancel edit\n  cancelEdit() {\n    this.editingProvider = null;\n    this.editForm = null;\n  }\n  // Remove provider functionality\n  removeUserProvider(index) {\n    const provider = this.userProviders[index];\n    if (confirm(`Are you sure you want to remove the ${this.getSelectedProviderName(parseInt(provider.providerId))} provider?`)) {\n      this.providersService.deleteUserProvider(parseInt(provider.providerId)).then(() => {\n        this.messageService.add({\n          severity: 'success',\n          summary: 'Success',\n          detail: 'Provider removed successfully'\n        });\n        this.getUserProviders(); // Refresh the list\n      }).catch(error => {\n        console.error('Error removing provider:', error);\n        this.messageService.add({\n          severity: 'error',\n          summary: 'Error',\n          detail: 'Failed to remove provider'\n        });\n      });\n    }\n  }\n};\nProvidersComponent = __decorate([Component({\n  selector: 'app-providers',\n  templateUrl: './providers.component.html'\n})], ProvidersComponent);\nexport { ProvidersComponent };", "map": {"version": 3, "names": ["Component", "Validators", "ProvidersComponent", "constructor", "layoutService", "stationsService", "providersService", "messageService", "fb", "cacheService", "availableProviders", "userProviders", "stations", "tooltipVisible", "editing<PERSON>rovider", "editForm", "userProvidersForm", "group", "providers", "array", "ngOnInit", "getProviders", "then", "data", "getUserProviders", "addProvider", "providerGroup", "providerId", "required", "username", "password", "portfolioId", "ftpUrl", "selectedStation", "push", "get", "valueChanges", "subscribe", "value", "portfolioIdControl", "setValidators", "clearValidators", "updateValueAndValidity", "getSelectedProviderName", "id", "find", "p", "name", "removeProvider", "index", "removeAt", "getStations", "at", "request", "console", "log", "patchValue", "setValue", "onSubmit", "invalid", "mark<PERSON>llAsTouched", "valid", "transformedProviders", "map", "formProvider", "toString", "saveUserProviders", "catch", "error", "needsPortfolio", "length", "for<PERSON>ach", "up", "configuration", "JSON", "parse", "getUserStations", "setStations", "edit<PERSON><PERSON><PERSON>", "provider", "Username", "PortfolioId", "FtpUrl", "Stations", "parseInt", "saveEditedProvider", "formValue", "Password", "updateUserProvider", "stringify", "add", "severity", "summary", "detail", "cancelEdit", "removeUserProvider", "confirm", "deleteUserProvider", "__decorate", "selector", "templateUrl"], "sources": ["D:\\Greg\\solarkapital\\ui\\src\\app\\demo\\components\\providers\\providers.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\r\nimport { Form<PERSON>rray, FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { Router } from '@angular/router';\r\nimport { MessageService } from 'primeng/api';\r\nimport { LayoutService } from '../../../layout/service/app.layout.service';\r\n\r\nimport { GetStationsRequest, SaveUserProvidersRequest } from '../../api/requests';\r\nimport { IProvider, IUserProvider, IUserProviderConfiguration } from '../../api/responses';\r\nimport { Station } from '../../api/station';\r\nimport { CacheService } from '../../service/cache.service';\r\nimport { ProvidersService } from '../../service/providers.service';\r\nimport { StationsService } from '../../service/stations.service';\r\nimport { FaIconLibrary, FontAwesomeModule } from '@fortawesome/angular-fontawesome';\r\n\r\n@Component({\r\n    selector: 'app-providers',\r\n    templateUrl: './providers.component.html'\r\n})\r\nexport class ProvidersComponent {\r\n\r\n    availableProviders:IProvider[] = [];\r\n    userProviders:IUserProviderConfiguration[] = [];\r\n    userProvidersForm: FormGroup;\r\n    stations: Station[] =[];\r\n    tooltipVisible = false;\r\n    editingProvider: IUserProviderConfiguration | null = null;\r\n    editForm: FormGroup | null = null;\r\n\r\n    constructor(public layoutService: LayoutService, \r\n        private stationsService: StationsService,\r\n        private providersService: ProvidersService,\r\n        private messageService: MessageService,\r\n        private fb: FormBuilder,\r\n        private cacheService: CacheService) {\r\n        \r\n        this.userProvidersForm = this.fb.group({\r\n          providers: this.fb.array([])\r\n        });\r\n\r\n    }\r\n\r\n    ngOnInit(){\r\n        this.providersService.getProviders().then(data => {\r\n            this.availableProviders = data;\r\n          });\r\n\r\n          this.getUserProviders();\r\n\r\n          this.addProvider(); // Ξεκινάμε με μία προεπιλεγμένη σειρά\r\n    }\r\n\r\n\r\n    addProvider() {\r\n        const providerGroup = this.fb.group({\r\n          providerId: ['', Validators.required],\r\n          username: ['', Validators.required],\r\n          password: ['', Validators.required],\r\n          portfolioId: [''],\r\n          ftpUrl: [''],\r\n          stations: [[]], // Αρχικά empty array για το multiSelect,\r\n          selectedStation:['']\r\n        });\r\n        this.providers.push(providerGroup);\r\n\r\n        // Παρακολούθηση του providerId\r\n        providerGroup.get('providerId')?.valueChanges.subscribe((value) => {\r\n          const portfolioIdControl = providerGroup.get('portfolioId');\r\n\r\n          if (value === '4') {\r\n            portfolioIdControl?.setValidators(Validators.required);\r\n          } else {\r\n            portfolioIdControl?.clearValidators();\r\n          }\r\n\r\n          portfolioIdControl?.updateValueAndValidity();\r\n        });\r\n      }\r\n  \r\n      get providers(): FormArray {\r\n        return this.userProvidersForm.get('providers') as FormArray;\r\n      }\r\n\r\n      getSelectedProviderName(id:number): string | undefined {\r\n        return this.availableProviders.find(p => p.id == id).name;\r\n      }\r\n\r\n      \r\n    \r\n  \r\n  \r\n      removeProvider(index: number) {\r\n        this.providers.removeAt(index);\r\n      }\r\n  \r\n      getStations(index: number) {\r\n        const providerId = this.providers.at(index).get('providerId')?.value;\r\n    \r\n        if (!providerId) return;\r\n\r\n        let request: GetStationsRequest = {\r\n          providerId : this.providers.at(index).get('providerId')?.value,\r\n          username: this.providers.at(index).get('username')?.value, \r\n          password: this.providers.at(index).get('password')?.value,\r\n          portfolioId: this.providers.at(index).get('portfolioId')?.value,\r\n          ftpUrl: this.providers.at(index).get('ftpUrl')?.value\r\n        }\r\n        console.log('Form Data:', request);\r\n  \r\n        this.stationsService.getStations(request).then(data => {\r\n          this.providers.at(index).patchValue({ stations: data });\r\n        });\r\n\r\n        const providerGroup = this.providers.at(index);\r\n\r\n        // Set validators based on provider type\r\n        if (providerId === 4) {\r\n          // Aurora provider - Portfolio ID is required\r\n          providerGroup.get('portfolioId')?.setValidators(Validators.required);\r\n          providerGroup.get('ftpUrl')?.clearValidators();\r\n          providerGroup.get('ftpUrl')?.setValue('');\r\n        } else if (providerId === 6) {\r\n          // SMA provider - FTP URL is required\r\n          providerGroup.get('ftpUrl')?.setValidators(Validators.required);\r\n          providerGroup.get('portfolioId')?.clearValidators();\r\n          providerGroup.get('portfolioId')?.setValue('');\r\n        } else {\r\n          // Other providers - clear both\r\n          providerGroup.get('portfolioId')?.clearValidators();\r\n          providerGroup.get('portfolioId')?.setValue('');\r\n          providerGroup.get('ftpUrl')?.clearValidators();\r\n          providerGroup.get('ftpUrl')?.setValue('');\r\n        }\r\n\r\n        providerGroup.get('portfolioId')?.updateValueAndValidity();\r\n        providerGroup.get('ftpUrl')?.updateValueAndValidity();\r\n\r\n      }\r\n    \r\n      onSubmit() {\r\n        if (this.userProvidersForm.invalid) {\r\n          this.userProvidersForm.markAllAsTouched();\r\n          return;\r\n        }\r\n        if (this.userProvidersForm.valid) {\r\n          console.log('Form Data (raw):', this.userProvidersForm.value.providers);\r\n\r\n          // Convert providerId from number to string as expected by API\r\n          const transformedProviders = this.userProvidersForm.value.providers.map((formProvider: any) => ({\r\n            ...formProvider,\r\n            providerId: formProvider.providerId.toString() // Convert number to string\r\n          }));\r\n\r\n          console.log('Form Data (transformed):', transformedProviders);\r\n\r\n          let request: SaveUserProvidersRequest = {\r\n            providers: transformedProviders\r\n          };\r\n\r\n          this.providersService.saveUserProviders(request).then(data => {\r\n            console.log('Save response:', data);\r\n            this.getUserProviders();\r\n          }).catch(error => {\r\n            console.error('Error saving providers:', error);\r\n          });\r\n        }\r\n      }\r\n\r\n      needsPortfolio(index:number): boolean{\r\n        return this.providers && this.providers.length > index && this.providers.at(index).get('providerId')?.value === 4;\r\n      }\r\n  \r\n      getUserProviders(){\r\n        this.providersService.getUserProviders().then(data => {\r\n          this.userProviders = data;\r\n          console.log(this.userProviders)\r\n          this.userProviders.forEach(up => {\r\n            up.configuration = JSON.parse(up.configuration);\r\n\r\n          })\r\n          if (data.length > 0){\r\n            this.stationsService.getUserStations().then(data => {\r\n              this.stations = data;\r\n              this.cacheService.setStations(this.stations);\r\n            });\r\n          }\r\n        });\r\n      }\r\n\r\n      // Edit provider functionality\r\n      editProvider(index: number) {\r\n        const provider = this.userProviders[index];\r\n        this.editingProvider = provider;\r\n\r\n        // Create edit form with current values\r\n        this.editForm = this.fb.group({\r\n          providerId: [provider.providerId, Validators.required],\r\n          username: [provider.configuration.Username, Validators.required],\r\n          password: ['', Validators.required], // Always require new password for security\r\n          portfolioId: [provider.configuration.PortfolioId || ''],\r\n          ftpUrl: [provider.configuration.FtpUrl || ''],\r\n          stations: [provider.configuration.Stations || []]\r\n        });\r\n\r\n        // Set validators based on provider type\r\n        const providerId = parseInt(provider.providerId);\r\n        if (providerId === 4) {\r\n          this.editForm.get('portfolioId')?.setValidators(Validators.required);\r\n          this.editForm.get('ftpUrl')?.clearValidators();\r\n        } else if (providerId === 6) {\r\n          this.editForm.get('ftpUrl')?.setValidators(Validators.required);\r\n          this.editForm.get('portfolioId')?.clearValidators();\r\n        } else {\r\n          this.editForm.get('portfolioId')?.clearValidators();\r\n          this.editForm.get('ftpUrl')?.clearValidators();\r\n        }\r\n\r\n        this.editForm.get('portfolioId')?.updateValueAndValidity();\r\n        this.editForm.get('ftpUrl')?.updateValueAndValidity();\r\n      }\r\n\r\n      // Save edited provider\r\n      saveEditedProvider() {\r\n        if (!this.editForm || !this.editingProvider || this.editForm.invalid) {\r\n          this.editForm?.markAllAsTouched();\r\n          return;\r\n        }\r\n\r\n        const formValue = this.editForm.value;\r\n        const configuration = {\r\n          Username: formValue.username,\r\n          Password: formValue.password,\r\n          PortfolioId: formValue.portfolioId,\r\n          FtpUrl: formValue.ftpUrl,\r\n          Stations: formValue.stations\r\n        };\r\n\r\n        this.providersService.updateUserProvider(\r\n          parseInt(this.editingProvider.providerId),\r\n          JSON.stringify(configuration)\r\n        ).then(() => {\r\n          this.messageService.add({\r\n            severity: 'success',\r\n            summary: 'Success',\r\n            detail: 'Provider updated successfully'\r\n          });\r\n          this.cancelEdit();\r\n          this.getUserProviders(); // Refresh the list\r\n        }).catch(error => {\r\n          console.error('Error updating provider:', error);\r\n          this.messageService.add({\r\n            severity: 'error',\r\n            summary: 'Error',\r\n            detail: 'Failed to update provider'\r\n          });\r\n        });\r\n      }\r\n\r\n      // Cancel edit\r\n      cancelEdit() {\r\n        this.editingProvider = null;\r\n        this.editForm = null;\r\n      }\r\n\r\n      // Remove provider functionality\r\n      removeUserProvider(index: number) {\r\n        const provider = this.userProviders[index];\r\n\r\n        if (confirm(`Are you sure you want to remove the ${this.getSelectedProviderName(parseInt(provider.providerId))} provider?`)) {\r\n          this.providersService.deleteUserProvider(parseInt(provider.providerId))\r\n            .then(() => {\r\n              this.messageService.add({\r\n                severity: 'success',\r\n                summary: 'Success',\r\n                detail: 'Provider removed successfully'\r\n              });\r\n              this.getUserProviders(); // Refresh the list\r\n            })\r\n            .catch(error => {\r\n              console.error('Error removing provider:', error);\r\n              this.messageService.add({\r\n                severity: 'error',\r\n                summary: 'Error',\r\n                detail: 'Failed to remove provider'\r\n              });\r\n            });\r\n        }\r\n      }\r\n    \r\n}\r\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAA4CC,UAAU,QAAQ,gBAAgB;AAiBvE,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAU3BC,YAAmBC,aAA4B,EACnCC,eAAgC,EAChCC,gBAAkC,EAClCC,cAA8B,EAC9BC,EAAe,EACfC,YAA0B;IALnB,KAAAL,aAAa,GAAbA,aAAa;IACpB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,gBAAgB,GAAhBA,gBAAgB;IAChB,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,YAAY,GAAZA,YAAY;IAbxB,KAAAC,kBAAkB,GAAe,EAAE;IACnC,KAAAC,aAAa,GAAgC,EAAE;IAE/C,KAAAC,QAAQ,GAAa,EAAE;IACvB,KAAAC,cAAc,GAAG,KAAK;IACtB,KAAAC,eAAe,GAAsC,IAAI;IACzD,KAAAC,QAAQ,GAAqB,IAAI;IAS7B,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MACrCC,SAAS,EAAE,IAAI,CAACV,EAAE,CAACW,KAAK,CAAC,EAAE;KAC5B,CAAC;EAEN;EAEAC,QAAQA,CAAA;IACJ,IAAI,CAACd,gBAAgB,CAACe,YAAY,EAAE,CAACC,IAAI,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACb,kBAAkB,GAAGa,IAAI;IAChC,CAAC,CAAC;IAEF,IAAI,CAACC,gBAAgB,EAAE;IAEvB,IAAI,CAACC,WAAW,EAAE,CAAC,CAAC;EAC1B;;EAGAA,WAAWA,CAAA;IACP,MAAMC,aAAa,GAAG,IAAI,CAAClB,EAAE,CAACS,KAAK,CAAC;MAClCU,UAAU,EAAE,CAAC,EAAE,EAAE1B,UAAU,CAAC2B,QAAQ,CAAC;MACrCC,QAAQ,EAAE,CAAC,EAAE,EAAE5B,UAAU,CAAC2B,QAAQ,CAAC;MACnCE,QAAQ,EAAE,CAAC,EAAE,EAAE7B,UAAU,CAAC2B,QAAQ,CAAC;MACnCG,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,MAAM,EAAE,CAAC,EAAE,CAAC;MACZpB,QAAQ,EAAE,CAAC,EAAE,CAAC;MACdqB,eAAe,EAAC,CAAC,EAAE;KACpB,CAAC;IACF,IAAI,CAACf,SAAS,CAACgB,IAAI,CAACR,aAAa,CAAC;IAElC;IACAA,aAAa,CAACS,GAAG,CAAC,YAAY,CAAC,EAAEC,YAAY,CAACC,SAAS,CAAEC,KAAK,IAAI;MAChE,MAAMC,kBAAkB,GAAGb,aAAa,CAACS,GAAG,CAAC,aAAa,CAAC;MAE3D,IAAIG,KAAK,KAAK,GAAG,EAAE;QACjBC,kBAAkB,EAAEC,aAAa,CAACvC,UAAU,CAAC2B,QAAQ,CAAC;OACvD,MAAM;QACLW,kBAAkB,EAAEE,eAAe,EAAE;;MAGvCF,kBAAkB,EAAEG,sBAAsB,EAAE;IAC9C,CAAC,CAAC;EACJ;EAEA,IAAIxB,SAASA,CAAA;IACX,OAAO,IAAI,CAACF,iBAAiB,CAACmB,GAAG,CAAC,WAAW,CAAc;EAC7D;EAEAQ,uBAAuBA,CAACC,EAAS;IAC/B,OAAO,IAAI,CAAClC,kBAAkB,CAACmC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACF,EAAE,IAAIA,EAAE,CAAC,CAACG,IAAI;EAC3D;EAMAC,cAAcA,CAACC,KAAa;IAC1B,IAAI,CAAC/B,SAAS,CAACgC,QAAQ,CAACD,KAAK,CAAC;EAChC;EAEAE,WAAWA,CAACF,KAAa;IACvB,MAAMtB,UAAU,GAAG,IAAI,CAACT,SAAS,CAACkC,EAAE,CAACH,KAAK,CAAC,CAACd,GAAG,CAAC,YAAY,CAAC,EAAEG,KAAK;IAEpE,IAAI,CAACX,UAAU,EAAE;IAEjB,IAAI0B,OAAO,GAAuB;MAChC1B,UAAU,EAAG,IAAI,CAACT,SAAS,CAACkC,EAAE,CAACH,KAAK,CAAC,CAACd,GAAG,CAAC,YAAY,CAAC,EAAEG,KAAK;MAC9DT,QAAQ,EAAE,IAAI,CAACX,SAAS,CAACkC,EAAE,CAACH,KAAK,CAAC,CAACd,GAAG,CAAC,UAAU,CAAC,EAAEG,KAAK;MACzDR,QAAQ,EAAE,IAAI,CAACZ,SAAS,CAACkC,EAAE,CAACH,KAAK,CAAC,CAACd,GAAG,CAAC,UAAU,CAAC,EAAEG,KAAK;MACzDP,WAAW,EAAE,IAAI,CAACb,SAAS,CAACkC,EAAE,CAACH,KAAK,CAAC,CAACd,GAAG,CAAC,aAAa,CAAC,EAAEG,KAAK;MAC/DN,MAAM,EAAE,IAAI,CAACd,SAAS,CAACkC,EAAE,CAACH,KAAK,CAAC,CAACd,GAAG,CAAC,QAAQ,CAAC,EAAEG;KACjD;IACDgB,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEF,OAAO,CAAC;IAElC,IAAI,CAAChD,eAAe,CAAC8C,WAAW,CAACE,OAAO,CAAC,CAAC/B,IAAI,CAACC,IAAI,IAAG;MACpD,IAAI,CAACL,SAAS,CAACkC,EAAE,CAACH,KAAK,CAAC,CAACO,UAAU,CAAC;QAAE5C,QAAQ,EAAEW;MAAI,CAAE,CAAC;IACzD,CAAC,CAAC;IAEF,MAAMG,aAAa,GAAG,IAAI,CAACR,SAAS,CAACkC,EAAE,CAACH,KAAK,CAAC;IAE9C;IACA,IAAItB,UAAU,KAAK,CAAC,EAAE;MACpB;MACAD,aAAa,CAACS,GAAG,CAAC,aAAa,CAAC,EAAEK,aAAa,CAACvC,UAAU,CAAC2B,QAAQ,CAAC;MACpEF,aAAa,CAACS,GAAG,CAAC,QAAQ,CAAC,EAAEM,eAAe,EAAE;MAC9Cf,aAAa,CAACS,GAAG,CAAC,QAAQ,CAAC,EAAEsB,QAAQ,CAAC,EAAE,CAAC;KAC1C,MAAM,IAAI9B,UAAU,KAAK,CAAC,EAAE;MAC3B;MACAD,aAAa,CAACS,GAAG,CAAC,QAAQ,CAAC,EAAEK,aAAa,CAACvC,UAAU,CAAC2B,QAAQ,CAAC;MAC/DF,aAAa,CAACS,GAAG,CAAC,aAAa,CAAC,EAAEM,eAAe,EAAE;MACnDf,aAAa,CAACS,GAAG,CAAC,aAAa,CAAC,EAAEsB,QAAQ,CAAC,EAAE,CAAC;KAC/C,MAAM;MACL;MACA/B,aAAa,CAACS,GAAG,CAAC,aAAa,CAAC,EAAEM,eAAe,EAAE;MACnDf,aAAa,CAACS,GAAG,CAAC,aAAa,CAAC,EAAEsB,QAAQ,CAAC,EAAE,CAAC;MAC9C/B,aAAa,CAACS,GAAG,CAAC,QAAQ,CAAC,EAAEM,eAAe,EAAE;MAC9Cf,aAAa,CAACS,GAAG,CAAC,QAAQ,CAAC,EAAEsB,QAAQ,CAAC,EAAE,CAAC;;IAG3C/B,aAAa,CAACS,GAAG,CAAC,aAAa,CAAC,EAAEO,sBAAsB,EAAE;IAC1DhB,aAAa,CAACS,GAAG,CAAC,QAAQ,CAAC,EAAEO,sBAAsB,EAAE;EAEvD;EAEAgB,QAAQA,CAAA;IACN,IAAI,IAAI,CAAC1C,iBAAiB,CAAC2C,OAAO,EAAE;MAClC,IAAI,CAAC3C,iBAAiB,CAAC4C,gBAAgB,EAAE;MACzC;;IAEF,IAAI,IAAI,CAAC5C,iBAAiB,CAAC6C,KAAK,EAAE;MAChCP,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE,IAAI,CAACvC,iBAAiB,CAACsB,KAAK,CAACpB,SAAS,CAAC;MAEvE;MACA,MAAM4C,oBAAoB,GAAG,IAAI,CAAC9C,iBAAiB,CAACsB,KAAK,CAACpB,SAAS,CAAC6C,GAAG,CAAEC,YAAiB,KAAM;QAC9F,GAAGA,YAAY;QACfrC,UAAU,EAAEqC,YAAY,CAACrC,UAAU,CAACsC,QAAQ,EAAE,CAAC;OAChD,CAAC,CAAC;;MAEHX,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEO,oBAAoB,CAAC;MAE7D,IAAIT,OAAO,GAA6B;QACtCnC,SAAS,EAAE4C;OACZ;MAED,IAAI,CAACxD,gBAAgB,CAAC4D,iBAAiB,CAACb,OAAO,CAAC,CAAC/B,IAAI,CAACC,IAAI,IAAG;QAC3D+B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEhC,IAAI,CAAC;QACnC,IAAI,CAACC,gBAAgB,EAAE;MACzB,CAAC,CAAC,CAAC2C,KAAK,CAACC,KAAK,IAAG;QACfd,OAAO,CAACc,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACjD,CAAC,CAAC;;EAEN;EAEAC,cAAcA,CAACpB,KAAY;IACzB,OAAO,IAAI,CAAC/B,SAAS,IAAI,IAAI,CAACA,SAAS,CAACoD,MAAM,GAAGrB,KAAK,IAAI,IAAI,CAAC/B,SAAS,CAACkC,EAAE,CAACH,KAAK,CAAC,CAACd,GAAG,CAAC,YAAY,CAAC,EAAEG,KAAK,KAAK,CAAC;EACnH;EAEAd,gBAAgBA,CAAA;IACd,IAAI,CAAClB,gBAAgB,CAACkB,gBAAgB,EAAE,CAACF,IAAI,CAACC,IAAI,IAAG;MACnD,IAAI,CAACZ,aAAa,GAAGY,IAAI;MACzB+B,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC5C,aAAa,CAAC;MAC/B,IAAI,CAACA,aAAa,CAAC4D,OAAO,CAACC,EAAE,IAAG;QAC9BA,EAAE,CAACC,aAAa,GAAGC,IAAI,CAACC,KAAK,CAACH,EAAE,CAACC,aAAa,CAAC;MAEjD,CAAC,CAAC;MACF,IAAIlD,IAAI,CAAC+C,MAAM,GAAG,CAAC,EAAC;QAClB,IAAI,CAACjE,eAAe,CAACuE,eAAe,EAAE,CAACtD,IAAI,CAACC,IAAI,IAAG;UACjD,IAAI,CAACX,QAAQ,GAAGW,IAAI;UACpB,IAAI,CAACd,YAAY,CAACoE,WAAW,CAAC,IAAI,CAACjE,QAAQ,CAAC;QAC9C,CAAC,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEA;EACAkE,YAAYA,CAAC7B,KAAa;IACxB,MAAM8B,QAAQ,GAAG,IAAI,CAACpE,aAAa,CAACsC,KAAK,CAAC;IAC1C,IAAI,CAACnC,eAAe,GAAGiE,QAAQ;IAE/B;IACA,IAAI,CAAChE,QAAQ,GAAG,IAAI,CAACP,EAAE,CAACS,KAAK,CAAC;MAC5BU,UAAU,EAAE,CAACoD,QAAQ,CAACpD,UAAU,EAAE1B,UAAU,CAAC2B,QAAQ,CAAC;MACtDC,QAAQ,EAAE,CAACkD,QAAQ,CAACN,aAAa,CAACO,QAAQ,EAAE/E,UAAU,CAAC2B,QAAQ,CAAC;MAChEE,QAAQ,EAAE,CAAC,EAAE,EAAE7B,UAAU,CAAC2B,QAAQ,CAAC;MACnCG,WAAW,EAAE,CAACgD,QAAQ,CAACN,aAAa,CAACQ,WAAW,IAAI,EAAE,CAAC;MACvDjD,MAAM,EAAE,CAAC+C,QAAQ,CAACN,aAAa,CAACS,MAAM,IAAI,EAAE,CAAC;MAC7CtE,QAAQ,EAAE,CAACmE,QAAQ,CAACN,aAAa,CAACU,QAAQ,IAAI,EAAE;KACjD,CAAC;IAEF;IACA,MAAMxD,UAAU,GAAGyD,QAAQ,CAACL,QAAQ,CAACpD,UAAU,CAAC;IAChD,IAAIA,UAAU,KAAK,CAAC,EAAE;MACpB,IAAI,CAACZ,QAAQ,CAACoB,GAAG,CAAC,aAAa,CAAC,EAAEK,aAAa,CAACvC,UAAU,CAAC2B,QAAQ,CAAC;MACpE,IAAI,CAACb,QAAQ,CAACoB,GAAG,CAAC,QAAQ,CAAC,EAAEM,eAAe,EAAE;KAC/C,MAAM,IAAId,UAAU,KAAK,CAAC,EAAE;MAC3B,IAAI,CAACZ,QAAQ,CAACoB,GAAG,CAAC,QAAQ,CAAC,EAAEK,aAAa,CAACvC,UAAU,CAAC2B,QAAQ,CAAC;MAC/D,IAAI,CAACb,QAAQ,CAACoB,GAAG,CAAC,aAAa,CAAC,EAAEM,eAAe,EAAE;KACpD,MAAM;MACL,IAAI,CAAC1B,QAAQ,CAACoB,GAAG,CAAC,aAAa,CAAC,EAAEM,eAAe,EAAE;MACnD,IAAI,CAAC1B,QAAQ,CAACoB,GAAG,CAAC,QAAQ,CAAC,EAAEM,eAAe,EAAE;;IAGhD,IAAI,CAAC1B,QAAQ,CAACoB,GAAG,CAAC,aAAa,CAAC,EAAEO,sBAAsB,EAAE;IAC1D,IAAI,CAAC3B,QAAQ,CAACoB,GAAG,CAAC,QAAQ,CAAC,EAAEO,sBAAsB,EAAE;EACvD;EAEA;EACA2C,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACtE,QAAQ,IAAI,CAAC,IAAI,CAACD,eAAe,IAAI,IAAI,CAACC,QAAQ,CAAC4C,OAAO,EAAE;MACpE,IAAI,CAAC5C,QAAQ,EAAE6C,gBAAgB,EAAE;MACjC;;IAGF,MAAM0B,SAAS,GAAG,IAAI,CAACvE,QAAQ,CAACuB,KAAK;IACrC,MAAMmC,aAAa,GAAG;MACpBO,QAAQ,EAAEM,SAAS,CAACzD,QAAQ;MAC5B0D,QAAQ,EAAED,SAAS,CAACxD,QAAQ;MAC5BmD,WAAW,EAAEK,SAAS,CAACvD,WAAW;MAClCmD,MAAM,EAAEI,SAAS,CAACtD,MAAM;MACxBmD,QAAQ,EAAEG,SAAS,CAAC1E;KACrB;IAED,IAAI,CAACN,gBAAgB,CAACkF,kBAAkB,CACtCJ,QAAQ,CAAC,IAAI,CAACtE,eAAe,CAACa,UAAU,CAAC,EACzC+C,IAAI,CAACe,SAAS,CAAChB,aAAa,CAAC,CAC9B,CAACnD,IAAI,CAAC,MAAK;MACV,IAAI,CAACf,cAAc,CAACmF,GAAG,CAAC;QACtBC,QAAQ,EAAE,SAAS;QACnBC,OAAO,EAAE,SAAS;QAClBC,MAAM,EAAE;OACT,CAAC;MACF,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACtE,gBAAgB,EAAE,CAAC,CAAC;IAC3B,CAAC,CAAC,CAAC2C,KAAK,CAACC,KAAK,IAAG;MACfd,OAAO,CAACc,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,IAAI,CAAC7D,cAAc,CAACmF,GAAG,CAAC;QACtBC,QAAQ,EAAE,OAAO;QACjBC,OAAO,EAAE,OAAO;QAChBC,MAAM,EAAE;OACT,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACAC,UAAUA,CAAA;IACR,IAAI,CAAChF,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,QAAQ,GAAG,IAAI;EACtB;EAEA;EACAgF,kBAAkBA,CAAC9C,KAAa;IAC9B,MAAM8B,QAAQ,GAAG,IAAI,CAACpE,aAAa,CAACsC,KAAK,CAAC;IAE1C,IAAI+C,OAAO,CAAC,uCAAuC,IAAI,CAACrD,uBAAuB,CAACyC,QAAQ,CAACL,QAAQ,CAACpD,UAAU,CAAC,CAAC,YAAY,CAAC,EAAE;MAC3H,IAAI,CAACrB,gBAAgB,CAAC2F,kBAAkB,CAACb,QAAQ,CAACL,QAAQ,CAACpD,UAAU,CAAC,CAAC,CACpEL,IAAI,CAAC,MAAK;QACT,IAAI,CAACf,cAAc,CAACmF,GAAG,CAAC;UACtBC,QAAQ,EAAE,SAAS;UACnBC,OAAO,EAAE,SAAS;UAClBC,MAAM,EAAE;SACT,CAAC;QACF,IAAI,CAACrE,gBAAgB,EAAE,CAAC,CAAC;MAC3B,CAAC,CAAC,CACD2C,KAAK,CAACC,KAAK,IAAG;QACbd,OAAO,CAACc,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,IAAI,CAAC7D,cAAc,CAACmF,GAAG,CAAC;UACtBC,QAAQ,EAAE,OAAO;UACjBC,OAAO,EAAE,OAAO;UAChBC,MAAM,EAAE;SACT,CAAC;MACJ,CAAC,CAAC;;EAER;CAEL;AA9QY3F,kBAAkB,GAAAgG,UAAA,EAJ9BlG,SAAS,CAAC;EACPmG,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE;CAChB,CAAC,C,EACWlG,kBAAkB,CA8Q9B"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}